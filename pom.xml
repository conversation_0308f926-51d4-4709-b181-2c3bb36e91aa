<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.teyuntong</groupId>
        <artifactId>tyt-dependencies-spring-cloud</artifactId>
        <version>2021.0.9.0-********-1.1.0</version>
    </parent>

    <artifactId>tyt-goods-search</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>tyt-goods-search</name>
    <description>找货服务</description>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <tyt-goods-search.version>1.0.0-SNAPSHOT</tyt-goods-search.version>
        <tyt-goods-service-client.version>1.0.4-SNAPSHOT</tyt-goods-service-client.version>
        <tyt-infra-basic-resource.version>1.0.0-SNAPSHOT</tyt-infra-basic-resource.version>
        <tyt-infra-common-bom.version>2.1.4-SNAPSHOT</tyt-infra-common-bom.version>
        <dynamic-datasource-spring-boot-starter.version>3.5.0</dynamic-datasource-spring-boot-starter.version>
        <tyt-user-service-client.version>1.3.0-SNAPSHOT</tyt-user-service-client.version>
        <elasticsearch.version>7.14.0</elasticsearch.version>
        <easy-elasticsearch.version>2.0.0-beta4</easy-elasticsearch.version>
        <spock.version>2.4-M4-groovy-4.0</spock.version>
        <junit-platform-engine.version>1.10.2</junit-platform-engine.version>
        <tyt-trade-service-client.version>1.0.5-SNAPSHOT</tyt-trade-service-client.version>
        <tyt-inner-export-service-client.version>1.1.1-SNAPSHOT</tyt-inner-export-service-client.version>
        <pagehelper-spring-boot-starter.version>1.4.7</pagehelper-spring-boot-starter.version>
        <tyt-market-activity-client.version>1.0-SNAPSHOT</tyt-market-activity-client.version>
        <!-- ==================== sonar ==================== -->
        <tyt-p3c-pmd.version>1.0.0</tyt-p3c-pmd.version>
        <sonar.projectKey>${project.artifactId}</sonar.projectKey>
        <sonar.projectName>${project.artifactId}</sonar.projectName>
        <sonar.moduleKey>${project.artifactId}</sonar.moduleKey>
        <sonar.projectVersion>1.0</sonar.projectVersion>
        <sonar.inclusions>**/com/teyuntong/**/*.java,**/com/teyuntong/**/*.kt</sonar.inclusions>
        <sonar.java.binaries>target</sonar.java.binaries>
        <sonar.host.url>http://************:9000</sonar.host.url>
        <sonar.login>****************************************</sonar.login>

        <!-- JaCoCo Configuration for Sonar -->
        <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
        <sonar.coverage.jacoco.xmlReportPaths>${project.basedir}/target/site/jacoco/jacoco.xml,${project.basedir}/target/site/jacoco-aggregate/jacoco.xml</sonar.coverage.jacoco.xmlReportPaths>
        <sonar.language>java</sonar.language>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <version>${spock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-engine</artifactId>
            <version>${junit-platform-engine.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-infra-common-bom</artifactId>
                <version>${tyt-infra-common-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.easy-es</groupId>
                <artifactId>easy-es-boot-starter</artifactId>
                <version>${easy-elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-goods-service-client</artifactId>
                <version>${tyt-goods-service-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-goods-search-service</artifactId>
                <version>${tyt-goods-search.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-goods-search-client</artifactId>
                <version>${tyt-goods-search.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-infra-basic-resource-client</artifactId>
                <version>${tyt-infra-basic-resource.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-user-service-client</artifactId>
                <version>${tyt-user-service-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-trade-service-client</artifactId>
                <version>${tyt-trade-service-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-inner-export-service-client</artifactId>
                <version>${tyt-inner-export-service-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.teyuntong</groupId>
                <artifactId>tyt-market-activity-client</artifactId>
                <version>${tyt-market-activity-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper-spring-boot-starter.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <!-- 脚手架插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
            </plugin>

            <!-- sonar插件 -->
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
            </plugin>

            <!-- JaCoCo plugin for code coverage -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.10</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <modules>
        <module>tyt-goods-search-adapter</module>
        <module>tyt-goods-search-client</module>
        <module>tyt-goods-search-service</module>
        <module>tyt-goods-search-schedule</module>
        <module>tyt-goods-search-mq</module>
        <module>tyt-goods-search-starter</module>
        <module>tyt-goods-search-mybatis-generator</module>
    </modules>
</project>
