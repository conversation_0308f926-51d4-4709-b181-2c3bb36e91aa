package com.teyuntong.goods.search.adapter.groupchat;

import com.teyuntong.goods.search.service.biz.groupchat.service.GroupChatService;
import com.teyuntong.goods.search.service.biz.groupchat.vo.AppletEntranceVO;
import com.teyuntong.goods.search.service.biz.groupchat.vo.FindCityGroupQRDTO;
import com.teyuntong.goods.search.service.biz.groupchat.vo.FindCityGroupQRVO;
import com.teyuntong.goods.search.service.biz.route.vo.MainSwitchVo;
import com.teyuntong.infra.common.definition.bean.WebResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import static com.teyuntong.goods.search.service.common.error.GoodsSearchErrorCode.ARGUMENTS_IS_ERROR;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/groupChat")
public class GroupChatController {

    private final GroupChatService groupChatService;

    /**
     * 获取跳转到小程序的参数
     */
    @GetMapping("/getAppletEntrance")
    public WebResult<AppletEntranceVO> getAppletEntrance() {
        return WebResult.success(groupChatService.getAppletEntrance());
    }

    /**
     * 获取群聊二维码
     */
    @PostMapping("/findCityGroupQR")
    public WebResult<FindCityGroupQRVO> findCityGroupQR(@RequestBody FindCityGroupQRDTO findCityGroupQRDTO) {
        if (findCityGroupQRDTO == null || StringUtils.isBlank(findCityGroupQRDTO.getCity())
                || StringUtils.isBlank(findCityGroupQRDTO.getProvince()) || findCityGroupQRDTO.getUserId() == null) {
            return WebResult.error(ARGUMENTS_IS_ERROR);
        }
        //把省份和城市拼在一起，要求必须是XX省XX市
        String provinceCity = findCityGroupQRDTO.getProvince() + findCityGroupQRDTO.getCity();

        //城市单独拿出来
        String city = findCityGroupQRDTO.getCity().replace("市", "");

        return WebResult.success(groupChatService.findCityGroupQR(provinceCity, city, findCityGroupQRDTO.getUserId()));
    }

    /**
     * 上报申请建群
     */
    @PostMapping("/reportedCity")
    public WebResult<MainSwitchVo> reportedCity(@RequestBody FindCityGroupQRDTO findCityGroupQRDTO) {
        if (findCityGroupQRDTO == null || StringUtils.isBlank(findCityGroupQRDTO.getCity())
                || StringUtils.isBlank(findCityGroupQRDTO.getProvince()) || findCityGroupQRDTO.getUserId() == null) {
            return WebResult.error(ARGUMENTS_IS_ERROR);
        }
        //把省份和城市拼在一起，要求必须是XX省XX市
        String provinceCity = findCityGroupQRDTO.getProvince() + findCityGroupQRDTO.getCity();
        groupChatService.reportedCity(provinceCity, findCityGroupQRDTO.getUserId());
        return WebResult.success();
    }

}
