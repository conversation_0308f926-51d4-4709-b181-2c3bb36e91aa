package com.teyuntong.goods.search.adapter.certification;

import com.teyuntong.goods.search.service.rpc.certification.service.CertificationService;
import com.teyuntong.goods.search.service.rpc.certification.vo.CertIllegalInfoVo;
import com.teyuntong.infra.common.definition.bean.WebResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 校验证件是否合法
 *
 * <AUTHOR>
 * @since 2024/08/10 11:20
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/certification")
public class CertificationController {

    private final CertificationService certificationService;

    /**
     * 校验司机和车辆是否合法
     */
    @GetMapping("/verifyCarAndDriver")
    public WebResult<CertIllegalInfoVo> verifyCarAndDriver(@RequestParam("type") Integer type) {
        return WebResult.success(certificationService.verifyCarAndDriver(type));
    }
}
