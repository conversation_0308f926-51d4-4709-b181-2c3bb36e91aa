package com.teyuntong.goods.search.adapter.exposure;

import com.teyuntong.goods.search.service.biz.exposure.vo.ExposureDataVO;
import com.teyuntong.goods.search.service.biz.exposure.vo.ExposureVO;
import com.teyuntong.goods.search.service.biz.goods.dto.TransportExposureQueryDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.TransportPreferenceDTO;
import com.teyuntong.goods.search.service.rpc.exposure.service.ExposureRpcService;
import com.teyuntong.infra.common.definition.bean.WebResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 急走专区曝光接口
 *
 * <AUTHOR>
 * @since 2024/07/16 09:53
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/exposure")
public class ExposureController {

    public final ExposureRpcService exposureRpcService;

    /**
     * 急走专区轮播开关
     */
    @GetMapping("/saveSwitch")
    public WebResult<ExposureDataVO> saveSwitch(
            @RequestParam(value = "switchStatus", required = false, defaultValue = "0") Integer switchStatus) {
        exposureRpcService.saveSwitch(switchStatus);
        return WebResult.success();
    }

    /**
     * 急走专区轮播接口
     */
    @PostMapping("/getHurryBannerList")
    public WebResult<ExposureDataVO> getHurryBannerList(@RequestBody TransportExposureQueryDTO searchDTO) {
        return WebResult.success(exposureRpcService.getHurryBannerList(searchDTO));
    }

    /**
     * 急走专区列表
     */
    @PostMapping("/getHurryList")
    public WebResult<List<ExposureVO>> getHurryList(@RequestBody TransportExposureQueryDTO searchDTO) {
        return WebResult.success(exposureRpcService.getHurryList(searchDTO));
    }

    /**
     * 货源点赞/取消接口
     */
    @PostMapping("/savePreference")
    public WebResult<Void> savePreference(@RequestBody @Valid TransportPreferenceDTO preferenceDTO) {
        exposureRpcService.savePreference(preferenceDTO);
        return WebResult.success();
    }
}
