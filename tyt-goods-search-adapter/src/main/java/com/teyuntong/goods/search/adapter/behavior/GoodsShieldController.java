package com.teyuntong.goods.search.adapter.behavior;

import com.teyuntong.goods.search.service.biz.behavior.dto.ShieldingShipperDTO;
import com.teyuntong.goods.search.service.biz.behavior.dto.TransportShieldDTO;
import com.teyuntong.goods.search.service.biz.behavior.vo.ShipperUserVO;
import com.teyuntong.goods.search.service.rpc.behavior.TransportShieldRpcService;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 货源屏蔽接口
 *
 * <AUTHOR>
 * @since 2024/12/14 17:58
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/goods/shield")
@Slf4j
public class GoodsShieldController {

    private final TransportShieldRpcService transportShieldRpcService;

    /**
     * 屏蔽货源接口
     */
    @PostMapping("/save")
    public WebResult save(@Validated @RequestBody TransportShieldDTO shieldDTO) {
        shieldDTO.setUserId(LoginHelper.getRequiredLoginUser().getUserId());
        transportShieldRpcService.saveShieldGoodsAndReportComplaint(shieldDTO);
        return WebResult.success();
    }

    /**
     * 返回当前用户的屏蔽货源id
     */
    @GetMapping("/getShieldSrcMsgIds")
    public WebResult<List<Long>> getShieldSrcMsgIds() {
        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        return WebResult.success(transportShieldRpcService.getShieldSrcMsgIds(userId));
    }

    /**
     * 屏蔽货主接口
     */
    @PostMapping("/shipper")
    public WebResult shipper(@Validated @RequestBody ShieldingShipperDTO shieldDTO) {
        shieldDTO.setUserId(LoginHelper.getRequiredLoginUser().getUserId());
        transportShieldRpcService.saveShieldShipper(shieldDTO);
        return WebResult.success();
    }

    /**
     * 返回当前用户屏蔽货主
     */
    @GetMapping("/getShieldShippers")
    public WebResult<List<ShipperUserVO>> getShieldShippers() {
        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        return WebResult.success(transportShieldRpcService.getShieldShippers(userId));
    }

}