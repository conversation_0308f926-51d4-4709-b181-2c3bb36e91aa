package com.teyuntong.goods.search.adapter.route;

import com.teyuntong.goods.search.service.biz.route.service.OftenRouteService;
import com.teyuntong.goods.search.service.biz.route.vo.MainSwitchVo;
import com.teyuntong.goods.search.service.biz.route.vo.OftenRouteEnableVo;
import com.teyuntong.goods.search.service.biz.route.vo.UserOftenRouteVo;
import com.teyuntong.infra.common.definition.bean.WebResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 常跑路线配置类
 *
 * <AUTHOR>
 * @since 2024/10/28 13:05
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/oftenRoute")
public class OftenRouteController {

    private final OftenRouteService oftenRouteService;

    /**
     * 播报总开关设置
     */
    @PostMapping("/saveMainSwitch")
    public WebResult<Void> saveMainSwitch(@Validated @RequestBody MainSwitchVo mainSwitchVo) {
        oftenRouteService.saveMainSwitch(mainSwitchVo);
        return WebResult.success();
    }

    /**
     * 获取播报总开关设置
     */
    @PostMapping("/getMainSwitch")
    public WebResult<MainSwitchVo> getMainSwitch() {
        MainSwitchVo mainSwitchVo = oftenRouteService.getMainSwitch();
        return WebResult.success(mainSwitchVo);
    }

    /**
     * 保存路线
     */
    @PostMapping("/saveOftenRoute")
    public WebResult<Void> saveOftenRoute(@RequestBody UserOftenRouteVo userOftenRouteVo) {
        oftenRouteService.saveOftenRoute(userOftenRouteVo);
        return WebResult.success();
    }

    /**
     * 设置路线播报开关
     *
     * @param routeUserId  用户路线id
     * @param reportStatus 开关（0关；1开）
     */
    @PostMapping("/setRouteSwitch")
    public WebResult<Void> setRouteSwitch(Long routeUserId, Integer reportStatus) {
        oftenRouteService.setRouteSwitch(routeUserId, reportStatus);
        return WebResult.success();
    }

    /**
     * 设置路线启用开关
     *
     * @param routeUserIds 路线用户ids，多个用逗号隔开
     */
    @PostMapping("/setRouteEnable")
    public WebResult<Void> setRouteEnable(String routeUserIds, @RequestBody OftenRouteEnableVo enableVo) {
        log.info("设置路线启用开关，routeUserIds:{}, enableVo:{}", routeUserIds, enableVo.getRouteUserIds());
        oftenRouteService.setRouteEnable(StringUtils.isNotBlank(routeUserIds) ? routeUserIds : enableVo.getRouteUserIds(), 1);
        return WebResult.success();
    }

    /**
     * 删除路线
     *
     * @param routeUserId 用户路线id
     */
    @PostMapping("/deleteRoute")
    public WebResult<Void> deleteRoute(Long routeUserId) {
        oftenRouteService.deleteRoute(routeUserId);
        return WebResult.success();
    }

    /**
     * 查看路线详情
     *
     * @param routeUserId 用户路线id
     */
    @PostMapping("/viewRoute")
    public WebResult<UserOftenRouteVo> viewRoute(Long routeUserId) {
        UserOftenRouteVo routeVo = oftenRouteService.viewRoute(routeUserId);
        return WebResult.success(routeVo);
    }

    /**
     * 路线列表
     */
    @PostMapping("/getUserRouteList")
    public WebResult<List<UserOftenRouteVo>> getUserRouteList() {
        List<UserOftenRouteVo> routeList = oftenRouteService.getUserRouteList();
        return WebResult.success(routeList);
    }
}
