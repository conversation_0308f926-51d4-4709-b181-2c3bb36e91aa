package com.teyuntong.goods.search.adapter.route;

import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.goods.search.service.biz.route.constant.RouteConstant;
import com.teyuntong.goods.search.service.biz.route.dto.OftenRouteSearchDTO;
import com.teyuntong.goods.search.service.biz.route.service.RouteDataService;
import com.teyuntong.goods.search.service.biz.route.vo.OftenRouteBubbleVO;
import com.teyuntong.goods.search.service.biz.route.vo.OftenRouteSearchVO;
import com.teyuntong.goods.search.service.biz.route.vo.RouteTransportCountVo;
import com.teyuntong.goods.search.service.biz.route.vo.RouteTransportQuery;
import com.teyuntong.goods.search.service.common.error.GoodsSearchErrorCode;
import com.teyuntong.goods.search.service.remote.basic.ConfigRemoteService;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 常跑路线数据接口类
 *
 * <AUTHOR>
 * @since 2024/10/28 13:05
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/routeData")
public class RouteDataController {

    private final RouteDataService routeDataService;
    private final ConfigRemoteService configRemoteService;

    /**
     * 播报记录列表
     *
     * @param transportIds 货源id集合，逗号,拼接
     */
    @PostMapping("/getTransportByIds")
    public WebResult<List<TransportVO>> getTransportByIds(String transportIds) {
        if (StringUtils.isEmpty(transportIds)) {
            return WebResult.success(List.of());
        }

        List<Long> transportIdList = splitStrParam(transportIds);
        if (transportIdList.size() > 160) {
            throw new BusinessException(GoodsSearchErrorCode.PAGE_SIZE_EXCEED);
        }

        List<TransportVO> transportList = routeDataService.getTransportByIds(transportIdList);
        return WebResult.success(transportList);
    }

    /**
     * 获取路线货源列表
     */
    @PostMapping("/getRouteTransportList")
    public WebResult<List<TransportVO>> getRouteTransportList(@Valid @RequestBody RouteTransportQuery transportQuery) {
        List<TransportVO> transportList = routeDataService.getRouteTransportList(routeDataService, transportQuery);
        return WebResult.success(transportList);
    }

    /**
     * 获取路线货源条数
     *
     * @param routeIdStr 路线id集合，逗号,拼接
     */
    @PostMapping("/getRouteTransportCount")
    public WebResult<List<RouteTransportCountVo>> getRouteTransportCount(String routeIdStr) {
        if (StringUtils.isEmpty(routeIdStr)) {
            return WebResult.success(List.of());
        }

        List<Long> routeIdList = splitStrParam(routeIdStr);
        if (routeIdList.size() > configRemoteService.getIntValue(RouteConstant.OFTEN_ROUTE_MAX, 5)) {
            throw new BusinessException(GoodsSearchErrorCode.ROUTE_COUNT_EXCEED);
        }

        List<RouteTransportCountVo> countList = routeDataService.getRouteTransportCount(routeIdList);
        return WebResult.success(countList);
    }

    private List<Long> splitStrParam(String strParam) {
        if (StringUtils.isBlank(strParam)) {
            throw new BusinessException(GoodsSearchErrorCode.ARGUMENTS_IS_NULL);
        }
        List<Long> idList;
        try {
            idList = Arrays.stream(strParam.split(",")).map(Long::parseLong).collect(Collectors.toList());
        } catch (Exception e) {
            throw new BusinessException(GoodsSearchErrorCode.ARGUMENTS_IS_ERROR);
        }
        return idList;
    }

    /**
     * 常跑路线气泡数量接口
     */
    @PostMapping("/hall/bubbleCount")
    public WebResult<OftenRouteBubbleVO> bubbleCount(@RequestBody OftenRouteSearchDTO searchDTO) {
        return WebResult.success(routeDataService.bubbleCount(searchDTO));
    }

    /**
     * 常跑路线找货大厅接口
     */
    @PostMapping("/hall/list")
    public WebResult<OftenRouteSearchVO> hallList(@RequestBody OftenRouteSearchDTO searchDTO) {
        return WebResult.success(routeDataService.hallList(searchDTO));
    }
}
