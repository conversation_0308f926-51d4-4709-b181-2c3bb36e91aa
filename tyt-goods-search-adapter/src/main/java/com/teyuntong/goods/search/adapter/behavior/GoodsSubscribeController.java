package com.teyuntong.goods.search.adapter.behavior;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.net.URLDecoder;
import com.teyuntong.goods.search.service.biz.behavior.dto.TransportSubscribeDTO;
import com.teyuntong.goods.search.service.biz.behavior.serivce.TransportSubscribeService;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

/**
 * 货源订阅接口
 *
 * <AUTHOR>
 * @since 2024/12/14 17:58
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/goods/subscribe")
@Slf4j
public class GoodsSubscribeController {

    private final TransportSubscribeService subscribeService;

    /**
     * 货源订阅接口
     */
    @PostMapping("/add")
    public WebResult<List<TransportVO>> add(@RequestBody TransportSubscribeDTO subscribeDTO) {
        subscribeService.addTransportSubscribe(subscribeDTO);
        return WebResult.success();
    }

    /**
     * 货源订阅接口
     * 注意：该接口用于APP融合，入参会传递2次，需要用数组接收，取第一个
     */
    @GetMapping("/save")
    public WebResult<List<TransportVO>> save(
            @RequestParam("startPoint") String[] startPoint,
            @RequestParam("destPoint") String[] destPoint,
            @RequestParam("loadingTime") String[] loadingTime,
            @RequestParam("weightMin") String[] weightMin,
            @RequestParam("weightMax") String[] weightMax,
            @RequestParam("orderId") String[] orderId) {
        log.info("添加订阅货源，入参:startPoint:{},destPoint:{},loadingTime:{},weightMin:{},weightMax:{},orderId:{}",
                Arrays.toString(startPoint), Arrays.toString(destPoint), Arrays.toString(loadingTime), Arrays.toString(weightMin), Arrays.toString(weightMax), Arrays.toString(orderId));

        try {
            TransportSubscribeDTO subscribeDTO = new TransportSubscribeDTO();
            subscribeDTO.setStartPoint(getFirstValue(startPoint, t -> URLDecoder.decode(t, Charset.defaultCharset())));
            subscribeDTO.setDestPoint(getFirstValue(destPoint, t -> URLDecoder.decode(t, Charset.defaultCharset())));
            subscribeDTO.setLoadingTime(getFirstValue(loadingTime, DateUtil::parse));
            subscribeDTO.setWeightMin(getFirstValue(weightMin, BigDecimal::new));
            subscribeDTO.setWeightMax(getFirstValue(weightMax, BigDecimal::new));
            subscribeDTO.setOrderId(getFirstValue(orderId, Long::valueOf));
            subscribeService.addTransportSubscribe(subscribeDTO);
        } catch (Exception e) {
            log.error("添加订阅货源失败", e);
        }

        return WebResult.success();
    }

    private <T> T getFirstValue(String[] strs, Function<String, T> function) {
        if (strs == null || strs.length == 0) {
            return null;
        }
        return StringUtils.isBlank(strs[0]) ? null : function.apply(strs[0]);
    }
}