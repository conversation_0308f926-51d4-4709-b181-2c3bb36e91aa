package com.teyuntong.goods.search.adapter.goods;

import com.teyuntong.goods.search.service.biz.goods.dto.SearchRecordQueryDTO;
import com.teyuntong.goods.search.service.biz.record.dto.CallFeedBackDTO;
import com.teyuntong.goods.search.service.biz.record.dto.CallPhoneRecordDTO;
import com.teyuntong.goods.search.service.biz.record.vo.CallFeedBackVO;
import com.teyuntong.goods.search.service.biz.record.vo.SearchRecordPageVO;
import com.teyuntong.goods.search.service.rpc.goods.service.SearchRecordRpcService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 找货记录
 *
 * <AUTHOR>
 * @since 2024/10/11 09:59
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/searchRecord")
public class SearchRecordController {

    private final SearchRecordRpcService searchRecordRpcService;

    /**
     * 查询找货记录，包括浏览记录、拨打记录、出价记录
     */
    @PostMapping("/queryRecordList")
    public WebResult<SearchRecordPageVO> queryRecordList(@RequestBody SearchRecordQueryDTO queryDTO) {
        return WebResult.success(searchRecordRpcService.queryRecordList(queryDTO));
    }

    /**
     * 车方 被反馈（报价被货方同意）、有回价 气泡提示
     */
    @PostMapping("/quotedBubble")
    public WebResult<String> quotedBubble() {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return WebResult.success("");
        }
        Long userId = loginUser.getUserId();
        return WebResult.success(searchRecordRpcService.quotedBubble(userId));
    }

    /**
     * 获取默认页面类型
     */
    @PostMapping("/getDefaultType")
    public WebResult<Integer> getDefaultType() {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return WebResult.success();
        }
        Long userId = loginUser.getUserId();
        return WebResult.success(searchRecordRpcService.getDefaultType(userId));
    }

    /**
     * 保存拨打记录
     */
    @PostMapping("/call/record/saveCallRecord")
    public WebResult<Void> saveCallRecord(@RequestBody CallPhoneRecordDTO recordDTO) {
        searchRecordRpcService.saveCallRecord(recordDTO);
        return WebResult.success();
    }

    /**
     * 获取拨打反馈页面数据
     */
    @GetMapping("/call/page/getCallFeedback")
    public WebResult<CallFeedBackVO> getCallFeedback(Long srcMsgId) {
        return WebResult.success(searchRecordRpcService.getCallFeedback(srcMsgId));
    }

    /**
     * 保存拨打反馈页面数据
     */
    @PostMapping("/call/page/saveCallFeedback")
    public WebResult<Void> saveCallFeedback(@RequestBody CallFeedBackDTO callFeedBackDTO) {
        searchRecordRpcService.saveCallFeedback(callFeedBackDTO);
        return WebResult.success();
    }

    /**
     * 是否填写过反馈页面
     */
    @GetMapping("/call/page/hasCallFeedback")
    public WebResult<Integer> hasCallFeedback(Long srcMsgId) {
        return WebResult.success(searchRecordRpcService.hasCallFeedback(srcMsgId));
    }

    /**
     * 返回反馈一级选项
     */
    @GetMapping("/call/page/getCallFeedbackOptions")
    public WebResult<List<CallFeedBackVO.Option>> getCallFeedbackOptions(Long srcMsgId) {
        return WebResult.success(searchRecordRpcService.getCallFeedbackOptions(srcMsgId));
    }

}
