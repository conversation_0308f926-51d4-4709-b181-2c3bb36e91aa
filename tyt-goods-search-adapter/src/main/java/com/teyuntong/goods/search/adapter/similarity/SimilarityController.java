package com.teyuntong.goods.search.adapter.similarity;

import com.teyuntong.goods.search.service.biz.similarity.dto.SimilarityQueryDTO;
import com.teyuntong.goods.search.service.biz.similarity.vo.SimilarityVO;
import com.teyuntong.goods.search.service.rpc.similarity.service.SimilarityRpcService;
import com.teyuntong.infra.common.definition.bean.WebResult;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 相似货源接口
 *
 * <AUTHOR>
 * @since 2024/07/04 09:30
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/similarity")
public class SimilarityController {

    private final SimilarityRpcService similarityRpcService;

    /**
     * 查询相似货源列表
     */
    @PostMapping("/getSimilarityList")
    public WebResult<List<SimilarityVO>> getSimilarityList(@RequestBody @Validated SimilarityQueryDTO queryDTO) {
        return WebResult.success(similarityRpcService.getSimilarityList(queryDTO));
    }
}
