package com.teyuntong.goods.search.adapter.goods;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.teyuntong.goods.search.service.biz.exposure.vo.ExposureVO;
import com.teyuntong.goods.search.service.biz.goods.dto.*;
import com.teyuntong.goods.search.service.biz.goods.vo.*;
import com.teyuntong.goods.search.service.biz.search.dto.SearchDistanceDTO;
import com.teyuntong.goods.search.service.biz.tytlog.service.SearchLogRecordService;
import com.teyuntong.goods.search.service.rpc.goods.service.SearchHallRpcService;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/18 19:49
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/hall")
@Slf4j
public class SearchHallController {

    public final SearchHallRpcService searchHallRpcService;

    public final SearchLogRecordService searchLogRecordService;

    /**
     * 智能排序
     *
     * @param intelligenceSortDTO
     * @return
     */
    @PostMapping("/intelligence/sortList")
    public WebResult<IntelligenceSortVO> intelligenceSortList(@RequestBody IntelligenceSortDTO intelligenceSortDTO) {
        IntelligenceSortVO intelligenceSortVO = searchHallRpcService.intelligenceSortList(intelligenceSortDTO);
        // 保存搜索日志
        // 开关开启且是下拉刷新的情况下情况下才记录搜索日志,
        intelligenceSortDTO.setSortType("3");
        searchLogRecordService.saveTsSearchLog(intelligenceSortDTO, LoginHelper.getBaseParam(), intelligenceSortVO.getList());
        return WebResult.success(intelligenceSortVO);
    }

    /**
     * 智能排序气泡数量
     *
     * @param intelligenceSortDTO
     * @return
     */
    @PostMapping("/intelligence/bubbleCount")
    public WebResult<IntelligenceCountVO> bubbleCount(@RequestBody IntelligenceSortDTO intelligenceSortDTO) {

        return WebResult.success(searchHallRpcService.intelligenceBubbleCount(intelligenceSortDTO));
    }

    /**
     * 找货大厅列表
     *
     * @param baseTransportSearchDTO
     * @return
     */
    @PostMapping("/search/list")
    public WebResult<SearchHallVO> searchHallList(@RequestBody BaseTransportSearchDTO baseTransportSearchDTO) {
        SearchHallVO searchHallVo = new SearchHallVO();
        List<TransportVO> transportVOList = searchHallRpcService.searchHallList(baseTransportSearchDTO);

        if (CollUtil.isNotEmpty(transportVOList)) {
            searchHallVo.setHasNext(transportVOList.size() >= baseTransportSearchDTO.getPageSize());
            searchHallVo.setResponseSize(transportVOList.size());
        }
        searchHallVo.setList(transportVOList);
        // 保存搜索日志
        searchLogRecordService.saveTsSearchLog(baseTransportSearchDTO, LoginHelper.getBaseParam(), transportVOList);
        return WebResult.success(searchHallVo);
    }

    /**
     * 找货大厅列表
     *
     * @param baseTransportSearchDTO
     * @return
     */
    @PostMapping("/search/newList")
    public WebResult<SearchHallVO> searchHallNewList(@RequestBody BaseTransportSearchDTO baseTransportSearchDTO) {
        //        log.info("===>/hall/search/newList;baseTransportSearchDTO:{},user:{},baseParam:{}", JSONUtil.toJsonStr(baseTransportSearchDTO),
        //                LoginHelper.getLoginUser(), LoginHelper.getBaseParam());
        SearchHallVO searchHallVo = searchHallRpcService.searchHallNewList(baseTransportSearchDTO);
        // 保存搜索日志
        searchLogRecordService.saveTsSearchLog(baseTransportSearchDTO, LoginHelper.getBaseParam(), searchHallVo.getList());
        // 打印日志
        //        printLog(searchHallVo, "/hall/search/newList");
        return WebResult.success(searchHallVo);
    }

    private void printLog(SearchHallVO searchHallVo, String url) {
        try {
            Set<Long> tsIds = new HashSet<>();
            if (CollUtil.isNotEmpty(searchHallVo.getList())) {
                tsIds = searchHallVo.getList().stream().map(TransportVO::getId).collect(Collectors.toSet());
            }
            log.info("<===,url:{}, responseSize:{},searchHallExtra:{},maxTsId:{},tsIds:{}", url,
                    searchHallVo.getResponseSize(), JSONUtil.toJsonStr(searchHallVo.getSearchHallExtra()),
                    searchHallVo.getMaxTsId(), JSONUtil.toJsonStr(tsIds));
        } catch (Exception e) {
            log.error("===>{}", url, e);
        }
    }

    /**
     * 时间排序气泡数量
     *
     * @param searchHallCountDTO
     * @return
     */
    @PostMapping("/search/bubbleCount")
    public WebResult<SearchHallCountVO> searchHallCount(@RequestBody SearchHallCountDTO searchHallCountDTO) {

        return WebResult.success(searchHallRpcService.searchHallCount(searchHallCountDTO));
    }


    /**
     * 找货大厅无货源时推荐列表
     *
     * @param hallRecommendDTO
     * @return
     */
    @PostMapping("/recommend/list")
    public WebResult<List<TransportVO>> recommendList(@RequestBody HallRecommendDTO hallRecommendDTO) {

        return WebResult.success(searchHallRpcService.recommendList(hallRecommendDTO));
    }

    /**
     * 找货大厅变动货源
     *
     * @param transportVaryDTO
     * @return
     */
    @PostMapping("/search/vary")
    public WebResult<List<TransportVaryVO>> searchHallVary(@RequestBody TransportVaryDTO transportVaryDTO) {

        return WebResult.success(searchHallRpcService.searchHallVary(transportVaryDTO));
    }

    /**
     * 范围倒短
     *
     * @param shortRangeDTO
     * @return
     */
    @PostMapping(value = "/short/range")
    public WebResult<List<TransportVO>> searchShortRange(@RequestBody @Validated ShortRangeDTO shortRangeDTO) {

        return WebResult.success(searchHallRpcService.searchShortRange(shortRangeDTO));
    }

    /**
     * 省内倒短
     *
     * @param shortProvinceDTO
     * @return
     */
    @PostMapping(value = "/short/province")
    public WebResult<List<TransportVO>> searchShortProvince(@RequestBody @Validated ShortProvinceDTO shortProvinceDTO) {

        return WebResult.success(searchHallRpcService.searchShortProvince(shortProvinceDTO));
    }

    /**
     * 同路线或同目的地货源
     */
    @PostMapping(value = "/same/routeDest")
    public WebResult<List<ExposureVO>> searchRouteDestList(@RequestBody @Validated SameRouteDestDTO sameRouteDestDTO) {
        return WebResult.success(searchHallRpcService.searchRouteDestList(sameRouteDestDTO));
    }


    /**
     * 找货大厅智能推荐列表
     */
    @PostMapping(value = "/intelligence/recommend")
    public WebResult<List<TransportVO>> intelligenceRecommendList(@RequestBody(required = false) IntelligenceRecommendDTO intelligenceRecommendDTO) {
        return WebResult.success(searchHallRpcService.intelligenceRecommendList(intelligenceRecommendDTO));
    }


    /**
     * 返回找货大厅配置的搜索距离
     */
    @PostMapping(value = "/search/distance")
    public WebResult<SearchDistanceDTO> searchDistance(@RequestBody SearchDistanceDTO sdDTO) {
        return WebResult.success(searchHallRpcService.getSearchDistance(sdDTO));
    }

    /**
     * 多车找货气泡提醒接口
     */
    @GetMapping(value = "/car/bubble")
    public WebResult<CarBubbleVO> carBubble() {
        return WebResult.success(searchHallRpcService.carBubble());
    }
}
