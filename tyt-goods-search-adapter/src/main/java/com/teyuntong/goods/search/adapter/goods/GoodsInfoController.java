package com.teyuntong.goods.search.adapter.goods;

import com.teyuntong.goods.search.service.biz.goods.dto.GoodsInfoRecommendDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.GoodsPhoneDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.GoodsStatusDTO;
import com.teyuntong.goods.search.service.biz.goods.vo.GoodsPhoneVO;
import com.teyuntong.goods.search.service.biz.goods.vo.GoodsStatusVO;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.goods.search.service.common.enums.ExcellentGoodsEnum;
import com.teyuntong.goods.search.service.common.enums.YesNoEnum;
import com.teyuntong.goods.search.service.common.error.GoodsSearchErrorCode;
import com.teyuntong.goods.search.service.rpc.goods.service.GoodsInfoRpcService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/11/15 16:20
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/goods/info")
@Slf4j
public class GoodsInfoController {

    private final GoodsInfoRpcService goodsInfoRpcService;

    /**
     * 货源详情页推荐货源
     *
     * @param goodsInfoRecommendDTO
     * @return
     */
    @PostMapping("/recommend")
    public WebResult<List<TransportVO>> recommend(@RequestBody GoodsInfoRecommendDTO goodsInfoRecommendDTO) {

        return WebResult.success(goodsInfoRpcService.InfoRecommendList(goodsInfoRecommendDTO));
    }

    /**
     * 支付前判断用户是否可以支付的状态
     *
     * @param goodsStatusDTO
     * @return
     */
    @PostMapping("/goodsStatus")
    public WebResult<GoodsStatusVO> getGoodsStatus(@RequestBody @Validated GoodsStatusDTO goodsStatusDTO) {
        LoginUserDTO user = LoginHelper.getRequiredLoginUser();

        GoodsStatusVO goodsStatusVo = goodsInfoRpcService.getGoodsStatus(goodsStatusDTO, user);
        if (Objects.equals(goodsStatusVo.getGoodStatus(), YesNoEnum.Y.code)) {
            // 专车货源校验
            if (Objects.equals(goodsStatusVo.getExcellentGoods(), ExcellentGoodsEnum.SPECIAL_CAR_GOODS.getCode())) {
                goodsInfoRpcService.checkSpecialCar(goodsStatusVo, user);
                // 判断一段时间内是否支付过该货源,如果支付过则提示用户
                if (goodsInfoRpcService.checkSpecialCarPayLimit(user.getUserId())) {
                    return new WebResult<>(GoodsSearchErrorCode.SPECIAL_CAR_PAYMENT, goodsStatusVo, null);
                }
            }
        }
        return WebResult.success(goodsStatusVo);
    }

    /**
     * 支付前判断用户是否可以支付的状态
     *
     * @param goodsPhoneDTO
     * @return
     */
    @PostMapping("/getPhone")
    public WebResult<GoodsPhoneVO> getPhone(@RequestBody @Validated GoodsPhoneDTO goodsPhoneDTO) {
        LoginUserDTO user = LoginHelper.getRequiredLoginUser();

        return WebResult.success(goodsInfoRpcService.getGoodsPhone(goodsPhoneDTO, user));
    }

}
