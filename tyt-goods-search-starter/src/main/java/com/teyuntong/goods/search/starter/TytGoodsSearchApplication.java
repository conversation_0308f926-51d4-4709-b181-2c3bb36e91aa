package com.teyuntong.goods.search.starter;

import com.teyuntong.infra.common.retrofit.core.EnableRetrofitClient;
import org.apache.ibatis.annotations.Mapper;
import org.dromara.easyes.starter.register.EsMapperScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@EnableAsync
@EnableScheduling
@EnableFeignClients(basePackages = "com.teyuntong.goods.search")
@EnableDiscoveryClient
@EnableAspectJAutoProxy(proxyTargetClass = true)
@MapperScan(basePackages = "com.teyuntong.goods.search", annotationClass = Mapper.class)
@SpringBootApplication(scanBasePackages = {"com.teyuntong.goods.search", "com.teyuntong.infra.common"})
@EnableRetrofitClient(basePackages = "com.teyuntong.goods.search")
@EsMapperScan(value = "com.teyuntong.goods.search.service.biz.goods.es.mapper")
public class TytGoodsSearchApplication {

    public static void main(String[] args) {
        SpringApplication.run(TytGoodsSearchApplication.class, args);
    }

}