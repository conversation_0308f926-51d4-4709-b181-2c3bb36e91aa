server:
  port: 6001
  servlet:
    context-path: /goods-search
  tomcat:
    max-connections: 2000

management:
  endpoints:
    web:
      exposure:
        include: health

spring:
  application:
    name: tyt-goods-search
  profiles:
    active: local
  #上传文件大小设置
  servlet:
    multipart:
      enabled: true
      #上传单个文件大小限制
      max-file-size: 10MB
      #上传总文件大小限制
      max-request-size: 50MB
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration
  jackson:
    defaultPropertyInclusion: non_null
    serialization:
      WRITE_DATES_AS_TIMESTAMPS: true
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false
  cache:
    redis:
      cacheNullValues: true
      # 默认缓存失效时间, 30*60*1000 = 30分钟
      timeToLive: 1800000
  cloud:
    loadbalancer:
      enabled: true
      retry:
        enabled: true
        avoid-previous-instance: true
custom:
  feign:
    decoder:
      log-business-exception: true
      throw-business-exception: true

feign:
  okhttp:
    enabled: true
  httpclient:
    connection-timeout: 5000
    ok-http:
      read-timeout: 60000
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 5000
  compression:
    request:
      enabled: true
    response:
      enabled: true
  circuitbreaker:
    enabled: false
    alphanumeric-ids:
      enabled: true
resilience4j.circuitbreaker:
  configs:
    default:
      minimumNumberOfCalls: 5
      sliding-window-size: 60
      sliding-window-type: count_based
      failureRateThreshold: 50
  instances:
    timeoutTestGroup:
      minimumNumberOfCalls: 10
      sliding-window-size: 60
      sliding-window-type: count_based
      failureRateThreshold: 50
    TimeoutSupplier-TimeoutRetrofit:
      minimumNumberOfCalls: 5
      sliding-window-size: 60
      sliding-window-type: count_based
      failureRateThreshold: 20
resilience4j.timelimiter:
  configs:
    default:
      timeoutDuration: 4s
  instances:
    timeoutTestGroup:
      timeout-duration: 3s
    TimeoutSupplier-TimeoutRetrofit:
      timeout-duration: 1s

resilience4j.retry:
  configs:
    default:
      wait-duration: 1s
      max-attempts: 3
  #      ignore-exceptions:
  #        - java.net.ConnectException
  instances:
    timeoutTestGroup:
      base-config: default

resilience4j.ratelimiter:
  configs:
    default:
      limit-for-period: 50
      limit-refresh-period: 5s
      timeout-duration: 3s
  instances:
    timeoutTestGroup:
      base-config: default

retrofit:
  circuitbreaker:
    resilience4j:
      enable: true

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    auto-mapping-behavior: full
    alias-search-package: com.teyuntong.goods.search
#    type-aliases-package: com.teyuntong.goods.search.service.mybatis.entity
  mapper-locations: classpath:mapper/*.xml

logging:
  level:
    root: info
    com.teyuntong: info # 通过此项更改 com.teyuntong 包下的日志级别

springdoc:
  swagger-ui:
    # 访问 /swagger-ui/index.html 可以访问 swagger, 生产环境需要关闭
    enabled: false
  api-docs:
    # 访问 /v3/api-docs 可以获取api定义, 生产环境需要关闭
    enabled: false

---
spring:
  config:
    activate:
      on-profile: local
  cloud:
    nacos:
      discovery:
        server-addr: http://mse-96d0cfa2-p.nacos-ans.mse.aliyuncs.com:8848
        namespace: 60b84c10-ce6f-401b-8452-514f593cb317
        password: tyt
        username: tyt
        group: ${os.name}___${java.library.path}___${java.home}___${java.runtime.version}___${user.name}
      config:
        server-addr: http://mse-96d0cfa2-p.nacos-ans.mse.aliyuncs.com:8848
        file-extension: yaml
        namespace: 60b84c10-ce6f-401b-8452-514f593cb317
        password: tyt
        username: tyt
        group: DEFAULT_GROUP
        # 会在配置中心寻找 dataId 为 tyt-scaffold.yaml 的配置
        name: ${spring.application.name}
        extension-configs:
          - dataId: foo.yaml
            group: DEFAULT_GROUP
            refresh: true
---
spring:
  config:
    activate:
      on-profile: dev
  cloud:
    nacos:
      discovery:
        server-addr: http://mse-96d0cfa2-nacos-ans.mse.aliyuncs.com:8848
        namespace: e7839125-3aa2-4a32-a927-5f01d7581a00
        password: tyt
        username: tyt
      config:
        server-addr: http://mse-96d0cfa2-nacos-ans.mse.aliyuncs.com:8848
        file-extension: yaml
        namespace: e7839125-3aa2-4a32-a927-5f01d7581a00
        password: tyt
        username: tyt
        group: DEFAULT_GROUP
        # 会在配置中心寻找 dataId 为 tyt-scaffold.yaml 的配置
        name: ${spring.application.name}

---
spring:
  config:
    activate:
      on-profile: test
  cloud:
    nacos:
      discovery:
        server-addr: http://mse-96d0cfa2-nacos-ans.mse.aliyuncs.com:8848
        namespace: a0786143-63f0-4a01-aaf7-f2fe06eddcfe
        password: tyt
        username: tyt
      config:
        server-addr: http://mse-96d0cfa2-nacos-ans.mse.aliyuncs.com:8848
        file-extension: yaml
        namespace: a0786143-63f0-4a01-aaf7-f2fe06eddcfe
        password: tyt
        username: tyt
        group: DEFAULT_GROUP
        # 会在配置中心寻找 dataId 为 tyt-scaffold.yaml 的配置
        name: ${spring.application.name}

---
spring:
  config:
    activate:
      on-profile: release
  cloud:
    nacos:
      discovery:
        server-addr: http://mse-96d0cfa2-nacos-ans.mse.aliyuncs.com:8848
        namespace: bf9db828-b029-4436-a35d-9321e77f05bc
        password: tyt
        username: tyt
      config:
        server-addr: http://mse-96d0cfa2-nacos-ans.mse.aliyuncs.com:8848
        file-extension: yaml
        namespace: bf9db828-b029-4436-a35d-9321e77f05bc
        password: tyt
        username: tyt
        group: DEFAULT_GROUP
        # 会在配置中心寻找 dataId 为 tyt-scaffold.yaml 的配置
        name: ${spring.application.name}

---
spring:
  config:
    activate:
      on-profile: prod
  cloud:
    nacos:
      discovery:
        server-addr: https://mse-050b1f62-nacos-ans.mse.aliyuncs.com
        namespace: b73c3f26-ba58-4348-a1a8-5f94015ae9ec
        access-key: LTAI5tJaEsHG9wAToMPLkQgs
        secret-key: ******************************
      config:
        server-addr: https://mse-050b1f62-nacos-ans.mse.aliyuncs.com
        file-extension: yaml
        namespace: b73c3f26-ba58-4348-a1a8-5f94015ae9ec
        access-key: LTAI5tJaEsHG9wAToMPLkQgs
        secret-key: ******************************
        group: DEFAULT_GROUP
        # 会在配置中心寻找 dataId 为 tyt-scaffold.yaml 的配置
        name: ${spring.application.name}