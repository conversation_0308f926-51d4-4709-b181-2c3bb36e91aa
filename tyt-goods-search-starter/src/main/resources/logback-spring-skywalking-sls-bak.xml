<?xml version="1.0" encoding="UTF-8"?>
<!--
    scan: 当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true。
    scanPeriod: 设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。
    debug: 当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。
    configuration 子节点为 appender、logger、root
-->
<configuration scan="true" scanPeriod="600 seconds" debug="false">
    <!--为了防止进程退出时，内存中的数据丢失，请加上此选项-->
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>

    <!--配置多环境日志输出
    可以在application.properties中配置选择哪个profiles : spring.profiles.active=dev-->
    <!-- <springProfile name="online">
        <property name="print_min_level" value="INFO" />
    </springProfile> -->
    <!-- 打印的最低级别 -->
    <springProperty scope="context" name="print_min_level" source="logging.level.root" defaultValue="info"/>
    <!-- 项目名称 -->
    <springProperty scope="context" name="app_name" source="spring.application.name"/>
    <!--定义日志文件的存储地址
    勿在 LogBack 的配置中使用相对路径-->
    <springProperty scope="context" name="log_out_path" source="logging.file.path" defaultValue="logs"/>
    <springProperty scope="context" name="active_profile" source="spring.profiles.active" defaultValue="local"/>

    <contextName>${HOSTNAME}</contextName>
    <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度
    %logger输出日志的logger名 %msg：日志消息，%n是换行符 -->
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr"
                    converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <!-- 彩色日志format -->
    <property name="color_layout_pattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{tid}] %clr(${PID:- }){magenta} %clr(%5p) %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n %ex"/>
    <!-- 正常日志format -->
    <property name="normal_layout_pattern"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{tid}] ${PID:- } %5p --- [%15.15t] %-40.40logger{39} : %m%n %ex"/>

    <!--控制台-->
    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <charset>UTF-8</charset>
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>${color_layout_pattern}</pattern>
            </layout>
        </encoder>
    </appender>

    <!--滚动文件-->
    <appender name="infoFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${log_out_path:-logs}/info/${app_name}.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log_out_path:-logs}/info/${app_name}-%d{yyyyMMdd}-%i.log</fileNamePattern>
            <!-- 单个日志文件最大200M，到了这个值，就会再创建一个日志文件 -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 保存最近7天的日志 -->
            <maxHistory>7</maxHistory>
            <!-- 所有的日志文件最大20G，超过就会删除旧的日志 -->
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <charset>UTF-8</charset>
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>${normal_layout_pattern}</pattern>
            </layout>
        </encoder>
    </appender>

    <!--滚动文件-->
    <appender name="warnFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${log_out_path:-logs}/warn/${app_name}.log</File>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log_out_path:-logs}/warn/${app_name}-%d{yyyyMMdd}-%i.log</fileNamePattern>
            <!-- 单个日志文件最大200M，到了这个值，就会再创建一个日志文件 -->
            <maxFileSize>200MB</maxFileSize>
            <!-- 保存最近7天的日志 -->
            <maxHistory>7</maxHistory>
            <!-- 所有的日志文件最大20G，超过就会删除旧的日志 -->
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <charset>UTF-8</charset>
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>${normal_layout_pattern}</pattern>
            </layout>
        </encoder>
    </appender>

    <!--滚动文件-->
    <appender name="errorFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${log_out_path:-logs}/error/${app_name}.log</File>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log_out_path:-logs}/error/${app_name}-%d{yyyyMMdd}-%i.log</fileNamePattern>
            <!-- 单个日志文件最大200M，到了这个值，就会再创建一个日志文件 -->
            <maxFileSize>200MB</maxFileSize>
            <!-- 保存最近7天的日志 -->
            <maxHistory>7</maxHistory>
            <!-- 所有的日志文件最大20G，超过就会删除旧的日志 -->
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <charset>UTF-8</charset>
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>${normal_layout_pattern}</pattern>
            </layout>
        </encoder>
    </appender>

    <!--什么都不干-->
    <appender name="no_op" class="ch.qos.logback.core.helpers.NOPAppender"/>

    <!-- 把日志异步输出到磁盘文件中，避免每次都进行磁盘IO操作 -->
    <appender name="asyncAppender" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>-1</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>2048</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="infoFile"/>
    </appender>

    <springProperty scope="context" name="sls.enable" source="logging.sls.configs.tmp-store.enable"
                    defaultValue="false"/>
    <springProperty scope="context" name="sls.access-key-id" source="logging.sls.configs.tmp-store.access-key-id"
                    defaultValue="access-key-id_is_required"/>
    <springProperty scope="context" name="sls.access-key-secret"
                    source="logging.sls.configs.tmp-store.access-key-secret"
                    defaultValue="access-key-secret_is_required"/>
    <springProperty scope="context" name="sls.base-retry-backoff-ms"
                    source="logging.sls.configs.tmp-store.base-retry-backoff-ms"
                    defaultValue="100"/>
    <springProperty scope="context" name="sls.batch-count-threshold"
                    source="logging.sls.configs.tmp-store.batch-count-threshold"
                    defaultValue="4096"/>
    <springProperty scope="context" name="sls.batch-size-threshold-in-bytes"
                    source="logging.sls.configs.tmp-store.batch-size-threshold-in-bytes"
                    defaultValue="524288"/>
    <springProperty scope="context" name="sls.endpoint" source="logging.sls.configs.tmp-store.endpoint"
                    defaultValue="endpoint_is_required"/>
    <springProperty scope="context" name="sls.include-location" source="logging.sls.configs.tmp-store.include-location"
                    defaultValue="false"/>
    <springProperty scope="context" name="sls.io-thread-count" source="logging.sls.configs.tmp-store.io-thread-count"
                    defaultValue="8"/>
    <springProperty scope="context" name="sls.linger-ms" source="logging.sls.configs.tmp-store.linger-ms"
                    defaultValue="2000"/>
    <springProperty scope="context" name="sls.log-store" source="logging.sls.configs.tmp-store.log-store"
                    defaultValue="log-store_is_required"/>
    <springProperty scope="context" name="sls.max-block-ms" source="logging.sls.configs.tmp-store.max-block-ms"
                    defaultValue="100"/>
    <springProperty scope="context" name="sls.max-reserved-attempts"
                    source="logging.sls.configs.tmp-store.max-reserved-attempts"
                    defaultValue="11"/>
    <springProperty scope="context" name="sls.max-retry-backoff-ms"
                    source="logging.sls.configs.tmp-store.max-retry-backoff-ms"
                    defaultValue="50000"/>
    <springProperty scope="context" name="sls.project" source="logging.sls.configs.tmp-store.project"
                    defaultValue="project_is_required"/>
    <springProperty scope="context" name="sls.retries" source="logging.sls.configs.tmp-store.retries"
                    defaultValue="10"/>
    <springProperty scope="context" name="sls.source" source="logging.sls.configs.tmp-store.source" defaultValue=""/>
    <springProperty scope="context" name="sls.time-format" source="logging.sls.configs.tmp-store.time-format"
                    defaultValue="yyyy-MM-dd HH:mm:ss.SSS"/>
    <springProperty scope="context" name="sls.time-zone" source="logging.sls.configs.tmp-store.time-zone"
                    defaultValue="Asia/Shanghai"/>
    <springProperty scope="context" name="sls.topic" source="logging.sls.configs.tmp-store.topic" defaultValue=""/>
    <springProperty scope="context" name="sls.total-size-in-bytes"
                    source="logging.sls.configs.tmp-store.total-size-in-bytes"
                    defaultValue="104857600"/>
    <springProperty scope="context" name="sls.mdc-fields" source="logging.sls.configs.tmp-store.mdc-fields"
                    defaultValue=""/>

    <appender name="aliyun" class="com.teyuntong.infra.common.logging.sls.CustomLoghubAppender">
        <!--必选项-->
        <!-- 账号及网络配置 -->
        <enable>${sls.enable}</enable>
        <endpoint>${sls.endpoint}</endpoint>
        <accessKeyId>${sls.access-key-id}</accessKeyId>
        <accessKeySecret>${sls.access-key-secret}</accessKeySecret>

        <!-- sls 项目配置 -->
        <project>${sls.project}</project>
        <logStore>${sls.log-store}</logStore>
        <!--必选项 (end)-->

        <topic>${sls.topic}</topic>
        <source>${sls.source}</source>

        <!-- 可选项 详见 '参数说明'-->
        <totalSizeInBytes>${sls.total-size-in-bytes}</totalSizeInBytes>
        <maxBlockMs>${sls.max-block-ms}</maxBlockMs>
        <maxReservedAttempts>${sls.max-reserved-attempts}</maxReservedAttempts>
        <ioThreadCount>${sls.io-thread-count}</ioThreadCount>
        <batchSizeThresholdInBytes>${sls.batch-size-threshold-in-bytes}</batchSizeThresholdInBytes>
        <batchCountThreshold>${sls.batch-count-threshold}</batchCountThreshold>
        <lingerMs>${sls.linger-ms}</lingerMs>
        <retries>${sls.retries}</retries>
        <baseRetryBackoffMs>${sls.base-retry-backoff-ms}</baseRetryBackoffMs>
        <maxRetryBackoffMs>${sls.max-retry-backoff-ms}</maxRetryBackoffMs>

        <!-- 可选项 设置 time 字段呈现的格式 -->
        <timeFormat>${sls.time-format}</timeFormat>
        <!-- 可选项 设置 time 字段呈现的时区 -->
        <timeZone>${sls.time-zone}</timeZone>
        <!-- 可选项 设置是否要添加 Location 字段（日志打印位置），默认为 true -->
        <includeLocation>${sls.include-location}</includeLocation>
        <mdcFields>${sls.mdc-fields}</mdcFields>

        <!-- 可选项 通过配置 encoder 的 pattern 自定义 log 的格式 -->
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <charset>UTF-8</charset>
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>${normal_layout_pattern}</pattern>
            </layout>
        </encoder>
    </appender>

    <root level="${logging.root.level}">
        <appender-ref ref="stdout"/>
        <appender-ref ref="asyncAppender"/>
        <appender-ref ref="errorFile"/>
        <appender-ref ref="warnFile"/>
        <!-- 目前不用 -->
        <!-- <appender-ref ref="aliyun"/> -->
    </root>

    <!--  mq 消费者 logger-->
    <logger name="mq_consumer" level="${logging.root.level}" additivity="false">
        <appender-ref ref="no_op"/>
    </logger>
    <!--  mq 生产者 logger-->
    <logger name="mq_producer" level="${logging.root.level}" additivity="false">
        <appender-ref ref="no_op"/>
    </logger>
</configuration>