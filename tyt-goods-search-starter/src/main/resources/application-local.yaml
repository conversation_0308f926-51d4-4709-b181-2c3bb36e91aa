spring:
  datasource:
    dynamic:
      primary: goods_search
      datasource:
        good_search:
          url: '***********************************************************************************************************************************************'
          username: tyt_dev
          password: tyt_dev#20200724
          driver-class-name: com.mysql.cj.jdbc.Driver
        tyt:
          url: '***********************************************************************************************************************************************'
          username: tyt_dev
          password: tyt_dev#20200724
#          url: '**********************************************************************************************************************************************************************************'
#          username: test_readonly
#          password: test_readonly@TE
          driverClassName: com.mysql.cj.jdbc.Driver
        recommend:
          url: '*********************************************************************************************************************'
          username: tyt_dev
          password: tyt_dev#20200724
#          url: '********************************************************************************************************************************************************************************************'
#          username: test_readonly
#          password: test_readonly@TE
          driver-class-name: com.mysql.cj.jdbc.Driver
        tytLog:
          url: '**************************************************************************************************************'
          username: tyt_dev
          password: tyt_dev#20200724
          driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        max-pool-size: 40
        min-idle: 15
        max-lifetime: 30000
  redis:
    database: 0
    host: public-network-dev-0.redis.rds.aliyuncs.com
    port: 16379
    password: TyT@dev#20220323
    lettuce:
      pool:
        # 最大连接数
        max-active: 8
        # 最大空闲连接数
        max-idle: 8
        # 最小空闲连接数
        min-idle: 2
  redis-often-route:
    database: 10
    host: public-network-dev-0.redis.rds.aliyuncs.com
    port: 16379
    password: TyT@dev#20220323
    lettuce:
      pool:
        # 最大连接数
        max-active: 8
        # 最大空闲连接数
        max-idle: 8
        # 最小空闲连接数
        min-idle: 2
  cache:
    redis:
      # 默认缓存失效时间, 30*60*1000 = 30分钟
      timeToLive: 1800000
      keyPrefix: "tytGoodsSearch:"

xxl-job:
  enable: false

rocket-mq:
  # 是否开启生产者和消费者
  consumer:
    enable: false
  producer:
    enable: true
  # rocket mq配置
  nameSrvAddr: http://MQ_INST_1955986389231769_BX2m3KBq.cn-beijing.mq.aliyuncs.com:80
  accessKey: LTAI5t9324vzFd6VLMybLzoE
  secretKey: ******************************
  # topic配置
  topic:
    # 常跑路线topic
    often-route: OFTEN_ROUTE_TOPIC

easy-es:
  address: ************:9200 # es的连接地址,必须含端口 若为集群,则可以用逗号隔开 例如:127.0.0.1:9200,*********:9200
 # username: elastic #如果无账号密码则可不配置此行
  #password: Es#Tyt@20210524 #如果无账号密码则可不配置此行
  keep-alive-millis: 300000 # 心跳策略时间 单位:ms
  connect-timeout: 10000 # 连接超时时间 单位:ms
  socket-timeout: 20000 # 通信超时时间 单位:ms
  connection-request-timeout: 5000 # 连接请求超时时间 单位:ms
  max-conn-total: 1024 # 最大连接数 单位:个
  max-conn-per-route: 500 # 最大连接路由数 单位:个
  global-config:
    db-config:
      index-prefix: dev_
    map-underscore-to-camel-case: true


# 自定义配置
tyt:
  key:
    xxtea: a3+@$@$!~!#222444
  # aes加密key
    aes: a3+@$@$!~!#22244
logging:
  level:
    root: debug
    com.teyuntong: debug
feign:
  client:
    config:
      default:
        logger-level: basic
custom:
  web:
    logging:
      ignore-paths:
        - /searchRecord/quotedBubble
        - /hall/search/bubbleCount
        - /hall/intelligence/bubbleCount
        - /hall/search/vary
        - /hall/search/distance
        - /hall/search/list
        - /hall/search/newList
        - /hall/intelligence/sortList