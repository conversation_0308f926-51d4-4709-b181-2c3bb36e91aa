package com.teyuntong.goods.search.adapter;

import cn.hutool.json.JSONUtil;
import com.teyuntong.goods.search.TestBase;
import com.teyuntong.goods.search.adapter.goods.SearchHallController;
import com.teyuntong.goods.search.service.biz.goods.dto.*;
import com.teyuntong.goods.search.service.biz.goods.service.TransportService;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.goods.search.service.biz.groupchat.service.GroupChatService;
import com.teyuntong.goods.search.service.biz.groupchat.vo.FindCityGroupQRVO;
import com.teyuntong.goods.search.service.biz.similarity.dto.SimilarityQueryDTO;
import com.teyuntong.goods.search.service.biz.similarity.vo.SimilarityVO;
import com.teyuntong.goods.search.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.search.service.rpc.goods.service.SearchHallRpcService;
import com.teyuntong.goods.search.service.rpc.similarity.service.SimilarityRpcService;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.inner.export.service.client.bidata.vo.BiRecommendVo;
import com.teyuntong.inner.export.service.client.common.bean.ResultMsgBean;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Set;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@Disabled
public class SearchHallServiceTest extends TestBase {

    @Autowired
    private SearchHallController searchHallController;
    @Autowired
    private TransportService transportService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private SearchHallRpcService searchHallRpcService;
    @Autowired
    private SimilarityRpcService similarityRpcService;

    @Autowired
    private GroupChatService groupChatService;

    @Test
    public void intelligenceRecommendListTest() {
        ResultMsgBean<List<BiRecommendVo>> resultMsgBean = new ResultMsgBean<>();
        BiRecommendVo biRecommendVo = new BiRecommendVo();
        biRecommendVo.setSrcMsgId(88822449L);
        resultMsgBean.setCode(200);
        resultMsgBean.setData(List.of(biRecommendVo));
        List<TransportVO> transportVOList = searchHallRpcService.afterRecommend(resultMsgBean, 1002000780L);
        log.info(JSONUtil.toJsonStr(transportVOList));
    }

    @Test
    void intelligenceSortListTest() {
        IntelligenceSortDTO intelligenceSortDTO = new IntelligenceSortDTO();
        EsSearchExtra esSearchExtra = new EsSearchExtra();
        esSearchExtra.setFirstSearchTime(new Date().getTime());

        intelligenceSortDTO.setQueryType(0);

        intelligenceSortDTO.setStartCoord("-2795.87,4109.8");
//        intelligenceSortDTO.setDestCoord("723.63,3924.65");
        intelligenceSortDTO.setStartDistance("300");
        intelligenceSortDTO.setStartArea("和田地区");
        intelligenceSortDTO.setStartCity("和田地区");
        intelligenceSortDTO.setStartProvinc("新疆");
//        intelligenceSortDTO.setDestArea("东港区");
//        intelligenceSortDTO.setDestCity("日照市");
//        intelligenceSortDTO.setDestProvinc("山东");
        intelligenceSortDTO.setEsSearchExtra(esSearchExtra);
        intelligenceSortDTO.setDestDistance("500");
        intelligenceSortDTO.setUserId(1002000780L);

        WebResult webResult = searchHallController.intelligenceSortList(intelligenceSortDTO);

        System.out.println(JSONUtil.toJsonStr(webResult));

    }

    @Test
    void bubbleCountTest() {
        IntelligenceSortDTO intelligenceSortDTO = new IntelligenceSortDTO();
        EsSearchExtra esSearchExtra = new EsSearchExtra();

        intelligenceSortDTO.setQueryType(0);
        intelligenceSortDTO.setStartProvinc("重庆");
//        intelligenceSortDTO.setStartCity("北京市");
        intelligenceSortDTO.setDestProvinc("北京");
//        intelligenceSortDTO.setDestCity("北京市");
        intelligenceSortDTO.setEsSearchExtra(esSearchExtra);
        WebResult webResult = searchHallController.bubbleCount(intelligenceSortDTO);
        System.out.println(JSONUtil.toJsonStr(webResult));
    }

    @Test
    void searchHallTest() throws Exception {
        BaseTransportSearchDTO baseTransportSearchDTO = new BaseTransportSearchDTO();

//        baseTransportSearchDTO.setStartCoord("-2795.87,4109.8");
        baseTransportSearchDTO.setStartDistance("100");
//        baseTransportSearchDTO.setDestDistance("300");
        baseTransportSearchDTO.setQueryType(1);

//        baseTransportSearchDTO.setStartLoadingTime(1731988800000L);
//        baseTransportSearchDTO.setEndLoadingTime(1732161600000L);
        baseTransportSearchDTO.setQueryType(0);
        baseTransportSearchDTO.setQuerySign(0L);

//        baseTransportSearchDTO.setStartCoord("-2967.87,3597.84");
//        intelligenceSortDTO.setDestCoord("723.63,3924.65");
        baseTransportSearchDTO.setStartDistance("100");
//        baseTransportSearchDTO.setStartArea("阿里地区");
//        baseTransportSearchDTO.setStartCity("阿里地区");
        baseTransportSearchDTO.setStartProvinc("北京");
//        baseTransportSearchDTO.setStartProvinc("北京");


        mockMvc.perform(post("/hall/search/newList")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("x-app-client-sign", "123")
                        .header("x-app-client-version", "1.0")
                        .header("x-app-client-id", "1002001059")
                        .header("x-app-os-version", "1234")
                        .content(JSONUtil.toJsonStr(baseTransportSearchDTO)))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }

    @Test
    void recommendTest() throws Exception {
        BaseTransportSearchDTO baseTransportSearchDTO = new BaseTransportSearchDTO();

        baseTransportSearchDTO.setStartProvinc("北京");

        mockMvc.perform(post("/hall/recommend/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("x-app-client-sign", "123")
                        .header("x-app-client-version", "1.0")
                        .header("x-app-client-id", "1002001059")
                        .header("x-app-os-version", "1234")
                        .content(JSONUtil.toJsonStr(baseTransportSearchDTO)))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }

    @Test
    void varyTest() throws Exception {
        TransportVaryDTO transportVaryDTO = new TransportVaryDTO();

        transportVaryDTO.setMaxId(1042730l);

        mockMvc.perform(post("/hall/search/vary")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("x-app-client-sign", "123")
                        .header("x-app-client-version", "1.0")
                        .header("x-app-client-id", "1002001059")
                        .header("x-app-os-version", "1234")
                        .content(JSONUtil.toJsonStr(transportVaryDTO)))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }

    @Test
    void shortRangeTest() throws Exception {
        ShortRangeDTO shortRangeDTO = new ShortRangeDTO();

        shortRangeDTO.setStartProvinc("北京");
        shortRangeDTO.setStartCity("北京市");
        shortRangeDTO.setStartDistance("100");
        shortRangeDTO.setStartCoord("448.69,4418.68");

        mockMvc.perform(post("/hall/short/range")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("x-app-client-sign", "123")
                        .header("x-app-client-version", "1.0")
                        .header("x-app-client-id", "1002001059")
                        .header("x-app-os-version", "1234")
                        .content(JSONUtil.toJsonStr(shortRangeDTO)))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }

    @Test
    void shortProvinceTest() throws Exception {
        ShortProvinceDTO shortProvinceDTO = new ShortProvinceDTO();

        shortProvinceDTO.setStartProvinc("北京");


        mockMvc.perform(post("/hall/short/province")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("x-app-client-sign", "123")
                        .header("x-app-client-version", "1.0")
                        .header("x-app-client-id", "1002001059")
                        .header("x-app-os-version", "1234")
                        .content(JSONUtil.toJsonStr(shortProvinceDTO)))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }

    @Test
    void routeDestTest() throws Exception {
        SameRouteDestDTO sameRouteDestDTO = new SameRouteDestDTO();

        sameRouteDestDTO.setStartProvinc("北京");
        sameRouteDestDTO.setStartCity("北京市");
        sameRouteDestDTO.setDestProvinc("上海");
        sameRouteDestDTO.setDestCity("上海市");
        sameRouteDestDTO.setGoodsId(88821636L);


        mockMvc.perform(post("/hall/same/routeDest")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("x-app-client-sign", "123")
                        .header("x-app-client-version", "1.0")
                        .header("x-app-client-id", "1002001059")
                        .header("x-app-os-version", "1234")
                        .content(JSONUtil.toJsonStr(sameRouteDestDTO)))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }


    @Test
    void hurryTest() throws Exception {
        TransportExposureQueryDTO sameRouteDestDTO = new TransportExposureQueryDTO();

        sameRouteDestDTO.setStartProvinc("北京");
        sameRouteDestDTO.setStartDistance("500");
        sameRouteDestDTO.setStartCoord("449.33,4419.12");
        sameRouteDestDTO.setQueryType(1);
        sameRouteDestDTO.setQuerySign(0L);
        sameRouteDestDTO.setMinTsId(215282382L);
        sameRouteDestDTO.setMaxTsId(215282382L);
        sameRouteDestDTO.setExposureType(2);


        mockMvc.perform(post("/exposure/getHurryList")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("x-app-client-sign", "123")
                        .header("x-app-client-version", "1.0")
                        .header("x-app-client-id", "1002001059")
                        .header("x-app-os-version", "1234")
                        .content(JSONUtil.toJsonStr(sameRouteDestDTO)))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }


    @Test
    void intelligenceRecommendList() throws Exception {
//        BaseTransportSearchDTO baseTransportSearchDTO = new BaseTransportSearchDTO();

//        baseTransportSearchDTO.setStartProvinc("北京");
//        baseTransportSearchDTO.setUserId(1002000780L);

        mockMvc.perform(post("/hall/intelligence/recommend")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("x-app-client-sign", "123")
                        .header("x-app-client-version", "1.0")
                        .header("x-app-client-id", "1002001059")
                        .header("x-app-os-version", "1234")
                        .header("x-app-ticket", "b24328480a708ce0d87ee532141b032b")
                        .header("x-app-user-id", "1002000780"))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }

    @Test
    void similarList() throws Exception {
        SimilarityQueryDTO similarityQueryDTO = new SimilarityQueryDTO();
        similarityQueryDTO.setSimilarityCode("14afe9139d91339f75f4e312f25d0af6");
        similarityQueryDTO.setSimilarityFirstId(1L);
        List<SimilarityVO> similarityList = similarityRpcService.getSimilarityList(similarityQueryDTO);
        log.info("similarityList:{}", JSONUtil.toJsonStr(similarityList));
    }


    @Test
    void redisTest() {

        String cacheKey = RedisKeyConstant.SHIELD_SHIPPER_KEY + 1002001059L;

//        List<Long> list = redisUtil.hashValues(cacheKey, Long.class);
        redisUtil.delete(cacheKey);
        Long id = 1002001059L;
        BaseTransportSearchDTO dto = new BaseTransportSearchDTO();
        dto.setUserId(1002001059L);
        BaseTransportSearchDTO dto2 = new BaseTransportSearchDTO();
        dto2.setUserId(5L);
        BaseTransportSearchDTO dto3 = new BaseTransportSearchDTO();
        dto3.setUserId(566L);
//        redisUtil.addSet(cacheKey, 1002001059L,10020010592L);
//        redisUtil.addSet(cacheKey, 1002001053L);
//        redisUtil.addSet(cacheKey, 1002001053L);
//        redisUtil.addSet(cacheKey, 1002001054L);
        redisUtil.addSet(cacheKey, dto);
        redisUtil.addSet(cacheKey, dto2);
        redisUtil.addSet(cacheKey, dto3);
        Set<BaseTransportSearchDTO> baseTransportSearchDTOS = redisUtil.membersSet(cacheKey, BaseTransportSearchDTO.class);
        System.out.println(JSONUtil.toJsonStr(baseTransportSearchDTOS));
//        System.out.println(list.get(0));
    }


    @Test
    void test43242() {

        FindCityGroupQRVO result = groupChatService.findCityGroupQR("上海上海市", "上海", 10001L);

        groupChatService.reportedCity("上海上海市", 10001L);

        System.out.println(1);
    }


}
