package com.teyuntong.goods.search.adapter;

import com.teyuntong.goods.search.TestBase;
import com.teyuntong.goods.search.client.transport.dto.SimilarityGoodsDTO;
import com.teyuntong.goods.search.client.transport.service.SimilarityGoodsRpcService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 *
 *
 * <AUTHOR>
 * @since 2025-07-13 12:35
 */
@Slf4j
public class SimilarityGoodsRpcServiceTest extends TestBase {
    @Autowired
    private SimilarityGoodsRpcService similarityGoodsRpcService;

    @Test
    public void batchSelectSimilarityGoodsIsTop() {
        List<SimilarityGoodsDTO> list = new ArrayList<>();
        SimilarityGoodsDTO dto = new SimilarityGoodsDTO();
        dto.setSrcMsgId(86775740L);
        dto.setSimilarityCode("a78fdf63da18a12b96f1a268c4b5585f");
        list.add(dto);

        SimilarityGoodsDTO dto2 = new SimilarityGoodsDTO();
        dto2.setSrcMsgId(86775741L);
        dto2.setSimilarityCode("213da3ad0d50d95cabfeaf7d5155b991");
        list.add(dto2);

        SimilarityGoodsDTO dto3 = new SimilarityGoodsDTO();
        dto3.setSrcMsgId(106578606L);
        dto3.setSimilarityCode("cd485b082fb3cb0eb19d12f53cf71ec3");
        list.add(dto3);

        SimilarityGoodsDTO dto4 = new SimilarityGoodsDTO();
        dto4.setSrcMsgId(106578605L);
        dto4.setSimilarityCode("cd485b082fb3cb0eb19d12f53cf71ec3");
        list.add(dto4);

        StopWatch watch = new StopWatch();
        watch.start();
        Map<Long, Optional<Boolean>> longBooleanMap = similarityGoodsRpcService.batchSelectGoodsIsTop(list);
        watch.stop();
        log.info("result:{}, {}", longBooleanMap, watch.getTotalTimeMillis());
    }
}
