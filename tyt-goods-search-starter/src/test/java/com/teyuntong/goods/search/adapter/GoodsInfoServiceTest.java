package com.teyuntong.goods.search.adapter;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.teyuntong.goods.search.TestBase;
import com.teyuntong.goods.search.adapter.goods.SearchHallController;
import com.teyuntong.goods.search.service.biz.goods.dto.GoodsInfoRecommendDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.GoodsPhoneDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.GoodsStatusDTO;
import com.teyuntong.goods.search.service.biz.goods.service.TransportService;
import com.teyuntong.goods.search.service.common.error.GoodsSearchErrorCode;
import com.teyuntong.goods.search.service.rpc.goods.service.SearchRecordRpcService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.Base64Utils;

import java.nio.charset.StandardCharsets;
import java.util.Date;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@Disabled
public class GoodsInfoServiceTest extends TestBase {

    @Autowired
    private SearchHallController searchHallController;
    @Autowired
    private TransportService transportService;
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SearchRecordRpcService searchRecordRpcService;


    @Test
    void recommendTest() throws Exception {
        GoodsInfoRecommendDTO goodsInfoRecommendDTO = new GoodsInfoRecommendDTO();

        goodsInfoRecommendDTO.setUserId(123L);
        goodsInfoRecommendDTO.setGoodsId(88821992L);

        mockMvc.perform(post("/goods/info/recommend")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("x-app-client-sign", "123")
                        .header("x-app-client-version", "1.0")
                        .header("x-app-client-id", "1002001059")
                        .header("x-app-os-version", "1234")
                        .content(JSONUtil.toJsonStr(goodsInfoRecommendDTO)))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }

    @Test
    void getGoodsStatusTest() throws Exception {
        GoodsStatusDTO goodsStatusDTO = new GoodsStatusDTO();
        goodsStatusDTO.setSrcMsgId(88822285L);
        goodsStatusDTO.setTsOrderNo("24121200000011");

        LoginUserDTO loginUserDTO = new LoginUserDTO();
        loginUserDTO.setUserId(1002000094L);
        loginUserDTO.setUserName("哈哈");

        mockMvc.perform(post("/goods/info/goodsStatus")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("x-app-client-sign", "123")
                        .header("x-app-client-version", "1.0")
                        .header("x-app-client-id", "1002001059")
                        .header("x-app-os-version", "1234")
                        .header("x-app-user-id", "1002000780")
                        .header("x-app-login-user", Base64Utils.encodeToString(JSONUtil.toJsonStr(loginUserDTO).getBytes()))
                        .content(JSONUtil.toJsonStr(goodsStatusDTO)))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }

    @Test
    void getGoodsPhoneTest() throws Exception {
        GoodsPhoneDTO goodsPhoneDTO = new GoodsPhoneDTO();
        goodsPhoneDTO.setSrcMsgId(88822370L);
        goodsPhoneDTO.setNoCarBlock(1);

        LoginUserDTO loginUserDTO = new LoginUserDTO();
        loginUserDTO.setUserId(1002000780L);
        loginUserDTO.setUserName("哈哈");
        loginUserDTO.setCtime(DateUtil.offsetDay(new Date(), 33));

        mockMvc.perform(post("/goods/info/getPhone")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header("x-app-client-sign", "1")
                        .header("x-app-client-version", "1.0")
                        .header("x-app-client-id", "1002001059")
                        .header("x-app-os-version", "1234")
                        .header("x-app-user-id", "1002000780")
                        .header("x-app-login-user", Base64Utils.encodeToString(JSONUtil.toJsonStr(loginUserDTO).getBytes()))
                        .content(JSONUtil.toJsonStr(goodsPhoneDTO)))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8)))
                .andExpect(status().isOk());
        Assertions.assertTrue(true);
    }
    @Test
    void getGoodsPhone1Test() throws Exception {
        String reason = "好好好";
        String errorMsg = "当前账号因<font color='#F52F3E'>【" + reason + "】</font>无法接单优车货源，如有疑问请联系客服。";

        throw BusinessException.createException(GoodsSearchErrorCode.SUPERIOR_CAR_SIGN_BLACK_ERROR.getCode(), errorMsg);
    }

    @Test
    void test242432() {
        String freeCommissionBubble = searchRecordRpcService.getFreeCommissionBubble(1000003978L);
        System.out.println(1);
    }


}
