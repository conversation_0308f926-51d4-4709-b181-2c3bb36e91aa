<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.teyuntong</groupId>
        <artifactId>tyt-goods-search</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.tyt</groupId>
    <artifactId>tyt-goods-search-starter</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <!--
       根据不同的profile,开启不同的功能
       由于adapter、schedule、mq的功能相互独立，没有依赖关系，所以通过profile控制引入的依赖，即可实现对应功能的开启/关闭

       all -> adapter、schedule、mq 的功能全部开启
       adapter -> 只开启adapter的功能
       schedule -> 只开启schedule的功能
       mq -> 只开启mq的功能
    -->
    <profiles>
        <profile>
            <id>all</id>
            <dependencies>
                <dependency>
                    <groupId>com.teyuntong</groupId>
                    <artifactId>tyt-goods-search-adapter</artifactId>
                    <version>1.0.0-SNAPSHOT</version>
                </dependency>
                <dependency>
                    <groupId>com.teyuntong</groupId>
                    <artifactId>tyt-goods-search-schedule</artifactId>
                    <version>1.0.0-SNAPSHOT</version>
                </dependency>
                <dependency>
                    <groupId>com.teyuntong</groupId>
                    <artifactId>tyt-goods-search-mq</artifactId>
                    <version>1.0.0-SNAPSHOT</version>
                </dependency>
            </dependencies>

            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>

        <profile>
            <id>adapter</id>
            <dependencies>
                <dependency>
                    <groupId>com.teyuntong</groupId>
                    <artifactId>tyt-goods-search-adapter</artifactId>
                    <version>1.0.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>schedule</id>
            <dependencies>
                <dependency>
                    <groupId>com.teyuntong</groupId>
                    <artifactId>tyt-goods-search-schedule</artifactId>
                    <version>1.0.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>mq</id>
            <dependencies>
                <dependency>
                    <groupId>com.teyuntong</groupId>
                    <artifactId>tyt-goods-search-mq</artifactId>
                    <version>1.0.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>

        <profile>
            <id>mq-schedule</id>
            <dependencies>
                <dependency>
                    <groupId>com.teyuntong</groupId>
                    <artifactId>tyt-goods-search-mq</artifactId>
                    <version>1.0.0-SNAPSHOT</version>
                </dependency>
                <dependency>
                    <groupId>com.teyuntong</groupId>
                    <artifactId>tyt-goods-search-schedule</artifactId>
                    <version>1.0.0-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
