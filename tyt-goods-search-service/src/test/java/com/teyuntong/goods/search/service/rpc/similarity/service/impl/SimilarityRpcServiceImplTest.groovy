package com.teyuntong.goods.search.service.rpc.similarity.service.impl

import com.teyuntong.goods.search.service.biz.behavior.serivce.ShieldingShipperService
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO
import com.teyuntong.goods.search.service.biz.goods.service.TransportService
import com.teyuntong.goods.search.service.biz.similarity.dto.SimilarityQueryDTO
import com.teyuntong.goods.search.service.biz.similarity.service.SimilarityTransportService
import com.teyuntong.goods.search.service.biz.similarity.vo.SimilarityVO
import com.teyuntong.goods.search.service.remote.basic.ConfigRemoteService
import com.teyuntong.infra.common.redis.utils.RedisUtil
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when
/**
 * <AUTHOR>
 * @since 2024/07/25 13:10
 */
class SimilarityRpcServiceImplTest extends Specification {
    @Mock
    SimilarityTransportService similarityTransportService
    @Mock
    RedisUtil redisUtil
    @Mock
    ShieldingShipperService shieldingShipperService
    @Mock
    TransportService transportService
    @Mock
    ConfigRemoteService configRemoteService
    @InjectMocks
    SimilarityRpcServiceImpl similarityRpcServiceImpl

    def setup() {
        MockitoAnnotations.openMocks(this)
//        def mockStatic = Mockito.mockStatic(LoginHelper.class)
//        mockStatic.when(LoginHelper.&getUserId).thenReturn(1L)
    }

    def "测试相似货源列表-不走缓存"() {
        given:
        when(shieldingShipperService.getShieldingUserList(anyLong())).thenReturn([])
        when(similarityTransportService.getSimilarityList(any(SimilarityQueryDTO.class)))
                .thenReturn([new TransportDO(id: 1L)])
        when(configRemoteService.getStringValue(anyString())).thenReturn("4,5")

        when:
        List<SimilarityVO> result = similarityRpcServiceImpl.getSimilarityList(new SimilarityQueryDTO())

        then:
        result.get(0).id == 1L
    }

    def "测试相似货源列表-走缓存"() {
        given:
        when(shieldingShipperService.getShieldingUserList(anyLong())).thenReturn([1])
        when(redisUtil.getString(anyString())).thenReturn("[{\"id\":1}]")

        when:
        List<SimilarityVO> result = similarityRpcServiceImpl.getSimilarityList(new SimilarityQueryDTO())

        then:
        result.get(0).id == 1L
    }


}
