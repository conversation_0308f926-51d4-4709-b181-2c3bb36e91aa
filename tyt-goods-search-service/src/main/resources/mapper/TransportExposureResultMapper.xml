<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.exposure.mapper.TransportExposureResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.search.service.biz.exposure.entity.TransportExposureResultDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="src_msg_id" property="srcMsgId" />
        <result column="show_count" property="showCount" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, src_msg_id, show_count, create_time, modify_time
    </sql>

    <update id="updateExposureCount">
        update tyt_transport_exposure_result set show_count = show_count+1
        where src_msg_id in ( <foreach collection="srcMsgIdList" item="i" separator=","> #{i} </foreach> )
    </update>

    <insert id="saveExposureResult">
        insert ignore into tyt_transport_exposure_result
        ( user_id, src_msg_id, show_count, create_time)
        values
        <foreach collection="transportList" item="transport" separator=",">
            ( #{transport.userId}, #{transport.srcMsgId}, 1, now())
        </foreach>
    </insert>

</mapper>
