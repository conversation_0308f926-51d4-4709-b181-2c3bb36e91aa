<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.record.mapper.CallFeedbackLogMapper">

    <select id="getLastFeedback" resultType="com.teyuntong.goods.search.service.biz.record.entity.CallFeedbackLogDO">
        select id, src_msg_id, feedback_1
        from tyt_call_feedback_log
        where car_user_id = #{carUserId}
        and src_msg_id in ( <foreach collection="srcMsgIds" item="i" separator=","> #{i} </foreach> )
        order by id desc
    </select>
</mapper>
