<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.record.mapper.TransportDispatchViewMapper">

    <select id="getByUserAndSrcMsgId"
            resultType="com.teyuntong.goods.search.service.biz.record.entity.TransportDispatchViewDO">
        select *
        from tyt_transport_dispatch_view
        where car_user_id = #{userId}
          and src_msg_id = #{srcMsgId}
    </select>
</mapper>
