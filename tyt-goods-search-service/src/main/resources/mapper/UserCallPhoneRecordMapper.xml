<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.record.mapper.UserCallPhoneRecordMapper">


    <select id="getByUserAndSrcMsgId"
            resultType="com.teyuntong.goods.search.service.biz.record.entity.UserCallPhoneRecordDO">
        select *
        from tyt_user_call_phone_record
        where user_id = #{userId}
          and ts_id = #{srcMsgId}
        limit 1
    </select>
    <select id="getDistinctTsCountByUserId" resultType="java.lang.Integer">
        select count(distinct ts_id)
        from tyt_user_call_phone_record tucpr
        where tucpr.`user_id` = #{userId}
        <if test="startTime != null">
            and tucpr.`ctime` &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and tucpr.`ctime` &lt;= #{endTime}
        </if>
    </select>

    <select id="getDistinctTsCountBySrcMsgId" resultType="java.lang.Integer">
        select count(distinct user_id)
        from tyt_user_call_phone_record
        where ts_id = #{srcMsgId}
          and ctime >= CURDATE()
    </select>
</mapper>
