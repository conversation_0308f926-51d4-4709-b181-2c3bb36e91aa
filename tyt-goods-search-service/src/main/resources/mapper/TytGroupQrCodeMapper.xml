<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.group.mapper.TytGroupQrCodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.search.service.biz.group.entity.TytGroupQrCodeDO">
        <id column="id" property="id" />
        <result column="province_city" property="provinceCity" />
        <result column="group_name" property="groupName" />
        <result column="qr_code_url" property="qrCodeUrl" />
        <result column="qr_code_upload_time" property="qrCodeUploadTime" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, province_city, group_name, qr_code_url, qr_code_upload_time, status, create_time, modify_time
    </sql>

    <select id="getGroupQRCodeByProvinceCity" resultMap="BaseResultMap">
        select *
        from tyt_group_qr_code where province_city like concat('%',#{city},'%') and status = 1 and qr_code_upload_time >= #{startDate} and qr_code_url is not null order by qr_code_upload_time desc limit 1;
    </select>

</mapper>
