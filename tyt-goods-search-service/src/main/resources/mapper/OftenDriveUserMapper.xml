<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.route.mapper.OftenDriveUserMapper">

    <select id="getByUserId" resultType="com.teyuntong.goods.search.service.biz.route.entity.OftenDriveUserDO">
        select *
        from often_drive_user
        where user_id = ${userId}
    </select>
</mapper>
