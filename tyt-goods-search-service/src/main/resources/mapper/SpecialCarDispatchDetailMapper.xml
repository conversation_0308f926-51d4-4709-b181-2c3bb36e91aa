<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.record.mapper.SpecialCarDispatchDetailMapper">

    <select id="selectCountByUserAndGoodsId" resultType="java.lang.Integer">
        select count(*) from tyt_special_car_dispatch_detail where user_id = #{userId} and ts_id = #{goodsId} and dispatch_status = 1
    </select>
</mapper>
