<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.search.mapper.SearchDistanceConfigMapper">

    <!-- 返回搜索距离，优先取城市，不存在取省 -->
    <select id="getOptimalDistance" resultType="integer">
        select distance from tyt_search_distance_config
        where status = 1
        and (city = #{city} or city = '' and province = #{province})
        order by city desc
        limit 1
    </select>

</mapper>
