<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.dispatch.mapper.CustomFirstOrderRecordMapper">


    <select id="countFinishOrder" resultType="java.lang.Integer">
        select count(1)
        from tyt_custom_first_order_record
        where custom_phone = #{cellPhone}
          and first_finish_order_time is not null
    </select>
</mapper>
