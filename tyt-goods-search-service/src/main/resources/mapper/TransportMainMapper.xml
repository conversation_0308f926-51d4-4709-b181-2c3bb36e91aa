<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.goods.mapper.TransportMainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.search.service.biz.goods.entity.TransportMainDO">
        <id column="id" property="id"/>
        <result column="start_point" property="startPoint"/>
        <result column="dest_point" property="destPoint"/>
        <result column="task_content" property="taskContent"/>
        <result column="tel" property="tel"/>
        <result column="pub_time" property="pubTime"/>
        <result column="pub_qq" property="pubQq"/>
        <result column="nick_name" property="nickName"/>
        <result column="user_show_name" property="userShowName"/>
        <result column="status" property="status"/>
        <result column="source" property="source"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="upload_cellphone" property="uploadCellphone"/>
        <result column="resend" property="resend"/>
        <result column="start_coord" property="startCoord"/>
        <result column="dest_coord" property="destCoord"/>
        <result column="plat_id" property="platId"/>
        <result column="verify_flag" property="verifyFlag"/>
        <result column="price" property="price"/>
        <result column="user_id" property="userId"/>
        <result column="price_code" property="priceCode"/>
        <result column="start_coord_x" property="startCoordX"/>
        <result column="start_coord_y" property="startCoordY"/>
        <result column="dest_coord_x" property="destCoordX"/>
        <result column="dest_coord_y" property="destCoordY"/>
        <result column="start_detail_add" property="startDetailAdd"/>
        <result column="start_longitude" property="startLongitude"/>
        <result column="start_latitude" property="startLatitude"/>
        <result column="dest_detail_add" property="destDetailAdd"/>
        <result column="dest_longitude" property="destLongitude"/>
        <result column="dest_latitude" property="destLatitude"/>
        <result column="pub_date" property="pubDate"/>
        <result column="goods_code" property="goodsCode"/>
        <result column="weight_code" property="weightCode"/>
        <result column="weight" property="weight"/>
        <result column="length" property="length"/>
        <result column="wide" property="wide"/>
        <result column="high" property="high"/>
        <result column="is_superelevation" property="isSuperelevation"/>
        <result column="linkman" property="linkman"/>
        <result column="remark" property="remark"/>
        <result column="distance" property="distance"/>
        <result column="pub_goods_time" property="pubGoodsTime"/>
        <result column="tel3" property="tel3"/>
        <result column="tel4" property="tel4"/>
        <result column="display_type" property="displayType"/>
        <result column="hash_code" property="hashCode"/>
        <result column="is_car" property="isCar"/>
        <result column="user_type" property="userType"/>
        <result column="pc_old_content" property="pcOldContent"/>
        <result column="resend_counts" property="resendCounts"/>
        <result column="verify_photo_sign" property="verifyPhotoSign"/>
        <result column="user_part" property="userPart"/>
        <result column="start_city" property="startCity"/>
        <result column="src_msg_id" property="srcMsgId"/>
        <result column="start_provinc" property="startProvinc"/>
        <result column="start_area" property="startArea"/>
        <result column="dest_provinc" property="destProvinc"/>
        <result column="dest_city" property="destCity"/>
        <result column="dest_area" property="destArea"/>
        <result column="client_version" property="clientVersion"/>
        <result column="is_info_fee" property="isInfoFee"/>
        <result column="info_status" property="infoStatus"/>
        <result column="ts_order_no" property="tsOrderNo"/>
        <result column="release_time" property="releaseTime"/>
        <result column="reg_time" property="regTime"/>
        <result column="type" property="type"/>
        <result column="brand" property="brand"/>
        <result column="good_type_name" property="goodTypeName"/>
        <result column="good_number" property="goodNumber"/>
        <result column="is_standard" property="isStandard"/>
        <result column="match_item_id" property="matchItemId"/>
        <result column="android_distance" property="androidDistance"/>
        <result column="ios_distance" property="iosDistance"/>
        <result column="is_display" property="isDisplay"/>
        <result column="refer_length" property="referLength"/>
        <result column="refer_width" property="referWidth"/>
        <result column="refer_height" property="referHeight"/>
        <result column="refer_weight" property="referWeight"/>
        <result column="car_length" property="carLength"/>
        <result column="loading_time" property="loadingTime"/>
        <result column="begin_unload_time" property="beginUnloadTime"/>
        <result column="unload_time" property="unloadTime"/>
        <result column="car_min_length" property="carMinLength"/>
        <result column="car_max_length" property="carMaxLength"/>
        <result column="car_type" property="carType"/>
        <result column="begin_loading_time" property="beginLoadingTime"/>
        <result column="car_style" property="carStyle"/>
        <result column="work_plane_min_high" property="workPlaneMinHigh"/>
        <result column="work_plane_max_high" property="workPlaneMaxHigh"/>
        <result column="work_plane_min_length" property="workPlaneMinLength"/>
        <result column="work_plane_max_length" property="workPlaneMaxLength"/>
        <result column="climb" property="climb"/>
        <result column="order_number" property="orderNumber"/>
        <result column="evaluate" property="evaluate"/>
        <result column="special_required" property="specialRequired"/>
        <result column="similarity_code" property="similarityCode"/>
        <result column="similarity_first_id" property="similarityFirstId"/>
        <result column="similarity_first_info" property="similarityFirstInfo"/>
        <result column="tyre_exposed_flag" property="tyreExposedFlag"/>
        <result column="car_length_labels" property="carLengthLabels"/>
        <result column="shunting_quantity" property="shuntingQuantity"/>
        <result column="first_publish_type" property="firstPublishType"/>
        <result column="publish_type" property="publishType"/>
        <result column="info_fee" property="infoFee"/>
        <result column="exclusive_type" property="exclusiveType"/>
        <result column="is_delete" property="isDelete"/>
        <result column="total_score" property="totalScore"/>
        <result column="rank_level" property="rankLevel"/>
        <result column="is_show" property="isShow"/>
        <result column="refund_flag" property="refundFlag"/>
        <result column="source_type" property="sourceType"/>
        <result column="trade_num" property="tradeNum"/>
        <result column="auth_name" property="authName"/>
        <result column="label_json" property="labelJson"/>
        <result column="guarantee_goods" property="guaranteeGoods"/>
        <result column="credit_retop" property="creditRetop"/>
        <result column="sort_type" property="sortType"/>
        <result column="priority_recommend_expire_time" property="priorityRecommendExpireTime"/>
        <result column="excellent_goods" property="excellentGoods"/>
        <result column="driver_driving" property="driverDriving"/>
        <result column="load_cell_phone" property="loadCellPhone"/>
        <result column="unload_cell_phone" property="unloadCellPhone"/>
        <result column="cargo_owner_id" property="cargoOwnerId"/>
        <result column="tec_service_fee" property="tecServiceFee"/>
        <result column="machine_remark" property="machineRemark"/>
        <result column="excellent_card_id" property="excellentCardId"/>
        <result column="invoice_transport" property="invoiceTransport"/>
        <result column="additional_price" property="additionalPrice"/>
        <result column="enterprise_tax_rate" property="enterpriseTaxRate"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, start_point, dest_point, task_content, tel, pub_time, pub_qq, nick_name, user_show_name, status, source,
        ctime, mtime, upload_cellphone, resend, start_coord, dest_coord, plat_id, verify_flag, price, user_id,
        price_code, start_coord_x, start_coord_y, dest_coord_x, dest_coord_y, start_detail_add, start_longitude,
        start_latitude, dest_detail_add, dest_longitude, dest_latitude, pub_date, goods_code, weight_code, weight,
        length, wide, high, is_superelevation, linkman, remark, distance, pub_goods_time, tel3, tel4, display_type,
        hash_code, is_car, user_type, pc_old_content, resend_counts, verify_photo_sign, user_part, start_city,
        src_msg_id, start_provinc, start_area, dest_provinc, dest_city, dest_area, client_version, is_info_fee,
        info_status, ts_order_no, release_time, reg_time, type, brand, good_type_name, good_number, is_standard,
        match_item_id, android_distance, ios_distance, is_display, refer_length, refer_width, refer_height,
        refer_weight, car_length, loading_time, begin_unload_time, unload_time, car_min_length, car_max_length,
        car_type, begin_loading_time, car_style, work_plane_min_high, work_plane_max_high, work_plane_min_length,
        work_plane_max_length, climb, order_number, evaluate, special_required, similarity_code, similarity_first_id,
        similarity_first_info, tyre_exposed_flag, car_length_labels, shunting_quantity, first_publish_type,
        publish_type, info_fee, exclusive_type, is_delete, total_score, rank_level, is_show, refund_flag, source_type,
        trade_num, auth_name, label_json, guarantee_goods, credit_retop, sort_type, priority_recommend_expire_time,
        excellent_goods, driver_driving, load_cell_phone, unload_cell_phone, cargo_owner_id, tec_service_fee,
        machine_remark, excellent_card_id, invoice_transport, additional_price, enterprise_tax_rate
    </sql>

    <!-- 根据货源id批量查询 -->
    <select id="getBySrcMsgIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tyt_transport_main
        where id in
        <foreach collection="srcMsgIds" item="srcMsgId" open="(" separator="," close=")">
            #{srcMsgId}
        </foreach>
    </select>

    <!-- 过滤有效货源id -->
    <select id="filterValidTransport" resultType="long">
        select id from tyt_transport_main
        where status = 1
        and id in
        <foreach collection="srcMsgIds" item="srcMsgId" open="(" separator="," close=")">
            #{srcMsgId}
        </foreach>
        and ctime > curdate()
    </select>

    <select id="getFreeCommissionTransportBySrcMsgIdList" resultType="java.lang.Long">
        select src_msg_id from tyt_transport_tec_service_fee
        where src_msg_id in
        <foreach collection="srcMsgIds" item="srcMsgId" open="(" separator="," close=")">
            #{srcMsgId}
        </foreach>
        and create_time > curdate()
        and (member_after_fee = 0 or no_member_after_fee = 0)
    </select>

</mapper>
