<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.route.mapper.OftenDriveRouteUserAddressMapper">

    <select id="getByUserId" resultType="com.teyuntong.goods.search.service.biz.route.entity.OftenDriveRouteUserAddressDO">
        select * from often_drive_route_user_address
        where user_id = #{userId}
    </select>

    <select id="getByRouteUserId" resultType="com.teyuntong.goods.search.service.biz.route.entity.OftenDriveRouteUserAddressDO">
        select * from often_drive_route_user_address
        where route_user_id = #{routeUserId}
    </select>

    <delete id="deleteByRouteUserId">
        delete from often_drive_route_user_address where route_user_id = #{routeUserId}
    </delete>

</mapper>
