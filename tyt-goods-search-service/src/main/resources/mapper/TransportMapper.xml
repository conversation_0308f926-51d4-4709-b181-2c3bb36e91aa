<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.goods.mapper.TransportMapper">

    <select id="getSimilarityList" resultType="com.teyuntong.goods.search.service.biz.goods.entity.TransportDO">
        SELECT * FROM tyt_transport
        WHERE `status` = 1
        AND display_type = 1
        AND is_display = 1
        AND similarity_code = #{similarityCode}
        <if test="similarityFirstId != null">
            AND src_msg_id != #{similarityFirstId}
        </if>
        <if test="excludeUserIds != null and excludeUserIds.size() > 0">
            AND user_id NOT IN
            <foreach collection="excludeUserIds" item="item" separator="," open="(" close=")">#{item}</foreach>
        </if>

        LIMIT #{pageSize}
    </select>

    <select id="getExposureList" resultType="com.teyuntong.goods.search.service.biz.goods.entity.TransportDO">
        SELECT tr.*
        FROM tyt_transport_exposure_daily te
        JOIN tyt_transport tr ON te.src_msg_id = tr.src_msg_id
        WHERE te.status = 1
        AND te.create_time >= CURDATE()
        <if test="maxTsId != null and maxTsId > 0">
            AND ( tr.id &gt; #{maxTsId} OR tr.id &lt; #{minTsId} )
        </if>
        <include refid="baseWhereQuery"/>
        ORDER BY te.id DESC
        LIMIT ${pageSize}
    </select>

    <select id="getIntentionList" resultType="com.teyuntong.goods.search.service.biz.goods.entity.TransportDO">
        SELECT tr.*
        FROM tyt_transport_intention_daily te
        JOIN tyt_transport tr ON te.src_msg_id = tr.src_msg_id
        WHERE te.status = 1
        AND te.create_time >= CURDATE()
        <if test="maxTsId != null and maxTsId > 0">
            AND ( tr.id &gt; #{maxTsId} OR tr.id &lt; #{minTsId} )
        </if>
        <include refid="baseWhereQuery"/>
        ORDER BY te.id DESC
        LIMIT ${pageSize}
    </select>

    <select id="searchHallList" resultType="com.teyuntong.goods.search.service.biz.goods.entity.TransportDO">
        select * from tyt_transport tr
        <where>
            <include refid="baseWhereQuery"/>
            ORDER BY tr.id DESC
            LIMIT ${pageSize}
        </where>
    </select>

    <select id="selectBySrcMsgIds"
            resultType="com.teyuntong.goods.search.service.biz.goods.entity.TransportDO">
        SELECT * FROM tyt_transport WHERE status = 1 and src_msg_id in
        <foreach collection="srcMsgIdList" item="srcMsgId" separator="," open="(" close=")">
            #{srcMsgId}
        </foreach>
        order by id DESC
    </select>

    <select id="selectValidByTsIds"
            resultType="com.teyuntong.goods.search.service.biz.goods.entity.TransportDO">
        SELECT * FROM tyt_transport
        WHERE status = 1 AND display_type = 1 AND is_display = 1
        AND id IN
        <foreach collection="tsIdList" item="tsId" separator="," open="(" close=")">
            #{tsId}
        </foreach>
        ORDER BY id DESC
    </select>

    <select id="selectByTsIds"
            resultType="com.teyuntong.goods.search.service.biz.goods.entity.TransportDO">
        SELECT * FROM tyt_transport
        WHERE id IN
        <foreach collection="tsIdList" item="tsId" separator="," open="(" close=")">
            #{tsId}
        </foreach>
        ORDER BY id DESC
    </select>

    <select id="selectSrcMsgIdByTsIds" resultType="com.teyuntong.goods.search.service.biz.goods.entity.TransportDO">
        SELECT id, src_msg_id FROM tyt_transport
        WHERE id IN
        <foreach collection="tsIdList" item="tsId" separator="," open="(" close=")">
            #{tsId}
        </foreach>
    </select>

    <select id="getBySrcMsgIdAndStatus"
            resultType="com.teyuntong.goods.search.service.biz.goods.entity.TransportDO">
        SELECT *
        FROM tyt_transport
        WHERE status = 1
          AND src_msg_id = #{srcMsgId}
        ORDER BY id DESC
        limit 1
    </select>

    <select id="getExposureOfScore"
            resultType="com.teyuntong.goods.search.service.biz.goods.entity.TransportDO">
        SELECT tr.*
        FROM tyt_transport_exposure_daily te
        JOIN tyt_transport tr ON te.src_msg_id = tr.src_msg_id
        WHERE te.status = 1
        AND te.create_time >= CURDATE()
        <include refid="baseWhereQuery"/>
        ORDER BY tr.total_score DESC,tr.id DESC limit 1

    </select>
    <select id="getByTsOrderNoAndStatus"
            resultType="com.teyuntong.goods.search.service.biz.goods.entity.TransportDO">
        select tr.*
        from tyt_transport tr
        where tr.status = 1
          and tr.ts_order_no = #{tsOrderNo}
        order by id desc
        limit 1
    </select>
    <select id="searchHallCount" resultType="java.lang.Long">
        select count(*) from tyt_transport tr
        <where>
            <if test="maxTsId != null">
                and tr.id &gt; #{maxTsId}
            </if>
            <include refid="baseWhereQuery"/>
        </where>
    </select>

    <select id="searchHallCountDetail" resultType="com.teyuntong.goods.search.service.biz.goods.entity.TransportDO">
        select * from tyt_transport tr
        <where>
            <if test="maxTsId != null">
                and tr.id &gt; #{maxTsId}
            </if>
            <include refid="baseWhereQuery"/>
        </where>
        ORDER BY tr.id DESC limit 1
    </select>

    <!-- 批量货源每个货源的最新一条记录的价格 -->
    <select id="getLatestPrice" resultType="com.teyuntong.goods.search.service.biz.goods.entity.TransportDO">
        SELECT src_msg_id, price
        FROM tyt_transport
        WHERE src_msg_id IN ( <foreach collection="srcMsgIds" separator="," item="i"> #{i} </foreach> )
        AND status = 1
    </select>


    <sql id="baseWhereQuery">
        and tr.status = 1
        and tr.display_type = '1'
        and tr.is_display = 1
        and tr.is_show = 1
        <!-- 自动加载新增的 -->
        <if test="queryType != null">
            <if test="queryType == 0">
                <if test="querySign != null">
                    and tr.id &gt; #{querySign}
                </if>
            </if>
            <!-- 默认无条件 -->
            <if test="queryType == 1">
            </if>
            <!-- 向上滑加载历史 -->
            <if test="queryType == 2">
                <if test="querySign != null">
                    and tr.id &lt; #{querySign}
                </if>
                <if test="minTsId != null">
                    and tr.id &lt; #{minTsId}
                </if>
            </if>
        </if>
        <if test="excellentGoods != null">
            and tr.excellent_goods = #{excellentGoods}
        </if>
        <choose>
            <when test="startCoordX != null and startCoordY != null">
                and tr.start_coord_x between #{startCoordX} - #{startRange} and #{startCoordX} + #{startRange}
                and tr.start_coord_y between #{startCoordY} - #{startRange} and #{startCoordY} + #{startRange}
            </when>
            <otherwise>
                <if test="startProvinc != null and startProvinc != ''">
                    and tr.start_provinc = #{startProvinc}
                </if>
                <if test="startCity != null and startCity != ''">
                    and tr.start_city = #{startCity}
                </if>
                <if test="startArea != null and startArea != ''">
                    and tr.start_area = #{startArea}
                </if>
            </otherwise>
        </choose>
        <choose>
            <when test="destCoordX != null and destCoordY != null">
                and tr.dest_coord_x between #{destCoordX} - #{destRange} and #{destCoordX} + #{destRange}
                and tr.dest_coord_y between #{destCoordY} - #{destRange} and #{destCoordY} + #{destRange}
            </when>
            <otherwise>
                <if test="destProvinc != null and destProvinc != ''">
                    and tr.dest_provinc = #{destProvinc}
                </if>
                <if test="destCity != null and destCity != ''">
                    and tr.dest_city = #{destCity}
                </if>
                <if test="destArea != null and destArea != ''">
                    and tr.dest_area = #{destArea}
                </if>
            </otherwise>
        </choose>
        <if test="startWeight != null">
            and tr.weight &gt; 0 and tr.refer_weight &gt;= #{startWeight}
        </if>
        <if test="endWeight != null">
            and tr.weight &gt; 0 and tr.refer_weight &lt;= #{endWeight}
        </if>
        <if test="minLength != null">
            and tr.length &gt; 0 and tr.refer_length &gt;= #{minLength}
        </if>
        <if test="maxLength != null">
            and tr.length &gt; 0 and tr.refer_length &lt;= #{maxLength}
        </if>
        <if test="minWidth != null">
            and tr.wide &gt; 0 and tr.refer_width &gt;= #{minWidth}
        </if>
        <if test="maxWidth != null">
            and tr.wide &gt; 0 and tr.refer_width &lt;= #{maxWidth}
        </if>
        <if test="minHeight != null">
            and tr.high &gt; 0 and tr.refer_height &gt;= #{minHeight}
        </if>
        <if test="maxHeight != null">
            and tr.high &gt; 0 and tr.refer_height &lt;= #{maxHeight}
        </if>
        <if test="minPrice != null">
            and tr.price &gt;= #{minPrice}
        </if>
        <if test="maxPrice != null">
            and tr.price &lt;= #{maxPrice}
        </if>
        <choose>
            <when test="startLoadingTime == null and endLoadingTime == null">
                <if test="queryNullLoadingTime != null and queryNullLoadingTime == true">
                    and tr.loading_time IS NULL
                </if>
            </when>
            <otherwise>
                <trim prefix="and (" suffix=")" prefixOverrides="OR">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <if test="startLoadingTime != null">
                            and tr.loading_time &gt;= FROM_UNIXTIME(#{startLoadingTime} / 1000)
                        </if>
                        <if test="endLoadingTime != null">
                            and tr.loading_time &lt;= FROM_UNIXTIME(#{endLoadingTime} / 1000)
                        </if>
                    </trim>
                    <if test="queryNullLoadingTime != null and queryNullLoadingTime == true">
                        OR tr.loading_time IS NULL
                    </if>
                </trim>
            </otherwise>
        </choose>
        <if test="goodsName != null and goodsName != ''">
            and
            <foreach collection="goodsName.split(',')" item="item" open="(" close=")" separator=" or ">
                tr.task_content like concat('%',#{item},'%')
            </foreach>
        </if>
        <if test="priceFlag == 0">
            and ( tr.price = '' or tr.price is null or tr.price = '0')
        </if>
        <if test="priceFlag == 1">
            and tr.price &gt;= 1
        </if>
        <if test="publishType != null">
            and tr.publish_type = #{publishType}
        </if>
        <if test="refundFlag != null">
            and tr.refund_flag = #{refundFlag}
        </if>
        <if test="excludeSrcMsgIds != null and excludeSrcMsgIds.size() > 0">
            and tr.src_msg_id not in
            <foreach collection="excludeSrcMsgIds" item="item" separator="," open="(" close=")">#{item}</foreach>
        </if>
        <if test="excludeUserIds != null and excludeUserIds.size() > 0">
            and tr.user_id not in
            <foreach collection="excludeUserIds" item="item" separator="," open="(" close=")">#{item}</foreach>
        </if>

        <if test="useSrcMsgIds != null and useSrcMsgIds.size() > 0">
            and tr.src_msg_id in
            <foreach collection="useSrcMsgIds" item="item" separator="," open="(" close=")">#{item}</foreach>
        </if>
    </sql>

</mapper>
