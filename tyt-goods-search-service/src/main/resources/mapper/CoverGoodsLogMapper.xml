<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.behavior.mapper.CoverGoodsLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.search.service.biz.behavior.entity.CoverGoodsLogDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="ts_id" property="tsId" />
        <result column="goods_level" property="goodsLevel" />
        <result column="view_index" property="viewIndex" />
        <result column="hit_rule" property="hitRule" />
        <result column="priority_recommend_expire_time" property="priorityRecommendExpireTime" />
        <result column="x_time_in_seconds" property="xTimeInSeconds" />
        <result column="y_time_in_seconds" property="yTimeInSeconds" />
        <result column="cover_interval" property="coverInterval" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, ts_id, goods_level, view_index, hit_rule, priority_recommend_expire_time, x_time_in_seconds, y_time_in_seconds, cover_interval, create_time, modify_time
    </sql>

    <select id="getCoverGoodsIds" resultType="long">
        select ts_id from tyt_cover_goods_log
        where user_id = #{userId}
        and ts_id in ( <foreach collection="srcMsgIds" item="i" separator=","> #{i} </foreach> )
    </select>
</mapper>
