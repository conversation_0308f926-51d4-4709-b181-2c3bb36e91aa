<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.enterprise.mapper.TransportEnterpriseLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.search.service.biz.enterprise.entity.TransportEnterpriseLogDO">
        <id column="id" property="id" />
        <result column="src_msg_id" property="srcMsgId" />
        <result column="enterprise_id" property="enterpriseId" />
        <result column="enterprise_name" property="enterpriseName" />
        <result column="legal_person_name" property="legalPersonName" />
        <result column="legal_person_phone" property="legalPersonPhone" />
        <result column="legal_person_card" property="legalPersonCard" />
        <result column="legal_person_card_url_g" property="legalPersonCardUrlG" />
        <result column="legal_person_card_url_t" property="legalPersonCardUrlT" />
        <result column="enterprise_credit_code" property="enterpriseCreditCode" />
        <result column="enterprise_type" property="enterpriseType" />
        <result column="enterprise_business_scope" property="enterpriseBusinessScope" />
        <result column="enterprise_home_address" property="enterpriseHomeAddress" />
        <result column="enterprise_detail_address" property="enterpriseDetailAddress" />
        <result column="license_url" property="licenseUrl" />
        <result column="license_start_time" property="licenseStartTime" />
        <result column="license_end_time" property="licenseEndTime" />
        <result column="transport_license_url" property="transportLicenseUrl" />
        <result column="sign_type" property="signType" />
        <result column="contract_no" property="contractNo" />
        <result column="contract_start_time" property="contractStartTime" />
        <result column="contract_end_time" property="contractEndTime" />
        <result column="certigier_user_id" property="certigierUserId" />
        <result column="certigier_user_name" property="certigierUserName" />
        <result column="certigier_user_phone" property="certigierUserPhone" />
        <result column="enterprise_tax_rate" property="enterpriseTaxRate" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="invoice_subject_id" property="invoiceSubjectId" />
        <result column="service_provider_code" property="serviceProviderCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, src_msg_id, enterprise_id, enterprise_name, legal_person_name, legal_person_phone, legal_person_card, legal_person_card_url_g, legal_person_card_url_t, enterprise_credit_code, enterprise_type, enterprise_business_scope, enterprise_home_address, enterprise_detail_address, license_url, license_start_time, license_end_time, transport_license_url, sign_type, contract_no, contract_start_time, contract_end_time, certigier_user_id, certigier_user_name, certigier_user_phone, enterprise_tax_rate, remark, create_time, modify_time, invoice_subject_id, service_provider_code
    </sql>

    <select id="getInvoiceSubjectIdBySrcMsgIds" resultMap="BaseResultMap">
        select src_msg_id, invoice_subject_id
        from tyt_transport_enterprise_log
        where src_msg_id in ( <foreach collection="list" item="i" separator=","> #{i} </foreach> )
        and invoice_subject_id is not null
    </select>
    <select id="selectBySrcMsgId"
            resultType="com.teyuntong.goods.search.service.biz.enterprise.entity.TransportEnterpriseLogDO">
        select *
        from tyt_transport_enterprise_log
        where src_msg_id = #{srcMsgId}
    </select>
</mapper>
