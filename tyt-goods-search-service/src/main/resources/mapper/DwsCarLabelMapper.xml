<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.group.mapper.DwsCarLabelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.search.service.biz.group.entity.DwsCarLabelDO">
        <result column="user_id" property="userId" />
        <result column="car_user_flag" property="carUserFlag" />
        <result column="distance_habit" property="distanceHabit" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, car_user_flag, distance_habit
    </sql>

    <!-- 根据用户ID查询车主用户标识和距离习惯 -->
    <select id="selectCarLabelByUserId" resultMap="BaseResultMap">
        SELECT car_user_flag, distance_habit
        FROM dws_car_label 
        WHERE user_id = #{userId} order by id limit 1
    </select>

</mapper>
