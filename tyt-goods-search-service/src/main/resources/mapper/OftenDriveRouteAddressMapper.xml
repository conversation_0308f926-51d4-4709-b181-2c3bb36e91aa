<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.route.mapper.OftenDriveRouteAddressMapper">


    <select id="getByRouteId" resultType="com.teyuntong.goods.search.service.biz.route.entity.OftenDriveRouteAddressDO">
        select * from often_drive_route_address where route_id = #{routeId}
    </select>

    <select id="getByRouteIds" resultType="com.teyuntong.goods.search.service.biz.route.entity.OftenDriveRouteAddressDO">
        select * from often_drive_route_address
        where route_id in ( <foreach collection="routeIds" item="routeId" separator=","> #{routeId} </foreach> )
    </select>

</mapper>
