<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.bi.mapper.DwsCarTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.search.service.biz.bi.entity.DwsCarTypeDO">
        <result column="user_id" property="userId" />
        <result column="car_model" property="carModel" />
        <result column="weight_min" property="weightMin" />
        <result column="weight_max" property="weightMax" />
        <result column="car_id" property="carId" />
        <result column="car_weight_min" property="carWeightMin" />
        <result column="car_weight_max" property="carWeightMax" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, car_model, weight_min, weight_max, car_id, car_weight_min, car_weight_max
    </sql>

    <!-- 查询用户的车辆吨重下限 -->
    <select id="getWeightMinByUserId" resultMap="BaseResultMap">
		select *
		from dws_car_type
		where user_id =#{userId}
    </select>

</mapper>
