<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.goods.mapper.TransportExtendMapper">

    <select id="getByTsIds" resultType="com.teyuntong.goods.search.service.biz.goods.entity.TransportExtendDO">
        select *
        from tyt_transport_extend
        where ts_id in ( <foreach collection="list" item="i" separator=","> #{i} </foreach> )
    </select>

</mapper>
