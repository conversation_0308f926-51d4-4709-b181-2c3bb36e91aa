<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.record.mapper.TransportViewLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.search.service.biz.record.entity.TransportViewLogDO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="ts_id" property="tsId"/>
        <result column="client_version" property="clientVersion"/>
        <result column="client_sign" property="clientSign"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, ts_id, client_version, client_sign, ctime, mtime
    </sql>

    <!-- 获取最近的浏览记录列表 -->
    <select id="getRecentViewList" resultMap="BaseResultMap">
        select * from tyt_transport_view_log
        where user_id = #{userId}
        and ctime > #{startDate}
        order by ctime desc
    </select>

    <select id="getRecentViewListSrcMsgId" resultType="long">
        select ts_id from tyt_transport_view_log
        where user_id = #{userId}
          and ctime > #{startDate}
    </select>

    <select id="getRecentViewSrcMsgIdList" resultType="java.lang.Long">
        select distinct ts_id from tyt_transport_view_log
        where user_id = #{userId}
          and ctime > #{startDate}
        order by ctime desc
    </select>

    <select id="getRecentCountByUserId" resultType="java.lang.Integer">
        select count(*) from tyt_transport_view_log
        where user_id = #{userId}
          and ctime > #{startDate}
    </select>


</mapper>
