<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.record.mapper.AppCallLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.search.service.biz.record.entity.AppCallLogDO">
        <id column="id" property="id"/>
        <result column="call_module" property="callModule"/>
        <result column="from_car" property="fromCar"/>
        <result column="car_id" property="carId"/>
        <result column="call_time" property="callTime"/>
        <result column="caller_id" property="callerId"/>
        <result column="called_info_id" property="calledInfoId"/>
        <result column="call_result_code" property="callResultCode"/>
        <result column="call_result_name" property="callResultName"/>
        <result column="marker_owner_codes" property="markerOwnerCodes"/>
        <result column="marker_owner_names" property="markerOwnerNames"/>
        <result column="marker_owner_code" property="markerOwnerCode"/>
        <result column="marker_owner_name" property="markerOwnerName"/>
        <result column="pub_user_id" property="pubUserId"/>
        <result column="src_msg_id" property="srcMsgId"/>
        <result column="reference" property="reference"/>
        <result column="plat_id" property="platId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, call_module, from_car, car_id, call_time, caller_id, called_info_id, call_result_code, call_result_name,
        marker_owner_codes, marker_owner_names, marker_owner_code, marker_owner_name, pub_user_id, src_msg_id,
        reference, plat_id, create_time
    </sql>

    <!-- 获取最近的拨打记录列表 -->
    <select id="getRecentCallList" resultMap="BaseResultMap">
        select * from tyt_app_call_log
        where caller_id = #{userId}
        and call_time > #{startDate}
        order by call_time desc
    </select>

    <select id="getRecentCallListSrcMsgId" resultType="long">
        select src_msg_id from tyt_app_call_log
        where caller_id = #{userId}
          and call_time > #{startDate}
    </select>

    <select id="getBySrcMsgIdAndUserId"
            resultType="com.teyuntong.goods.search.service.biz.record.entity.AppCallLogDO">
        select *
        from tyt_app_call_log
        where src_msg_id = #{srcMsgId}
          and caller_id = #{userId}
        order by id desc
        limit 1
    </select>

    <select id="getRecentCountByUserId" resultType="java.lang.Integer">
        select count(*) from tyt_app_call_log
        where caller_id = #{userId}
          and call_time > #{startDate}
    </select>
</mapper>
