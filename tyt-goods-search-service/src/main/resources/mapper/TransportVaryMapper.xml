<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.goods.mapper.TransportVaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.search.service.biz.goods.entity.TransportVaryDO">
        <id column="id" property="id"/>
        <result column="ts_id" property="tsId"/>
        <result column="status" property="status"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <select id="searchHallVary"
            resultType="com.teyuntong.goods.search.service.biz.goods.entity.TransportVaryDO">
        select *
        from tyt_transport_vary
        <where>
            <if test="maxId != null and maxId > 0">
                id &lt;= (#{maxId} + #{pageSize})
                and id &gt; #{maxId}
            </if>
        </where>
        order by id desc
        <if test="pageSize != null">
            limit #{pageSize}
        </if>


    </select>


</mapper>
