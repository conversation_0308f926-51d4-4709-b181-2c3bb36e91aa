<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.record.mapper.TransportQuotedPriceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.search.service.biz.record.entity.TransportQuotedPriceDO">
        <id column="id" property="id"/>
        <result column="car_id" property="carId"/>
        <result column="car_user_name" property="carUserName"/>
        <result column="transport_user_id" property="transportUserId"/>
        <result column="transport_user_name" property="transportUserName"/>
        <result column="src_msg_id" property="srcMsgId"/>
        <result column="car_quoted_price" property="carQuotedPrice"/>
        <result column="transport_quoted_price" property="transportQuotedPrice"/>
        <result column="car_is_done" property="carIsDone"/>
        <result column="transport_is_done" property="transportIsDone"/>
        <result column="final_quoted_price_is_done" property="finalQuotedPriceIsDone"/>
        <result column="final_quoted_price" property="finalQuotedPrice"/>
        <result column="final_quoted_price_type" property="finalQuotedPriceType"/>
        <result column="car_quoted_price_time" property="carQuotedPriceTime"/>
        <result column="transport_quoted_price_time" property="transportQuotedPriceTime"/>
        <result column="car_quoted_price_times" property="carQuotedPriceTimes"/>
        <result column="transport_quoted_price_times" property="transportQuotedPriceTimes"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, car_id, car_user_name, transport_user_id, transport_user_name, src_msg_id, car_quoted_price,
        transport_quoted_price, car_is_done, transport_is_done, final_quoted_price_is_done, final_quoted_price,
        final_quoted_price_type, car_quoted_price_time, transport_quoted_price_time, car_quoted_price_times,
        transport_quoted_price_times
    </sql>

    <!-- 获取最近的出价记录列表 -->
    <select id="getRecentQuotedList" resultMap="BaseResultMap">
        select * from tyt_transport_quoted_price
        where car_id = #{userId}
        and car_quoted_price_time > #{startDate}
        order by car_quoted_price_time desc
    </select>

    <select id="getRecentQuotedListSrcMsgId" resultType="long">
        select src_msg_id from tyt_transport_quoted_price
        where car_id = #{userId}
          and car_quoted_price_time > #{startDate}
    </select>

    <select id="getRecentCountByUserId" resultType="java.lang.Integer">
        select count(*) from tyt_transport_quoted_price
        where car_id = #{userId}
          and car_quoted_price_time > #{startDate}
    </select>

    <select id="getByCarIdAndSrcMsgId" resultMap="BaseResultMap">
        select *
        from tyt_transport_quoted_price
        where car_id = #{carUserId} and src_msg_id = #{srcMsgId}
        limit 1
    </select>

</mapper>
