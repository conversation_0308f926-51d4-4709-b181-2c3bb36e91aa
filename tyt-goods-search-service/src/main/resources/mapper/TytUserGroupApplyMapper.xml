<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.group.mapper.TytUserGroupApplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.search.service.biz.group.entity.TytUserGroupApplyDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="user_account" property="userAccount" />
        <result column="user_identity_label" property="userIdentityLabel" />
        <result column="transport_distance_preference" property="transportDistancePreference" />
        <result column="apply_province_city" property="applyProvinceCity" />
        <result column="create_time" property="createTime" />
        <result column="is_joined_group" property="isJoinedGroup" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, user_name, user_account, user_identity_label, transport_distance_preference, apply_province_city, create_time, is_joined_group
    </sql>

    <select id="getJoinByProvinceCityAndUserId" resultMap="BaseResultMap">
        select *
        from tyt_user_group_apply where apply_province_city = #{provinceCity} and user_id = #{userId};
    </select>
    
    <insert id="insertSelective" parameterType="com.teyuntong.goods.search.service.biz.group.entity.TytUserGroupApplyDO">
        insert into tyt_user_group_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="userAccount != null">
                user_account,
            </if>
            <if test="userIdentityLabel != null">
                user_identity_label,
            </if>
            <if test="transportDistancePreference != null">
                transport_distance_preference,
            </if>
            <if test="applyProvinceCity != null">
                apply_province_city,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="isJoinedGroup != null">
                is_joined_group,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="userName != null">
                #{userName},
            </if>
            <if test="userAccount != null">
                #{userAccount},
            </if>
            <if test="userIdentityLabel != null">
                #{userIdentityLabel},
            </if>
            <if test="transportDistancePreference != null">
                #{transportDistancePreference},
            </if>
            <if test="applyProvinceCity != null">
                #{applyProvinceCity},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="isJoinedGroup != null">
                #{isJoinedGroup},
            </if>
        </trim>
    </insert>
</mapper>
