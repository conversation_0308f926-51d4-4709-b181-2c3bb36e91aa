<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.route.mapper.OftenDriveRouteMapper">

    <select id="getBySignCode" resultType="com.teyuntong.goods.search.service.biz.route.entity.OftenDriveRouteDO">
        select * from often_drive_route where sign_code = #{signCode}
    </select>
</mapper>
