<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.route.mapper.OftenDriveRouteUserMapper">

    <select id="getRouteList" resultType="com.teyuntong.goods.search.service.biz.route.vo.UserOftenRouteVo">
        select
            ru.id,
            ru.user_id userId,
            ru.route_id routeId,
            ru.report_status reportStatus,
            ru.enable_status enableStatus,
            dr.min_weight minWeight,
            dr.max_weight maxWeight,
            dr.goods_types goodsTypeStr
        from often_drive_route_user ru
        join often_drive_route dr on ru.route_id = dr.id
        where ru.user_id = #{userId}
        order by ru.id
    </select>

    <select id="getByUserIdAndRouteId" resultType="com.teyuntong.goods.search.service.biz.route.entity.OftenDriveRouteUserDO">
        select *
        from often_drive_route_user
        where user_id = #{userId} and route_id = #{routeId}
    </select>

    <select id="countByUserId" resultType="int">
        select count(*) from often_drive_route_user where user_id = #{userId}
        <if test="reportStatus != null">
            and report_status = #{reportStatus}
        </if>
    </select>

    <select id="getRouteIdOfEnable" resultType="long">
        select route_id
        from often_drive_route_user
        where user_id = #{userId}
        and enable_status = 1
    </select>

</mapper>
