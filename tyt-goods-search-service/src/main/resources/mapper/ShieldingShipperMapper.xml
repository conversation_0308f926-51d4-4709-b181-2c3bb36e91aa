<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.behavior.mapper.ShieldingShipperMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.search.service.biz.behavior.entity.ShieldingShipperDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="shielding_user_id" property="shieldingUserId" />
        <result column="is_delete" property="isDelete" />
        <result column="create_id" property="createId" />
        <result column="create_time" property="createTime" />
        <result column="update_id" property="updateId" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, shielding_user_id, is_delete, create_id, create_time, update_id, update_time
    </sql>

    <select id="getShieldingUserIdList" resultType="long">
        select shielding_user_id
        from tyt_shielding_shipper
        where user_id = #{userId} and is_delete = 0
    </select>

    <update id="deleteData">
        UPDATE tyt_shielding_shipper
        SET is_delete = 1, update_id = #{userId}, update_time = now()
        WHERE user_id = #{userId} and is_delete = 0 and shielding_user_id = #{shieldingUserId}
    </update>

</mapper>
