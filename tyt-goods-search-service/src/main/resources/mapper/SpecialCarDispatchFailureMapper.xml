<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.search.service.biz.dispatch.mapper.SpecialCarDispatchFailureMapper">


    <select id="getBySrcMsgIds"
            resultType="com.teyuntong.goods.search.service.biz.dispatch.entity.SpecialCarDispatchFailureDO">
        select *
        from tyt_special_car_dispatch_failure
        where src_msg_id in
        <foreach collection="srcMsgIds" item="srcMsgId" open="(" separator="," close=")">
            #{srcMsgId}
        </foreach>
    </select>
</mapper>
