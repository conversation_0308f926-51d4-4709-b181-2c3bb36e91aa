package com.teyuntong.goods.search.service.rpc.certification.service.impl;

import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.search.service.remote.basic.ConfigRemoteService;
import com.teyuntong.goods.search.service.remote.order.OrderRemoteService;
import com.teyuntong.goods.search.service.rpc.certification.enums.CertVerifyTypeEnum;
import com.teyuntong.goods.search.service.rpc.certification.service.CertificationService;
import com.teyuntong.goods.search.service.rpc.certification.vo.CertIllegalInfoVo;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.trade.service.client.orders.dto.CheckDriverAndCarDTO;
import com.teyuntong.trade.service.client.orders.vo.CheckDriverAndCarVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @since 2024/08/10 11:27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CertificationServiceImpl implements CertificationService {

    private final OrderRemoteService orderRemoteService;
    private final RedisUtil redisUtil;
    private final ConfigRemoteService configRemoteService;


    /**
     * 校验司机和车辆是否合法。
     * APP证件缺失提醒和找货限制提醒都传2，找货限制优先级更高；
     * 服务端先判断找货限制，再判断证件缺失，证件缺失每天只判断1次
     *
     * @param type 1:详情页证件缺失提示  2:找货大厅证件缺失提醒和找货限制提醒  4 找货限制提醒（PC/小程序）
     */
    @Override
    public CertIllegalInfoVo verifyCarAndDriver(Integer type) {
        CertIllegalInfoVo certIllegalInfoVo = new CertIllegalInfoVo();
        // 校验证件缺失开关，默认打开
        if (configRemoteService.getIntValue("verify_certification_switch", 1) != 1) {
            certIllegalInfoVo.setIsIllegal(0);
            return certIllegalInfoVo;
        }

        // 调用履约接口
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            certIllegalInfoVo.setIsIllegal(0);
            return certIllegalInfoVo;
        }
        Long userId = loginUser.getUserId();
        // 如果是2，优先判断找货限制，如果通过再判断证件缺失，证件缺失一天只判断1次
        if (type == 2) {
            callRemoteInterface(userId, 3, certIllegalInfoVo);
            if (certIllegalInfoVo.getIsIllegal() == 0 && isNeedCheckAgain(userId)) {
                callRemoteInterface(userId, 2, certIllegalInfoVo);
            }
        } else {
            callRemoteInterface(userId, type, certIllegalInfoVo);
        }

        return certIllegalInfoVo;
    }

    /**
     * 判断是否需要再次校验证件缺失。
     */
    private boolean isNeedCheckAgain(Long userId) {
        String cacheKey = "goods:search:checkCert:" + userId;
        Object value = redisUtil.get(cacheKey);
        if (value == null) {
            // 返回现在到明天零点的时间差
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime nextMidnight = now.with(LocalTime.MIDNIGHT).plusDays(1);
            redisUtil.set(cacheKey, "1", Duration.between(now, nextMidnight));
            return true;
        }
        return false;
    }

    /**
     * 调用履约接口：<href>http://192.168.2.20:3300/project/265/interface/api/26684</href>
     */
    private void callRemoteInterface(Long userId, Integer type, CertIllegalInfoVo illegalInfoVo) {
        try {
            CertVerifyTypeEnum typeEnum = CertVerifyTypeEnum.getByCode(type);
            if (typeEnum == null) {
                throw new RuntimeException("校验司机和车辆是否合法，type参数不合法：" + type);
            }
            illegalInfoVo.setTitleType(typeEnum.getCode());
            illegalInfoVo.setTitle(typeEnum.getTitle());

            CheckDriverAndCarDTO checkDriverAndCarDTO = new CheckDriverAndCarDTO();
            checkDriverAndCarDTO.setUserId(userId);
            checkDriverAndCarDTO.setCheckSourceType(type);
            log.info("调用履约接口校验司机和车辆是否合格，入参：{}", JSON.toJSONString(checkDriverAndCarDTO));
            CheckDriverAndCarVO checkDriverAndCarVO = orderRemoteService.checkDriverAndCarQualified(checkDriverAndCarDTO);
            log.info("调用履约接口校验司机和车辆是否合格，出参：{}", JSON.toJSONString(checkDriverAndCarVO));

            if (checkDriverAndCarVO == null || checkDriverAndCarVO.getCheckResultType() == 0) {
                illegalInfoVo.setIsIllegal(0);
            } else {
                illegalInfoVo.setIsIllegal(1);
                if (checkDriverAndCarVO.getCheckResultType() == 1) {
                    illegalInfoVo.setMessage("此货源为开票货源，要求司机和车辆证件完整，您当前司机不合格");
                } else if (checkDriverAndCarVO.getCheckResultType() == 2) {
                    illegalInfoVo.setMessage("此货源为开票货源，要求司机和车辆证件完整，您当前车辆不合格");
                } else if (checkDriverAndCarVO.getCheckResultType() == 3) {
                    illegalInfoVo.setMessage("此货源为开票货源，要求司机和车辆证件完整，您当前司机和车辆都不合格");
                } else if (checkDriverAndCarVO.getCheckResultType() == 4) {
                    illegalInfoVo.setMessage(checkDriverAndCarVO.getCheckResultMsg());
                    illegalInfoVo.setItems(checkDriverAndCarVO.getCheckMsgList());
                }
            }
        } catch (Exception e) {
            illegalInfoVo.setIsIllegal(0); // 异常默认合法
            log.error("调用履约接口校验司机和车辆是否合格异常", e);
        }
    }

}
