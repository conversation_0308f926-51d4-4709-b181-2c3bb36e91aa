package com.teyuntong.goods.search.service.biz.dispatch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 专车自动派单无人接单货源表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Getter
@Setter
@TableName("tyt_special_car_dispatch_failure")
public class SpecialCarDispatchFailureDO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货源ID
     */
    private Long srcMsgId;

    /**
     * 货主ID
     */
    private Long userId;

    /**
     * 工单状态：0-待处理，1-处理中，2-已结单
     */
    private Integer workOrderStatus;

    /**
     * 首次响应时间
     */
    private Date responseTime;

    /**
     * 结单时间
     */
    private Date endTime;

    /**
     * 发货用户类型：1-调度发货，2-普通用户发货
     */
    private Integer publishUserType;

    /**
     * 派单类型：0-系统自动派单，1-人工指派，2-人工改派
     */
    private Integer dispatchType;

    /**
     * 发货人ID(tyt_user表ID)
     */
    private Long publishUserId;

    /**
     * 发货人姓名
     */
    private String publishUserName;

    /**
     * 调度负责人ID
     */
    private Long dispatcherId;

    /**
     * 调度负责人姓名
     */
    private String dispatcherName;

    /**
     * 货主出价
     */
    private BigDecimal ownerFreight;

    /**
     * 给货货主手机号
     */
    private String giveGoodsPhone;

    /**
     * 给货货主姓名
     */
    private String giveGoodsName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 删除状态：0-正常，1-已删除
     */
    private Integer delStatus;

    /**
     * 是否可大厅抢单 0：否；1：是
     */
    private Integer declareInPublic;
}
