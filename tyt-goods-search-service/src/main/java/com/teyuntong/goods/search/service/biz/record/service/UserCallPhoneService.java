package com.teyuntong.goods.search.service.biz.record.service;


import com.teyuntong.goods.search.service.biz.goods.dto.GoodsPhoneDTO;
import com.teyuntong.goods.search.service.common.enums.ActionTypeEnum;

/**
 * <p>
 * 用户获取电话表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface UserCallPhoneService {
    /**
     * 保存用户获取手机号记录
     *
     * @param goodsPhoneDTO
     * @param userId
     * @param actionTypeEnum
     */
    void saveRecord(GoodsPhoneDTO goodsPhoneDTO, Long userId, ActionTypeEnum actionTypeEnum);
}
