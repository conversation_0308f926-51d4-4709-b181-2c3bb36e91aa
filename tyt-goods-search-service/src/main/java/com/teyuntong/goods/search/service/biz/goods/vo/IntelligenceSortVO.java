package com.teyuntong.goods.search.service.biz.goods.vo;

import com.teyuntong.goods.search.service.biz.goods.dto.EsSearchExtra;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 智能排序返回结果
 * <AUTHOR>
 * @since 2024/07/04 10:24
 */
@Getter
@Setter
public class IntelligenceSortVO {
    /**
     * es查询需要的额外字段，客户端原封不动带过来
     */
    private EsSearchExtra esSearchExtra;

    /**
     * 是否还有数据  true:有  false:无
     */
    private boolean hasNext = false;

    private List<TransportVO> list;
}
