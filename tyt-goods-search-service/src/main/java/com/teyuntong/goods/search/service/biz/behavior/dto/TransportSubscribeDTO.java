package com.teyuntong.goods.search.service.biz.behavior.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 货源订阅
 *
 * <AUTHOR>
 * @since 2024/12/14 18:02
 */
@Getter
@Setter
public class TransportSubscribeDTO {

    /**
     * 出发地省市区
     */
    private String startPoint;

    /**
     * 目的地省市区
     */
    private String destPoint;

    /**
     * 装货起始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date loadingTime;

    /**
     * 吨重-下限
     */
    private BigDecimal weightMin;

    /**
     * 吨重-上限
     */
    private BigDecimal weightMax;

    /**
     * 订单id
     */
    private Long orderId;
}
