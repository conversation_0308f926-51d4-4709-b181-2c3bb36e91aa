package com.teyuntong.goods.search.service.biz.goods.vo;

import lombok.Data;

import java.util.List;

/**
 * @Describe
 * <AUTHOR>
 * @Date 2023/3/16
 */
@Data
public class SearchHallCountVO {

    /**
     * 时间排序气泡数量
     */
    private Long bubbleCount = 0L;
    /**
     * 时间排序气泡最大的货源id
     */
    private Long maxTsId;

    /**
     * 如果不在找货大厅首页，有气泡时需要在上面给提示出来
     */
    private List<TransportVO> transportVOList;


}
