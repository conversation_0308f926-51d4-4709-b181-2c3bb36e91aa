package com.teyuntong.goods.search.service.biz.route.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.route.entity.OftenDriveRouteUserAddressDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户路线地址冗余表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Mapper
@DS("good_search")
public interface OftenDriveRouteUserAddressMapper extends BaseMapper<OftenDriveRouteUserAddressDO> {

    /**
     * 根据用户id获取路线地址
     */
    List<OftenDriveRouteUserAddressDO> getByUserId(@Param("userId") Long userId);

    /**
     * 根据用户路线id获取路线地址
     */
    List<OftenDriveRouteUserAddressDO> getByRouteUserId(@Param("routeUserId") Long routeUserId);

    /**
     * 根据用户路线id删除路线地址
     */
    int deleteByRouteUserId(@Param("routeUserId") Long routeUserId);

}
