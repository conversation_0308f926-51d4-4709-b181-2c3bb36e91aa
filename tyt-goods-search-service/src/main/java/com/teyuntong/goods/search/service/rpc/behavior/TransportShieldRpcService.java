package com.teyuntong.goods.search.service.rpc.behavior;

import com.teyuntong.goods.search.service.biz.behavior.dto.ShieldingShipperDTO;
import com.teyuntong.goods.search.service.biz.behavior.dto.TransportShieldDTO;
import com.teyuntong.goods.search.service.biz.behavior.vo.ShipperUserVO;

import java.util.List;

/**
 * 货源屏蔽接口
 *
 * <AUTHOR>
 * @since 2025/02/15 15:01
 */
public interface TransportShieldRpcService {

    /**
     * 报错屏蔽货源，并举报投诉
     */
    void saveShieldGoodsAndReportComplaint(TransportShieldDTO shieldDTO);

    /**
     * 获取屏蔽货源的srcMsgId
     */
    List<Long> getShieldSrcMsgIds(Long userId);

    /**
     * 保存屏蔽货主
     */
    void saveShieldShipper(ShieldingShipperDTO shieldDTO);

    /**
     * 获取屏蔽货主
     */
    List<ShipperUserVO> getShieldShippers(Long userId);

}
