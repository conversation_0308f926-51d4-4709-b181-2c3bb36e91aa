package com.teyuntong.goods.search.service.biz.goods.dto.rule;

import lombok.Getter;
import lombok.Setter;

/**
 * 智能排序因子：公里单价规则
 * 格式：[{"score":100,"unitPrice":0.76},{"score":80,"unitPrice":0.61},{"score":60,"unitPrice":0.60},{"score":40,"unitPrice":0.58},{"score":20,"unitPrice":0.54},{"score":5,"unitPrice":0.50}]
 * score是分数，unitPrice的公里单价，按价格倒序
 *
 * <AUTHOR>
 * @since 2024/8/29 15:59
 */
@Getter
@Setter
public class IntelligentSortRulePrice {
    /**
     * 公里单价
     */
    private Float unitPrice;
    /**
     * 评分
     */
    private Integer score;

}
