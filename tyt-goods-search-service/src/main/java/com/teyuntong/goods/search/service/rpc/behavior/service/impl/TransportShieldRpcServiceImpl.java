package com.teyuntong.goods.search.service.rpc.behavior.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.teyuntong.goods.search.service.biz.behavior.dto.ShieldingShipperDTO;
import com.teyuntong.goods.search.service.biz.behavior.dto.TransportShieldDTO;
import com.teyuntong.goods.search.service.biz.behavior.entity.ShieldingShipperDO;
import com.teyuntong.goods.search.service.biz.behavior.serivce.ShieldingShipperService;
import com.teyuntong.goods.search.service.biz.behavior.serivce.TransportShieldService;
import com.teyuntong.goods.search.service.biz.behavior.vo.ShipperUserVO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.goods.service.TransportService;
import com.teyuntong.goods.search.service.common.error.GoodsSearchErrorCode;
import com.teyuntong.goods.search.service.common.property.TytEncryptProperty;
import com.teyuntong.goods.search.service.common.util.TransportUtil;
import com.teyuntong.goods.search.service.common.util.TytStringUtils;
import com.teyuntong.goods.search.service.common.util.XXTeaUtils;
import com.teyuntong.goods.search.service.remote.basic.ConfigRemoteService;
import com.teyuntong.goods.search.service.remote.basic.constant.TytConfigKey;
import com.teyuntong.goods.search.service.remote.user.UserComplaintRemoteService;
import com.teyuntong.goods.search.service.remote.user.UserRemoteService;
import com.teyuntong.goods.search.service.rpc.behavior.TransportShieldRpcService;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.custom.dto.ReportBlockerDTO;
import com.teyuntong.user.service.client.user.vo.ApiDataUserCreditInfoRpcVO;
import com.teyuntong.user.service.client.user.vo.CreditUserRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 货源屏蔽接口
 *
 * <AUTHOR>
 * @since 2025/02/15 15:01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportShieldRpcServiceImpl implements TransportShieldRpcService {

    private final TransportShieldService transportShieldService;
    private final UserComplaintRemoteService userComplaintRemoteService;
    private final TransportService transportService;
    private final ShieldingShipperService shieldingShipperService;

    private final UserRemoteService userRemoteService;
    private final ConfigRemoteService configRemoteService;

    private final TytEncryptProperty tytEncryptProperty;

    // 屏蔽
    private static final Integer SHIELDING = 1;
    // 解除屏蔽
    private static final Integer UNBLOCK = 2;

    /**
     * 记录屏蔽货源，并举报投诉
     */
    @Override
    @Async
    public void saveShieldGoodsAndReportComplaint(TransportShieldDTO shieldDTO) {
        transportShieldService.save(shieldDTO);

        TransportDO transportDO = transportService.getBySrcMsgIdAndStatus(shieldDTO.getSrcMsgId());
        if (transportDO == null) {
            return;
        }
        ReportBlockerDTO reportBlockerDTO = new ReportBlockerDTO();
        reportBlockerDTO.setSrcMsgId(shieldDTO.getSrcMsgId());
        reportBlockerDTO.setBlockerUserId(transportDO.getUserId());
        reportBlockerDTO.setReason(shieldDTO.getReason());
        reportBlockerDTO.setBeFrom(shieldDTO.getBeFrom());
        userComplaintRemoteService.reportGoodsBlocker(reportBlockerDTO);
    }

    @Override
    public List<Long> getShieldSrcMsgIds(Long userId) {
        return transportShieldService.getShieldSrcMsgIds(userId);
    }

    /**
     * 保存屏蔽货主
     */
    @Override
    public void saveShieldShipper(ShieldingShipperDTO shieldDTO) {
        if (SHIELDING.equals(shieldDTO.getType())) {
            List<Long> shieldingUserIdList = shieldingShipperService.getShieldingUserFromDB(shieldDTO.getUserId());
            // 判断是否是重复屏蔽
            if (shieldingUserIdList.contains(shieldDTO.getShieldingUserId())) {
                throw new BusinessException(GoodsSearchErrorCode.SHIELDING_USER_HAS_EXIST);
            }
            // 判断当前用户屏蔽的发货人的数量
            if (shieldingUserIdList.size() >= 50) {
                throw new BusinessException(GoodsSearchErrorCode.SHIELDING_USER_COUNT_EXCEED);
            }

            ShieldingShipperDO shieldingShipperDO = new ShieldingShipperDO();
            shieldingShipperDO.setUserId(shieldDTO.getUserId());
            shieldingShipperDO.setShieldingUserId(shieldDTO.getShieldingUserId());
            shieldingShipperDO.setCreateId(shieldDTO.getUserId());
            shieldingShipperDO.setCreateTime(new Date());
            shieldingShipperService.save(shieldingShipperDO);
        } else if (UNBLOCK.equals(shieldDTO.getType())) {
            shieldingShipperService.delete(shieldDTO.getUserId(), shieldDTO.getShieldingUserId());
        }
    }

    /**
     * 获取屏蔽货主
     */
    @Override
    public List<ShipperUserVO> getShieldShippers(Long userId) {
        List<Long> shielderIdList = shieldingShipperService.getShieldingUserList(userId);
        if (CollUtil.isEmpty(shielderIdList)) {
            return List.of();
        }

        List<ShipperUserVO> shielderList = new ArrayList<>();

        // 批量查询用户信息
        Map<Long, UserRpcVO> userMap = userRemoteService.getUserByIdList(shielderIdList)
                .stream().collect(Collectors.toMap(UserRpcVO::getId, t -> t, (k1, k2) -> k1));
        Map<Long, CreditUserRpcVO> creditUserMap = userRemoteService.getPayUserInfosByCarUserId(shielderIdList, userId)
                .stream().collect(Collectors.toMap(CreditUserRpcVO::getUserId, t -> t, (k1, k2) -> k1));

        for (Long shieldUserId : shielderIdList) {
            ShipperUserVO shipperUserVO = new ShipperUserVO();
            shipperUserVO.setUserId(shieldUserId);
            shielderList.add(shipperUserVO);

            // 货主信用信息
            CreditUserRpcVO creditUserRpcVO = creditUserMap.get(shieldUserId);
            if (creditUserRpcVO != null) {
                shipperUserVO.setCoopNums(creditUserRpcVO.getCoopNums());
                shipperUserVO.setTradeNums(creditUserRpcVO.getTradeNums());
            }

            // 货主信用等级信息
            ApiDataUserCreditInfoRpcVO userCreditInfo = userRemoteService.getUserCreditInfo(shieldUserId);
            if (userCreditInfo != null) {
                shipperUserVO.setCarCreditRankLevel(userCreditInfo.getCarCreditRankLevel());
                shipperUserVO.setTransportScore(userCreditInfo.getTransportScore());
                shipperUserVO.setTsRank(userCreditInfo.getTsRank());
            }

            // 货主信息
            UserRpcVO userRpcVO = userMap.get(shieldUserId);
            if (userRpcVO != null) {
                shipperUserVO.setCallPhone(userRpcVO.getCellPhone());
                shipperUserVO.setRegTime(userRpcVO.getCtime());
                shipperUserVO.setIsNeedDecrypt(configRemoteService.getIntValue(TytConfigKey.IS_NEED_ENCYPT, 1));
                shipperUserVO.setNickName(XXTeaUtils.Encrypt(TytStringUtils.hidePhoneInStr(userRpcVO.getUserName()), tytEncryptProperty.getXxtea()));
                shipperUserVO.setUserShowName(TransportUtil.getUserShowName(userRpcVO.getTrueName(), userRpcVO.getIdCard(), userRpcVO.getUserName()));
            }
        }
        return shielderList;
    }

}
