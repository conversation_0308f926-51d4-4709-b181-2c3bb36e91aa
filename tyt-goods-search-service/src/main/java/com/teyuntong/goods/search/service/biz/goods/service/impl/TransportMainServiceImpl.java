package com.teyuntong.goods.search.service.biz.goods.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportMainDO;
import com.teyuntong.goods.search.service.biz.goods.mapper.TransportMainMapper;
import com.teyuntong.goods.search.service.biz.goods.service.TransportMainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 货源处理service
 *
 * <AUTHOR>
 * @since 2024/07/15 17:14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportMainServiceImpl extends ServiceImpl<TransportMainMapper, TransportMainDO> implements TransportMainService {


    @Override
    public List<TransportMainDO> getBySrcMsgIds(List<Long> srdMsgIdList) {
        return baseMapper.getBySrcMsgIds(srdMsgIdList);
    }

    @Override
    public List<Long> getFreeCommissionTransportBySrcMsgIdList(List<Long> srcMsgIds) {
        return baseMapper.getFreeCommissionTransportBySrcMsgIdList(srcMsgIds);
    }

}
