package com.teyuntong.goods.search.service.common.constant;

/**
 * <AUTHOR>
 * @since 2024/07/16 11:41
 */
public class SearchParamConstant {
    // 默认的找货范围：310公里 * 100
    public static final long DEFAULT_DISTANCE_RANGE = 310 * 100;

    // 最小的找货距离
    public static final long MIN_DISTANCE = 0;

    // 最大的找货距离
    public static final long MAX_DISTANCE = 500;

    // 找货坐标换算距离计算值
    public static final long RANG_SIZE = 100;
}
