package com.teyuntong.goods.search.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 特殊标识（命中多个就累加）：1固定位货源；2首票相似货源
 *
 * <AUTHOR>
 * @since 2025/03/21 16:30
 */
@Getter
@AllArgsConstructor
public enum SpecialMarkEnum {

    FIXED_POSITION_GOODS("固定位货源", 0B1),
    FIRST_SIMILARITY_GOODS("首票相似货源", 0B10),
    ;

    private final String desc;
    private final int value; // 二进制值，0B100=4

    /**
     * 判断是否某项已经存在
     */
    public static boolean exist(int value, SpecialMarkEnum itemEnum) {
        return (value & itemEnum.getValue()) != 0;
    }

}
