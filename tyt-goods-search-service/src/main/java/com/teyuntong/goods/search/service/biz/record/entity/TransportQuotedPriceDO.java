package com.teyuntong.goods.search.service.biz.record.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 无价货源报价表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Getter
@Setter
@TableName("tyt_transport_quoted_price")
public class TransportQuotedPriceDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 车主ID
     */
    private Long carId;

    /**
     * 车主昵称
     */
    private String carUserName;

    /**
     * 货主ID
     */
    private Long transportUserId;

    /**
     * 货主昵称
     */
    private String transportUserName;

    /**
     * 货源主表ID
     */
    private Long srcMsgId;

    /**
     * 车方报价
     */
    private Integer carQuotedPrice;

    /**
     * 货方报价
     */
    private Integer transportQuotedPrice;

    /**
     * 车方是否已出价 0：未出价；1：已出价
     */
    private Integer carIsDone;

    /**
     * 货方是否已出价 0：未出价；1：已出价
     */
    private Integer transportIsDone;

    /**
     * 是否完成最终报价 0：未完成；1：已完成
     */
    private Integer finalQuotedPriceIsDone;

    /**
     * 最终报价
     */
    private Integer finalQuotedPrice;

    /**
     * 最终报价遵循 1：遵循车方报价；2：遵循货方报价
     */
    private Integer finalQuotedPriceType;

    /**
     * 车方报价时间
     */
    private Date carQuotedPriceTime;

    /**
     * 货方报价时间
     */
    private Date transportQuotedPriceTime;

    /**
     * 车方报价次数
     */
    private Integer carQuotedPriceTimes;

    /**
     * 货方报价次数
     */
    private Integer transportQuotedPriceTimes;
}
