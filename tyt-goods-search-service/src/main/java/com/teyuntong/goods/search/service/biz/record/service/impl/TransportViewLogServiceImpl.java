package com.teyuntong.goods.search.service.biz.record.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.teyuntong.goods.search.service.biz.goods.dto.SearchRecordQueryDTO;
import com.teyuntong.goods.search.service.biz.record.entity.TransportViewLogDO;
import com.teyuntong.goods.search.service.biz.record.mapper.TransportViewLogMapper;
import com.teyuntong.goods.search.service.biz.record.service.TransportViewLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 货物详情浏览日志表，每人每货仅存储一条 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportViewLogServiceImpl implements TransportViewLogService {

    private final TransportViewLogMapper transportViewLogMapper;

    /**
     * 获取最近的车主浏览记录
     */
    @Override
    public PageInfo<TransportViewLogDO> getRecentViewPage(SearchRecordQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getCurrentPage(), queryDTO.getPageSize());
        List<TransportViewLogDO> list = transportViewLogMapper.getRecentViewList(queryDTO.getUserId(), queryDTO.getStartDate());
        return new PageInfo<>(list);
    }

    @Override
    public List<Long> getRecentViewAllSrcMsgId(Long userId, Date startDate) {
        return transportViewLogMapper.getRecentViewSrcMsgIdList(userId, startDate);
    }

    @Override
    public Integer getRecentCountByUserId(Long userId, Date startDate) {
        return transportViewLogMapper.getRecentCountByUserId(userId, startDate);
    }
}
