package com.teyuntong.goods.search.service.biz.route.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 常跑路线推送消息tag
 *
 * <AUTHOR>
 * @since 2024/11/05 14:06
 */
@Getter
@AllArgsConstructor
public enum RouteMqTagEnum {
    OFTEN_ROUTE("often_route", "常跑路线"),
    NEW_ROUTE("new_route", "新增路线"),
    RESEND_ROUTE("resend_route", "路线重发货源"),
    PUSH_DIRECT_DATA("push_direct_data", "透传推送"),
    GOODS_PUSH("goods_push", "货源推送"),
    ;

    private final String code;
    private final String name;

}
