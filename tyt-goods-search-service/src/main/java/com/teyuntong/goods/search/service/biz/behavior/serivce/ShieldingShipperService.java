package com.teyuntong.goods.search.service.biz.behavior.serivce;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teyuntong.goods.search.service.biz.behavior.entity.ShieldingShipperDO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/07/12 17:10
 */
public interface ShieldingShipperService extends IService<ShieldingShipperDO> {

    /**
     * 从缓存中获取当前用户屏蔽的发货人id
     *
     * @param userId 车主id
     * @return 屏蔽的发货人userIds
     */
    List<Long> getShieldingUserList(Long userId);

    /**
     * 获取默认屏蔽的发货人列表
     *
     * @param userId 车主id
     * @return 屏蔽的发货人userIds
     */
    List<Long> getDefaultShieldingUserIds(Long userId);

    /**
     * 从数据库中获取当前用户屏蔽的发货人id
     *
     * @param userId 车主id
     * @return 屏蔽的发货人userIds
     */
    List<Long> getShieldingUserFromDB(Long userId);

    /**
     * 删除数据
     */
    void delete(Long userId, Long shieldingUserId);
}
