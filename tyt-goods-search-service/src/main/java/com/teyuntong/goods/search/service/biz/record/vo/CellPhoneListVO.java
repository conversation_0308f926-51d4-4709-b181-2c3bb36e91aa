package com.teyuntong.goods.search.service.biz.record.vo;

import com.teyuntong.goods.service.client.callphonerecord.vo.PrivacyPhoneTabInfo;
import com.teyuntong.trade.service.client.orders.vo.AcceptOrderLimitInfo;
import lombok.Data;

@Data
public class CellPhoneListVO {
    /**
     * 联系人1
     */
    private String tel;
    /**
     * 联系人2
     */
    private String tel3;
    /**
     * 联系人3
     */
    private String tel4;
    /**
     * 发货人账号
     */
    private String uploadCellPhone;
    /**
     * 装货联系电话
     */
    private String loadCellPhone;

    /**
     * 卸货联系电话
     */
    private String unloadCellPhone;
    /**
     * 货源id
     */
    private Long srcMsgId;
    /**
     * 运单号
     */
    private String tsOrderNo;
    /**
     * 是否收取定金
     */
    private String isInfoFee;
    /**
     * 当前用户是否对该货物拨打过电话标示 返回结构"是否打过电话状态码_通话标注状态码"，具体参看接口文档
     */
    private String callStatus;
    /**
     * 电话标注code
     */
    private Integer callStatusCode = 0;
    /**
     * 是否可以打电话，0可以，1不可以
     */
    private Integer isCanCall = 0;
    /**
     * 是否下过单:0否、1是
     */
    private String hasMakeOrder;
    /**
     * 用户id
     */
    private Long userId;
    private Integer goodStatus;
    /**
     * 拨打电话时提示框提示信息
     */
    private String noGoodsMemberText;

    /**
     * 接单限制信息
     */
    private AcceptOrderLimitInfo acceptOrderLimitInfo;
    /**
     * 隐私号码信息
     */
    private PrivacyPhoneTabInfo privacyPhoneTabInfo;
}
