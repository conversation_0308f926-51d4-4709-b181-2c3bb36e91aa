package com.teyuntong.goods.search.service.biz.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.search.client.transport.enums.BenefitLabelEnum;
import com.teyuntong.goods.search.client.transport.vo.BenefitLabelVO;
import com.teyuntong.goods.search.service.biz.behavior.serivce.ShieldingShipperService;
import com.teyuntong.goods.search.service.biz.dispatch.service.CustomFirstOrderRecordService;
import com.teyuntong.goods.search.service.biz.enterprise.service.TransportEnterpriseLogService;
import com.teyuntong.goods.search.service.biz.goods.constant.TransportConstant;
import com.teyuntong.goods.search.service.biz.goods.converter.TransportConverter;
import com.teyuntong.goods.search.service.biz.goods.dto.BaseTransportSearchDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.SearchHallCountDTO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.goods.es.entity.TransportEsDO;
import com.teyuntong.goods.search.service.biz.goods.es.service.EsTransportService;
import com.teyuntong.goods.search.service.biz.goods.mapper.TransportMainMapper;
import com.teyuntong.goods.search.service.biz.goods.mapper.TransportMapper;
import com.teyuntong.goods.search.service.biz.goods.service.TransportService;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportLabelJsonVO;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.goods.search.service.biz.record.service.UserCallPhoneRecordService;
import com.teyuntong.goods.search.service.biz.similarity.service.SimilarityTransportService;
import com.teyuntong.goods.search.service.common.enums.*;
import com.teyuntong.goods.search.service.common.property.TytEncryptProperty;
import com.teyuntong.goods.search.service.common.util.*;
import com.teyuntong.goods.search.service.remote.activity.MarketingActivityRemoteService;
import com.teyuntong.goods.search.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.search.service.remote.basic.ConfigRemoteService;
import com.teyuntong.goods.search.service.remote.basic.constant.ABTestCode;
import com.teyuntong.goods.search.service.remote.basic.constant.TytConfigKey;
import com.teyuntong.goods.search.service.remote.user.UserLocalService;
import com.teyuntong.goods.search.service.remote.user.UserPermissionRemoteService;
import com.teyuntong.goods.search.service.remote.user.constant.UserBiIdentityEnum;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.market.activity.client.activity.enums.ActivityTypeEnum;
import com.teyuntong.user.service.client.permission.vo.UserPermissionResult;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

import static com.teyuntong.goods.search.service.common.constant.ConfigConstant.*;
import static com.teyuntong.goods.search.service.common.constant.RedisKeyConstant.*;

/**
 * 货源处理service
 *
 * <AUTHOR>
 * @since 2024/07/15 17:14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportServiceImpl implements TransportService {

    private final UserPermissionRemoteService userPermissionRemoteService;
    private final ConfigRemoteService configService;
    private final ShieldingShipperService shieldingShipperService;
    private final TransportMapper transportMapper;
    private final TransportMainMapper transportMainMapper;
    private final TytEncryptProperty tytEncryptProperty;
    private final ConfigRemoteService configRemoteService;
    private final TransportEnterpriseLogService transportEnterpriseLogService;
    private final UserLocalService userLocalService;
    private final ABTestRemoteService abTestRemoteService;
    private final RedisUtil redisUtil;
    private final EsTransportService esTransportService;
    private final SimilarityTransportService similarityTransportService;
    private final StringRedisTemplate stringRedisTemplate;
    private final MarketingActivityRemoteService marketingActivityRemoteService;
    private final CustomFirstOrderRecordService customFirstOrderRecordService;
    private final UserCallPhoneRecordService userCallPhoneRecordService;

    /**
     * 对非会员车主隐藏货物敏感信息
     *
     * @param transportList 货源
     * @param userId        车主id
     */
    @Override
    public void hideSensitiveInfo(List<? extends TransportVO> transportList, Long userId) {
        hideSensitiveInfoIsShowTrueNickName(transportList, userId, true);
    }

    @Override
    public void hideSensitiveInfoIsShowTrueNickName(List<? extends TransportVO> transportList, Long userId, boolean isNeedEncrypt) {
        if (CollUtil.isNotEmpty(transportList)) {
            try {
                Integer defaultUserId = configService.getIntValue(TytConfigKey.TRANSPORT_DEFAULT_USER_ID, 4);

                //如果是否需要加密为是，则最后是否真的需要加密由开关控制，如果是否需要加密为否，则强制不加密
                if (isNeedEncrypt) {
                    isNeedEncrypt = Objects.equals(configService.getIntValue(TytConfigKey.IS_NEED_ENCYPT, 1), 1);
                }
                // 获取用户权限
                UserPermissionResult userPermission = (userId == null ? null : userPermissionRemoteService.getPermissionForGoodsSearch(userId));
                log.info("打印用户权限：userId:{}, userPermission:{}", userId, userPermission);

                Integer userType = 0;
                if (userId != null) {
                    userType = abTestRemoteService.getUserType("not_show_transport_user_data", userId);
                }
                for (TransportVO transportVO : transportList) {
                    if (userType != null && userType == 1) {
                        //如果车主ID在 防飞单（隐藏货主信息）AB测试中，则将货源货主昵称修改为特运通老板，并且不需要加密
                        transportVO.setNickName("特运通老板");
                    } else {
                        transportVO.setNickName(TytStringUtils.hidePhoneInStr(transportVO.getNickName()));
                    }
                    transportVO.setPubDate(transportVO.getCtime());

                    // 无拨打电话权限用户  设置昵称***
                    dealContentAndNickName(transportVO, userPermission);

                    // 昵称加密处理
                    encryptNickName(transportVO, isNeedEncrypt);

                    transportVO.setUserId(defaultUserId == null ? 4 : defaultUserId.longValue());
                }
            } catch (Exception e) {
                log.error("hideSensitiveInfo error,userId:{}", userId, e);
            }
        }
    }

    /**
     * 处理货源内容
     */
    private void dealContentAndNickName(TransportVO vo, UserPermissionResult userPermission) {
        String similarityFirstInfo = vo.getSimilarityFirstInfo();
        String[] firstInfo = null;
        if (StringUtils.isNotBlank(similarityFirstInfo)) {
            firstInfo = similarityFirstInfo.split(TransportConstant.SIMILARITY_FIRST_INFO_SPLIT);
        }
        if (userPermission != null && userPermission.isUserNamePower()) {
            if (vo.getNickName().contains("" + vo.getUserId())) {
                vo.setNickName("用户...");
            }
        } else {
            vo.setNickName(TransportConstant.REPLACEMENT_STARTS);
            if (firstInfo != null) {
                firstInfo[1] = TransportConstant.REPLACEMENT_STARTS;
            }
        }

        if (userPermission == null || !userPermission.isContentPower()) {
            vo.setTaskContent(TransportConstant.REPLACEMENT_STARTS_CONTENT);
            vo.setWeight(null);
            vo.setCarLength(null);
            vo.setCarType(null);
            vo.setSpecialRequired(null);
            if (firstInfo != null) {
                firstInfo[0] = TransportConstant.REPLACEMENT_STARTS_CONTENT;
            }
        }
        vo.setSimilarityFirstInfo(StringUtils.join(firstInfo, TransportConstant.SIMILARITY_FIRST_INFO_SPLIT));
    }

    /**
     * 加密昵称
     */
    private void encryptNickName(TransportVO vo, boolean isNeedEncrypt) {

        if (isNeedEncrypt) {
            vo.setIsNeedDecrypt(1);
            String nickName = vo.getNickName();
            if (StringUtils.isNotBlank(nickName)) {
                vo.setNickName(XXTeaUtils.Encrypt(nickName, tytEncryptProperty.getXxtea()));
                vo.setNickNameByAes(AESUtils.enCode(nickName, tytEncryptProperty.getAes()));
                vo.setDecryptNickName(vo.getNickNameByAes());
            } else {
                vo.setIsNeedDecrypt(2);
            }
        }
    }

    /**
     * 对货源进行打标，如：给专票货源打标；给专车货源打标
     *
     * @param transportList 货源
     */
    @Override
    public void handleTransportTag(List<? extends TransportVO> transportList, Long userId) {
        if (CollectionUtils.isEmpty(transportList)) {
            return;
        }
        try {
            // 给专票货源打标
            List<Long> srcMsgIds = transportList.stream().filter(t -> Objects.equals(t.getInvoiceTransport(), 1)).map(TransportVO::getSrcMsgId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(srcMsgIds)) {
                Map<Long, Long> invoiceSubjectIdMap = transportEnterpriseLogService.getInvoiceSubjectIdMap(srcMsgIds);
                transportList.forEach(t -> t.setInvoiceSubjectId(invoiceSubjectIdMap.get(t.getSrcMsgId())));
            }
            List<Long> allSrcMsgIds = transportList.stream().map(TransportVO::getSrcMsgId).collect(Collectors.toList());
            Map<Long, BigDecimal> transportEarliestPriceMap = getTransportEarliestPrice(allSrcMsgIds);
            boolean joinActivity = isJoinActivity(userId, ActivityTypeEnum.car_gtv);

            for (TransportVO transportVO : transportList) {
                transportVO.setSort(transportVO.getId()); // sort设置为id
                // 只有好货展示读秒货源标签，非好货把捂货过期时间置为空就隐藏了
                TransportLabelJsonVO labelJsonVO = JSON.parseObject(transportVO.getLabelJson(), TransportLabelJsonVO.class);
                labelJsonVO = labelJsonVO == null ? new TransportLabelJsonVO() : labelJsonVO;

                if (!Objects.equals(labelJsonVO.getIGBIResultData(), 1)) {
                    transportVO.setPriorityRecommendExpireTime(null);
                }
                Integer userType = userLocalService.getUserBiIdentityType(transportVO.getUserId());
                UserRpcVO transportUser = userLocalService.getUserById(transportVO.getUserId());
                // 是否显示超额保障标签
//                transportVO.setShowExcessCoverageLabel(shouldShowExcessCoverageLabel(labelJsonVO, userType));
                // 是否显示加价标签
                showAddPriceLabel(transportEarliestPriceMap, transportVO);
                // 是否显示现金奖标签
                if (joinActivity) {
                    showCashLabel(transportVO, labelJsonVO, userType, transportUser);
                }
                // 是否显示送订金券标签，优先级低于现金券
                if (!Objects.equals(transportVO.getIsCashPrizeActivityGoods(), YesNoEnum.Y.getCode())) {
                    transportVO.setShowDepositCouponsLabel(shouldShowDepositCouponsLabel(labelJsonVO, userType));
                }
                // 格式化长宽高重尺寸
                formatTransportSize(transportVO);
                //星级豁免
                if (!showRankLevelCache(transportVO.getUserId(), transportVO.getRankLevel(), userType, transportUser)) {
                    transportVO.setRankLevel(null);
                }
            }
        } catch (Exception e) {
            log.error("handleTransportTag error,userId:{}", userId, e);
        }

    }

    /**
     * 格式化长宽高重尺寸
     *
     * @param transportVO
     */
    private void formatTransportSize(TransportVO transportVO) {

        //长
        if (StringUtils.isNotBlank(transportVO.getLength())) {
            transportVO.setLength(FormatUtil.formatDimension(transportVO.getLength()));
        }
        //宽
        if (StringUtils.isNotBlank(transportVO.getWide())) {
            transportVO.setWide(FormatUtil.formatDimension(transportVO.getWide()));
        }
        //高
        if (StringUtils.isNotBlank(transportVO.getHigh())) {
            transportVO.setHigh(FormatUtil.formatDimension(transportVO.getHigh()));
        }
        //重
        if (StringUtils.isNotBlank(transportVO.getWeight())) {
            transportVO.setWeight(FormatUtil.formatDimension(transportVO.getWeight()));
        }
    }

    private boolean showRankLevelCache(Long userId, Integer rankLevel, Integer userType, UserRpcVO user) {
        Integer flag = redisUtil.getInt(GOODS_USER_LEVEL_SHOW_KEY + userId);
        if (flag != null) {
            return flag == 1;
        }
        boolean rankLevelShow = isShowRankLevel(userId, rankLevel, userType, user);
        // 设置缓存，1 表示显示，0 表示不显示
        redisUtil.set(GOODS_USER_LEVEL_SHOW_KEY + userId, rankLevelShow ? 1 : 0, Duration.ofHours(1));
        return rankLevelShow;
    }


    private boolean isShowRankLevel(Long userId, Integer rankLevel, Integer userType, UserRpcVO user) {
        if (rankLevel == null) {
            return false;
        }
        if (rankLevel > 2) {
            return true;
        }
        // 用户是否是直客
        if (Objects.equals(userType, UserBiIdentityEnum.ENTERPRISE_CARGO_OWNER.getCode())
                || Objects.equals(userType, UserBiIdentityEnum.SELF_CARGO_OWNER.getCode())) {
            return false;
        }
        //用户注册30天内
        if (user != null) {
            long betweenDay = DateUtil.betweenDay(user.getCtime(), new Date(), false);
            if (betweenDay <= 30) {
                return false;
            }
        }
        // ab测内不显示星级
        return !abTestRemoteService.getInABTest(ABTestCode.GOODS_SEARCH_RANK_LEVEL_SHOW_ABTEST_CODE, userId);
    }

    /**
     * 是否显示现金奖标签
     *
     * @param transportVO
     */
    private void showCashLabel(TransportVO transportVO, TransportLabelJsonVO labelJsonVO, Integer userType, UserRpcVO user) {
        // 列表页“现金奖”打标逻辑，满足任一条件（a/b/c满足其一就可以）
        // a. 技术服务费>0的货源（免佣后不打签，不考虑新注册司机b条）&活动名单里的用户（车主ID）
        // b. 直客发布2小时未成交的货源&活动名单里的用户（车主ID）
        // c. 未首履过（没有剔除风险单）的直客（货主）发布的货源&活动名单里的用户（车主ID）
        try {

            if (Objects.equals(labelJsonVO.getCommissionTransport(), 1)
                    && transportVO.getTecServiceFee() != null && transportVO.getTecServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                transportVO.setIsCashPrizeActivityGoods(YesNoEnum.Y.getCode());
                return;
            }
            // 直客发布2小时未成交的货源
            if (Objects.equals(userType, UserBiIdentityEnum.ENTERPRISE_CARGO_OWNER.getCode())
                    || Objects.equals(userType, UserBiIdentityEnum.SELF_CARGO_OWNER.getCode())) {

                if (transportVO.getReleaseTime().before(DateUtil.offsetHour(new Date(), -2))) {
                    transportVO.setIsCashPrizeActivityGoods(YesNoEnum.Y.getCode());
                    return;
                }
                // 未首履过（没有剔除风险单）的直客（货主）发布的货源
                if (user != null) {
                    int count = customFirstOrderRecordService.countFinishOrder(user.getCellPhone());

                    if (count <= 0) {
                        transportVO.setIsCashPrizeActivityGoods(YesNoEnum.Y.getCode());
                    }
                }
            }
        } catch (Exception e) {
            log.error("打现金奖标签异常，srcMsgId:{}", transportVO.getSrcMsgId(), e);
        }
    }

    /**
     * 是否在活动名单
     */
    private boolean isJoinActivity(Long userId, ActivityTypeEnum activityType) {
        if (userId == null) {
            return false;
        }
        // 如果不在名单里，不打标
        Integer activityFlag = redisUtil.getInt(ACTIVITY_CASH_PREFIX + userId);
        if (activityFlag == null) {
            boolean b = marketingActivityRemoteService.userIsValidByType(activityType.getId(), userId);
            activityFlag = b ? 1 : 0;
            redisUtil.set(ACTIVITY_CASH_PREFIX + userId, activityFlag, Duration.ofHours(1));
        }
        // 如果不在活动名单里，不打标
        return Objects.equals(activityFlag, 1);
    }

    /**
     * 是否显示加价标签
     *
     * @param transportEarliestPriceMap
     * @param transportVO
     */
    private void showAddPriceLabel(Map<Long, BigDecimal> transportEarliestPriceMap, TransportVO transportVO) {
        if (StringUtils.isNotBlank(transportVO.getPrice())) {
            BigDecimal priceDec = new BigDecimal(transportVO.getPrice());
            // 当前运费与最开始有运费时的运费比较加价了多少钱
            BigDecimal transportEarliestPrice = transportEarliestPriceMap.get(transportVO.getSrcMsgId());
            if (transportEarliestPrice != null && transportEarliestPrice.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal subtract = priceDec.subtract(transportEarliestPrice);
                if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                    transportVO.setAddPriceNum(subtract);
                }
            }

            transportVO.setActualPrice(transportVO.getPrice());
            // 到手运费 = 发货填写的运费 - 不退还定金 + 平台补贴
            BigDecimal actualPrice = priceDec;
            if (transportVO.getRefundFlag() != null && transportVO.getRefundFlag() == 0 && transportVO.getInfoFee() != null && transportVO.getInfoFee().compareTo(BigDecimal.ZERO) > 0) {
                actualPrice = priceDec.subtract(transportVO.getInfoFee());
            }
            if (transportVO.getPerkPrice() != null) {
                actualPrice = actualPrice.add(new BigDecimal(transportVO.getPerkPrice()));
            }
            if (actualPrice.compareTo(BigDecimal.ZERO) > 0) {
                transportVO.setActualPrice(actualPrice.setScale(0, RoundingMode.DOWN).toPlainString());
            }
        }
    }

    public Map<Long, BigDecimal> getTransportEarliestPrice(List<Long> srcMsgIds) {
        HashMap<Long, BigDecimal> result = new HashMap<>();
        if (CollectionUtils.isNotEmpty(srcMsgIds)) {
            // 构建所有需要查询的 Redis key
            List<String> keys = srcMsgIds.stream().map(srcMsgId -> EARLIEST_PRICE_CACHE_KEY + srcMsgId).collect(Collectors.toList());

            // 批量获取 Redis 值
            List<String> values = stringRedisTemplate.opsForValue().multiGet(keys);

            // 处理结果
            if (values != null) {
                for (int i = 0; i < keys.size(); i++) {
                    String value = values.get(i);
                    if (StringUtils.isNotBlank(value)) {
                        try {
                            Long srcMsgId = srcMsgIds.get(i);
                            result.put(srcMsgId, new BigDecimal(value));
                        } catch (NumberFormatException e) {
                            // 忽略无法转换为 BigDecimal 的值
                        }
                    }
                }
            }
        }
        return result;
    }


    /**
     * 判断是否应该显示超额投保标签：直客（个人货主或企业货主）发布的货源 或优车2.0 或抽佣货源
     */
    private Integer shouldShowExcessCoverageLabel(TransportLabelJsonVO labelJson, Integer userType) {
        // 优车2.0 或 抽佣货源
        if (labelJson != null && (Objects.equals(labelJson.getGoodCarPriceTransport(), 1) || Objects.equals(labelJson.getCommissionTransport(), 1))) {
            return 1;
        }
        // 或 直客发布货源（直客=个人货主或企业货主）
        if (Objects.equals(userType, UserBiIdentityEnum.ENTERPRISE_CARGO_OWNER.getCode()) || Objects.equals(userType, UserBiIdentityEnum.SELF_CARGO_OWNER.getCode())) {
            return 1;
        }
        return 0;
    }

    /**
     * 判断是否显示优惠券标签：开关开启 + 直客（个人货主或企业货主）发布的货源 或优车2.0 或抽佣货源
     */
    private Integer shouldShowDepositCouponsLabel(TransportLabelJsonVO labelJson, Integer userType) {
        if (Objects.equals(shouldShowExcessCoverageLabel(labelJson, userType), 1)
                && Objects.equals(configService.getIntValue("show_deposit_coupons_label_switch", 0), 1)) {
            return 1;
        }
        return 0;
    }

    @Override
    public List<TransportDO> searchHallList(BaseTransportSearchDTO searchDTO) {

        return transportMapper.searchHallList(searchDTO);
    }

    @Override
    public List<TransportDO> selectBySrcMsgIdsAndStatus(List<Long> srcMsgIdList) {
        return transportMapper.selectBySrcMsgIds(srcMsgIdList);
    }

    @Override
    public TransportDO getBySrcMsgIdAndStatus(Long srcMsgId) {
        return transportMapper.getBySrcMsgIdAndStatus(srcMsgId);
    }


    @Override
    public List<TransportDO> selectValidByTsIds(List<Long> tsIdList) {
        return transportMapper.selectValidByTsIds(tsIdList);
    }

    @Override
    public List<TransportDO> selectByTsIds(List<Long> tsIdList) {
        return transportMapper.selectByTsIds(tsIdList);
    }

    /**
     * 根据tsId集合查询货源srcMsgId集合
     *
     * @param tsIdList
     * @return
     */
    @Override
    public List<TransportDO> selectSrcMsgIdByTsIds(List<Long> tsIdList) {
        return transportMapper.selectSrcMsgIdByTsIds(tsIdList);
    }

    /**
     * 过滤有效的货源id
     *
     * @param srcMsgIds 货源id
     */
    @Override
    public List<Long> filterValidTransport(List<Long> srcMsgIds) {
        if (CollectionUtils.isNotEmpty(srcMsgIds)) {
            return transportMainMapper.filterValidTransport(srcMsgIds);
        }
        return List.of();
    }

    /**
     * 过滤测试账号数据
     *
     * @param transportVOList
     * @param userId
     */
    @Override
    public List<TransportVO> filterTest(List<TransportVO> transportVOList, Long userId) {
        List<Long> defaultShieldingUserIds = shieldingShipperService.getDefaultShieldingUserIds(userId);
        if (CollectionUtils.isEmpty(defaultShieldingUserIds)) {
            return transportVOList;
        }
        List<TransportVO> result = new ArrayList<>();
        for (TransportVO vo : transportVOList) {
            if (defaultShieldingUserIds.contains(vo.getUserId())) {
                continue;
            }
            result.add(vo);
        }
        return result;
    }

    /**
     * 屏蔽发货人逻辑，并截取相应页数
     *
     * @param shieldingUserList 当前用户屏蔽的货主id
     */
    @Override
    public List<TransportVO> filterPublishUser(List<Long> shieldingUserList, List<Long> shieldSrcMsgIds, List<TransportVO> transportVOList, Integer pageSize) {
        if (CollectionUtils.isEmpty(transportVOList)) {
            return transportVOList;
        }

        // 过滤掉屏蔽的货主和货源
        List<TransportVO> list = transportVOList.stream().filter(t -> !shieldingUserList.contains(t.getUserId())).filter(t -> !shieldSrcMsgIds.contains(t.getSrcMsgId())).limit(pageSize).collect(Collectors.toList());

        // 如果这一批次全都被屏蔽了，头尾都放进去
        if (CollectionUtils.isEmpty(list)) {
            list.add(transportVOList.get(0));
            if (transportVOList.size() > 1) {
                list.add(transportVOList.get(transportVOList.size() - 1));
            }
        }
        return list;
    }

    @Override
    public TransportDO getByTsOrderNoAndStatus(String tsOrderNo) {
        return transportMapper.getByTsOrderNoAndStatus(tsOrderNo);
    }

    @Override
    public void handleSimilarTransport(List<TransportVO> transportVOList, Long userId) {
        if (CollUtil.isEmpty(transportVOList)) {
            return;
        }
        try {
            Map<String, List<TransportVO>> similarGroup = transportVOList.stream().collect(Collectors.groupingBy(TransportVO::getSimilarityCode));
            similarGroup.forEach((similarCodeKey, similarTransportVOList) -> {
                similarityTransportService.sortSimilarityList(similarTransportVOList);
                TransportVO transportVO = similarTransportVOList.get(0);
                transportVO.setShowSimilarGoods(YesNoEnum.Y.getCode());
                transportVO.setSpecialMark(transportVO.getSpecialMark() + SpecialMarkEnum.FIRST_SIMILARITY_GOODS.getValue());
            });
        } catch (Exception e) {
            log.error("handleSimilarTransport error,userId:{}", userId, e);
        }
    }

    @Override
    public Long searchHallCount(SearchHallCountDTO searchHallCountDTO) {

        return transportMapper.searchHallCount(searchHallCountDTO);
    }

    @Override
    public void handleFixedTransport(BaseTransportSearchDTO searchDTO, List<TransportVO> transportVOList, Long userId) {

        // 如果不是下拉刷新，就不进行插入
        if (!Objects.equals(searchDTO.getQueryType(), QueryTypeEnum.AUTO.getCode()) && !Objects.equals(searchDTO.getQueryType(), QueryTypeEnum.TO_DOWN.getCode())) {
            return;
        }
        // 未登录的也不插入
        if (userId == null) {
            return;
        }
        try {
            Integer fixedLocation = configRemoteService.getIntValue(TRANSPORT_FIXED_LOCATION, 4);
            // 处理固定位置的货源
            if (CollUtil.isNotEmpty(transportVOList) && transportVOList.size() >= fixedLocation) {
                //0关闭，1全部开启 2奇数开启
                Integer fixedSwitch = configRemoteService.getIntValue(TRANSPORT_FIXED_LOCATION_SWITCH, 1);
                if (fixedSwitch != 0) {
                    // 根据规则找到符合的固定位货源，然后进行插入
                    TransportVO transportVO = searchFixedTransport(searchDTO, transportVOList, userId);
                    // 有符合条件的货源，打上标签，插入第四个位置
                    if (transportVO != null && (fixedSwitch == 1 || (fixedSwitch == 2 && transportVO.getSrcMsgId() % 2 == 1))) {
                        transportVO.setFixedTransport(YesNoEnum.Y.getCode());
                        transportVO.setSpecialMark(transportVO.getSpecialMark() + SpecialMarkEnum.FIXED_POSITION_GOODS.getValue());
                        // 固定位货源要展示出来
                        transportVO.setShowSimilarGoods(YesNoEnum.Y.getCode());
                        // 看看原来的货源有没有跟这个货源重复的，如果有，则剔除
                        transportVOList.removeIf(t -> Objects.equals(t.getSrcMsgId(), transportVO.getSrcMsgId()));
                        // 对货源进行打标
                        handleTransportTag(List.of(transportVO), searchDTO.getUserId());
                        // 加密货源字段
                        hideSensitiveInfo(List.of(transportVO), searchDTO.getUserId());
                        // 插入到第四个位置，
                        if (CollUtil.isNotEmpty(transportVOList) && transportVOList.size() < fixedLocation) {
                            transportVOList.add(transportVO);
                        } else {
                            transportVOList.add(fixedLocation - 1, transportVO);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("handleFixedTransport error,userId:{}", userId, e);
        }

    }

    private TransportVO searchFixedTransport(BaseTransportSearchDTO searchDTO, List<TransportVO> transportVOList, Long userId) {
        TransportVO transportVO = null;
        String searchDTOMD5 = getFixedTransportKey(searchDTO);

        // 获取已经在展示的货源
        Long showingSrcMsgId = redisUtil.getLong(FIXED_SHOWING_PREFIX + searchDTOMD5);

        // 要将相似货源不展示货源也要过滤掉
        Set<Long> similarExcludes = transportVOList.stream().filter(t -> !Objects.equals(t.getShowSimilarGoods(), YesNoEnum.Y.getCode())).map(TransportVO::getSrcMsgId).collect(Collectors.toSet());
        // 查询货源已经展示过的货源，那么这个货源也要移除掉
        Set<Long> shownExcludes = redisUtil.membersLongSet(FIXED_SHOWN_PREFIX + searchDTOMD5);
        // 如果缓存中有正在展示的货源，在确认能用之前那么就从排除条件中剔除掉
        shownExcludes.remove(showingSrcMsgId);

        // 合并排除的货源和已经展示过的货源，然后放到排除条件中
        Collection<Long> unionSet = CollUtil.union(similarExcludes, shownExcludes);
        List<Long> excludeSrcMsgIds = new ArrayList<>(searchDTO.getExcludeSrcMsgIds());

        // 取出已展示货源中本身就被屏蔽的货源，后面清空已展示货源时这部分要留着
        // intersection存储既在已展示的集合中，又在用户排除的集合中
        Set<Long> intersection = new HashSet<>(shownExcludes);
        intersection.retainAll(excludeSrcMsgIds);

        excludeSrcMsgIds.addAll(unionSet);
        searchDTO.setExcludeSrcMsgIds(excludeSrcMsgIds);

        // 如果缓存中有数据，那么就看状态
        if (showingSrcMsgId != null) {
            // 如果没有要过滤的货源或者过滤的货源不包含缓存中的货源，那么就看这个货源现在还符不符合插入的条件，不符合的话直接删除缓存
            if (CollUtil.isEmpty(excludeSrcMsgIds) || !excludeSrcMsgIds.contains(showingSrcMsgId)) {
                List<Long> useSrcMsgIds = new ArrayList<>();
                useSrcMsgIds.add(showingSrcMsgId);
                searchDTO.setUseSrcMsgIds(useSrcMsgIds);
                List<TransportEsDO> transportEsDOList = esTransportService.searchFixedTransport(searchDTO);
                // 缓存中的数据能用的话，直接返回
                if (CollUtil.isNotEmpty(transportEsDOList)) {
                    TransportEsDO transportEsDO = transportEsDOList.get(0);
                    return TransportConverter.INSTANCE.transportEsDO2VO(transportEsDO);
                }
            }
            // 如果缓存中的货源已经被过滤掉了或者缓存的货源已经不在发布中状态或者不符合条件了，那么就删除该缓存重新获取
            redisUtil.delete(FIXED_SHOWING_PREFIX + searchDTOMD5);
        }
        // 如果缓存中没有数据，按照规则找到符合的固定位货源，然后进行插入
        // 先将货源id置空查所有的
        searchDTO.setUseSrcMsgIds(null);
        List<TransportEsDO> transportEsDOList = esTransportService.searchFixedTransport(searchDTO);
        // 如果查询到了，进行过滤规则及放入缓存的处理
        if (CollUtil.isNotEmpty(transportEsDOList)) {
            // 按规则处理货源，然后放入缓存中
            transportVO = dealMatchTransport(transportEsDOList);
        } else {
            // 没有查出来货源还要分两种情况，一种是本身就没有，一种是货源都被移出了，还得查一次
            if (CollUtil.isNotEmpty(shownExcludes)) {
                // 剔除已经展示过的货源，然后重新查询
                shownExcludes.removeAll(intersection);
                if (CollUtil.isNotEmpty(shownExcludes)) {
                    searchDTO.getExcludeSrcMsgIds().removeAll(shownExcludes);
                    transportEsDOList = esTransportService.searchFixedTransport(searchDTO);
                    if (CollUtil.isNotEmpty(transportEsDOList)) {
                        // 如果查询到了，按规则处理货源
                        transportVO = dealMatchTransport(transportEsDOList);
                    }
                }
            }
            // 第一次没查到时，第二次查完了不管有没有查到就可以将已经展示过的货源清空了
            redisUtil.delete(FIXED_SHOWN_PREFIX + searchDTOMD5);
        }
        // 放入缓存
        if (transportVO != null) {
            // 放入缓存中展示五分钟
            redisUtil.set(FIXED_SHOWING_PREFIX + searchDTOMD5, transportVO.getSrcMsgId(), Duration.ofMinutes(5));
            // 放入已展示的缓存中，过期时间可以放到24小时
            redisUtil.addSet(FIXED_SHOWN_PREFIX + searchDTOMD5, Duration.ofHours(24), transportVO.getSrcMsgId());
        }
        // 没有符合条件的货源，直接返回null
        return transportVO;
    }

    /**
     * 将查出来符合搜索条件的货源按照优先级找出最符合的一条货源
     *
     * @param transportEsDOList
     * @return
     */
    private TransportVO dealMatchTransport(List<TransportEsDO> transportEsDOList) {
        // 先取优车2.0的，再取非优车2.0的
        TransportEsDO transportEsDO;
        List<TransportEsDO> excellentGoodsTwoList = transportEsDOList.stream().filter(t -> Objects.equals(t.getExcellentGoodsTwo(), ExcellentGoodsTwoEnum.EXCELLENT_GOODS_TWO.getCode())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(excellentGoodsTwoList)) {
            excellentGoodsTwoList.sort(Comparator.comparing(TransportEsDO::getReleaseTime));
            transportEsDO = excellentGoodsTwoList.get(0);
        } else {
            // 如果没有优车2.0的，就取非优车2.0的货源中的有价货源
            transportEsDOList.sort(Comparator.comparing(TransportEsDO::getReleaseTime));
            transportEsDO = transportEsDOList.get(0);
        }
        // 移出
        return TransportConverter.INSTANCE.transportEsDO2VO(transportEsDO);
    }

    private String getFixedTransportKey(BaseTransportSearchDTO searchDTO) {
        String md5Str = searchDTO.getStartProvinc() + searchDTO.getStartCity() + searchDTO.getStartArea() + searchDTO.getStartDistance() + searchDTO.getDestProvinc() + searchDTO.getDestCity() + searchDTO.getDestArea() + searchDTO.getDestDistance() + searchDTO.getGoodsName() + searchDTO.getStartLoadingTime() + searchDTO.getEndLoadingTime() + searchDTO.getPriceFlag() + searchDTO.getRefundFlag() + searchDTO.getPublishType() + searchDTO.getStartWeight() + searchDTO.getEndWeight() + searchDTO.getMinLength() + searchDTO.getMaxLength() + searchDTO.getMinWidth() + searchDTO.getMaxWidth() + searchDTO.getMinHeight() + searchDTO.getMaxHeight() + searchDTO.getMinPrice() + searchDTO.getMaxPrice() + searchDTO.getCarId() + searchDTO.getUserId();
        return searchDTO.getUserId() + ":" + DateUtil.formatDate(new Date()) + "_" + MD5Utils.GetMD5Code(md5Str);
    }


    @Override
    public List<TransportDO> searchHallCountDetail(SearchHallCountDTO searchDTO) {
        return transportMapper.searchHallCountDetail(searchDTO);
    }

    /**
     * 查询最新价格
     *
     * @param srcMsgIdList
     * @return
     */
    @Override
    public Map<Long, String> getLatestPrice(List<Long> srcMsgIdList) {
        if (CollectionUtils.isEmpty(srcMsgIdList)) {
            return Map.of();
        }
        Map<Long, String> map = new HashMap<>();
        List<TransportDO> latestPriceList = transportMapper.getLatestPrice(srcMsgIdList);

        if (CollUtil.isNotEmpty(latestPriceList)) {
            for (TransportDO transportDO : latestPriceList) {
                map.put(transportDO.getSrcMsgId(), transportDO.getPrice());
            }
        }
        return map;
    }

    /**
     * 根据userId判断是走es还是db
     *
     * @param userId
     * @return
     */
    @Override
    public EsOrDbEnum getEsDbEnum(Long userId) {
        // 使用开关判断用户走es还是走数据库，做主备的同时进行分流
        Integer esSwitch = configRemoteService.getIntValue(GOODS_SEARCH_ES_DB, 1);
        if (EsOrDbEnum.ES.code.equals(esSwitch)) {
            return EsOrDbEnum.ES;
        }
        if (EsOrDbEnum.DB.code.equals(esSwitch)) {
            return EsOrDbEnum.DB;
        }
        // 判断是否走es，如果开关为3，则根据用户id进行分流
        return EsOrDbEnum.HALF.code.equals(esSwitch) && userId != null && userId % 2 == 1 ? EsOrDbEnum.ES : EsOrDbEnum.DB;
    }

    @Override
    public void dealBenefitLabel(List<? extends TransportVO> transportList) {
        if (CollectionUtils.isEmpty(transportList)) {
            return;
        }
        int switchOn = configRemoteService.getIntValue(TytConfigKey.BENEFIT_LABEL_SHOW_SWITCH, 0);
        if (switchOn == 0) { //关闭
            return;
        }
        for (TransportVO transportVO : transportList) {
            BenefitLabelVO labelVO = getBenefitLabel(transportVO, switchOn);
            transportVO.setBenefitLabel(labelVO.getLabel());
            transportVO.setBenefitLabelCode(labelVO.getCode());
        }
    }


    @Override
    public BenefitLabelVO getBenefitLabel(TransportVO transportVO, Integer showSwitch) {
        TransportLabelJsonVO labelJsonVO = JSON.parseObject(transportVO.getLabelJson(), TransportLabelJsonVO.class);
        labelJsonVO = labelJsonVO == null ? new TransportLabelJsonVO() : labelJsonVO;
        BenefitLabelEnum label = handelBenefitLabel(labelJsonVO, transportVO);
        boolean isShow = (showSwitch == 2) || (showSwitch == 1 && transportVO.getSrcMsgId() % 2 == 1);
        BenefitLabelVO labelVO = new BenefitLabelVO();
        if (isShow && null != label) {
            labelVO.setLabel(label.getLabel());
        }
        String benefitLabelCode = (isShow ? "1" : "0") + (null == label ? "0" : label.getCode().toString());
        labelVO.setCode(benefitLabelCode);
        return labelVO;
    }

    private BenefitLabelEnum handelBenefitLabel(TransportLabelJsonVO labelJsonVO, TransportVO transportVO) {
        if (Objects.equals(labelJsonVO.getPriceLabel(), 1)) {
            return BenefitLabelEnum.HIGH_PRICE;
        }
        // 抽佣货源且技术服务费为0
        if (Objects.equals(labelJsonVO.getCommissionTransport(), 1)
                && (transportVO.getTecServiceFee() == null || transportVO.getTecServiceFee().compareTo(BigDecimal.ZERO) <= 0)) {
            return BenefitLabelEnum.NO_TEC_FEE;
        }
        //拨打数量 >=3
//        String callCountCacheKey = GOODS_CALL_COUNT_KEY + transportVO.getSrcMsgId();
//        Integer callCount = redisUtil.getInt(callCountCacheKey);
//        if (callCount == null) {
//            callCount = userCallPhoneRecordService.getDistinctTsCountBySrcMsgId(transportVO.getSrcMsgId());
//            redisUtil.set(callCountCacheKey, callCount, Duration.ofMinutes(5));
//        }
//        if (callCount >= 3) {
//            return BenefitLabelEnum.HIGH_HEAT;
//        }
        // 当前时间在首发时间40分钟内
        Date date = DateUtil.offsetMinute(new Date(), -40).toJdkDate();
        if (DateUtil.compare(date, transportVO.getReleaseTime()) <= 0) {
            return BenefitLabelEnum.NEW_GOODS;
        }
        return null;
    }

}
