package com.teyuntong.goods.search.service.biz.route.service;

import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.goods.search.service.biz.route.dto.OftenRouteSearchDTO;
import com.teyuntong.goods.search.service.biz.route.vo.OftenRouteBubbleVO;
import com.teyuntong.goods.search.service.biz.route.vo.OftenRouteSearchVO;
import com.teyuntong.goods.search.service.biz.route.vo.RouteTransportCountVo;
import com.teyuntong.goods.search.service.biz.route.vo.RouteTransportQuery;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 常跑路线数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface RouteDataService {

    /**
     * 根据id获取货源信息
     */
    List<TransportVO> getTransportByIds(List<Long> transportIdList);

    /**
     * 根据redis中路线的货源id查询货源信息
     */
    List<TransportVO> queryRedisTransportIdList(Date nowTime, RouteTransportQuery transportQuery);

    /**
     * 查询路线货源详情(带缓存)
     */
    List<TransportVO> getRouteTransportList(List<TransportVO> redisIndexList);

    /**
     * 多次获取路线货源详情（包含用户过滤）
     */
    List<TransportVO> getRouteTransportList(RouteDataService routeDataService, RouteTransportQuery transportQuery);

    /**
     * 路线货源条数
     */
    RouteTransportCountVo getOneRouteCount(Date nowTime, Long routeId);

    /**
     * 路线货源条数
     */
    List<RouteTransportCountVo> getRouteTransportCount(List<Long> routeIdList);

    /**
     * 常跑路线气泡数量接口
     */
    OftenRouteBubbleVO bubbleCount(OftenRouteSearchDTO searchDTO);

    /**
     * 常跑路线找货大厅接口
     */
    OftenRouteSearchVO hallList(OftenRouteSearchDTO searchDTO);
}
