package com.teyuntong.goods.search.service.biz.goods.converter;

import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportExtendDO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportMainDO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportMainExtendDO;
import com.teyuntong.goods.search.service.biz.goods.es.entity.TransportEsDO;
import com.teyuntong.goods.search.service.biz.goods.vo.GoodsStatusVO;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.goods.search.service.biz.record.vo.SearchRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/07/15 17:30
 */
@Mapper
public interface TransportConverter {

    TransportConverter INSTANCE = Mappers.getMapper(TransportConverter.class);

    // 明确指定映射方法
    default List<TransportVO> convertDOs2VOs(List<TransportDO> transportDOList) {
        return transportDOList.stream()
                .map(this::transportDO2VO)
                .collect(Collectors.toList());
    }

    @Mapping(target = "pubDate", source = "ctime")
    List<TransportVO> convertEsDOs2VOs(List<TransportEsDO> transportEsDO);

    @Mappings({
            @Mapping(target = "pubDate", source = "ctime")
    })
    TransportVO transportDO2VO(TransportDO transportDO);

    @Mappings({
            @Mapping(target = "pubDate", source = "ctime")
    })
    TransportVO transportEsDO2VO(TransportEsDO transportEsDO);

    @Mappings({
            @Mapping(target = "width", source = "wide"),
            @Mapping(target = "height", source = "high"),
            @Mapping(target = "startPosition", source = "startPoint"),
            @Mapping(target = "destPosition", source = "destPoint"),
            @Mapping(target = "goodId", source = "id"),
            @Mapping(target = "remark", source = "taskContent"),
            @Mapping(target = "pubDate", source = "ctime"),
            @Mapping(target = "goodStatus", source = "status"),
    })
    SearchRecordVO transport2RecordVO(TransportMainDO transportMainDO);

    GoodsStatusVO transport2GoodsStatus(TransportDO transportDO);

    /**
     * 货源扩展类型转VO
     */
    @Mapping(target = "id", ignore = true)
    void transportExtend2VO(@MappingTarget TransportVO transportVO, TransportExtendDO extendDO);

    @Mapping(target = "id", ignore = true)
    void transportMainExtend2VO(@MappingTarget TransportVO transportVO, TransportMainExtendDO extendDO);
}
