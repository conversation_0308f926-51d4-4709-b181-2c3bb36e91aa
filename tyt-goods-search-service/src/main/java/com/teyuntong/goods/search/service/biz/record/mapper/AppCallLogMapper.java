package com.teyuntong.goods.search.service.biz.record.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.record.entity.AppCallLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 记录APP拨打电话的信息（电话标注）  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Mapper
@DS("tyt")
public interface AppCallLogMapper extends BaseMapper<AppCallLogDO> {

    /**
     * 获取最近的拨打记录列表
     *
     * @param userId    拨打人id
     * @param startDate 开始时间
     */
    List<AppCallLogDO> getRecentCallList(@Param("userId") Long userId, @Param("startDate") Date startDate);

    List<Long> getRecentCallListSrcMsgId(@Param("userId") Long userId, @Param("startDate") Date startDate);

    /**
     * 根据srcMsgId和userId查询记录
     *
     * @param userId
     * @param srcMsgId
     * @return
     */
    AppCallLogDO getBySrcMsgIdAndUserId(@Param("userId") Long userId, @Param("srcMsgId") Long srcMsgId);

    /**
     * 获取用户近期记录数量
     * @param userId
     * @param startDate
     * @return
     */
    Integer getRecentCountByUserId(@Param("userId") Long userId, @Param("startDate") Date startDate);
}
