package com.teyuntong.goods.search.service.common.aop;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * ab测试的本地缓存切面
 *
 * <AUTHOR>
 * @since 2024/07/15 15:22
 */
@Slf4j
@Aspect
@Component
public class ABTestLocalCacheAspect {

    /**
     * 本地缓存，适合缓存开关、公共资源等调用频繁，数据量不大的场景
     * 默认淘汰策略：LRU最近最少使用
     */
    public static Cache<Object, Object> localCache = Caffeine.newBuilder()
            // 最大条数
            .maximumSize(1000)
            // 写入后10s过期
            .expireAfterWrite(10, TimeUnit.SECONDS)
            .build();

    /**
     * AB测试只拦截get开头的方法
     */
    @Pointcut("execution(* com.teyuntong.goods.search.service.remote.basic.ABTestRemoteService.get*(..))")
    public void aBTestServiceGet() {
    }

    /**
     * 切点，多个可以用 || 隔开，例如：configServiceGet() || resourceServiceGet()
     */
    @Pointcut("aBTestServiceGet()")
    public void point() {
    }

    @Around("point()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {

        // 先查询本地缓存
        String cacheKey = Arrays.stream(joinPoint.getArgs())
                .map(t -> t == null ? "null" : t.toString())
                .collect(Collectors.joining("-"));
        Object ifPresent = localCache.getIfPresent(cacheKey);
        if (ifPresent != null) {
//            log.debug("ABTest命中了本地缓存，key:{}", cacheKey);
            return ifPresent;
        }

        // 执行原方法
        Object result = joinPoint.proceed();

        // 本地缓存，远程调用失败会返回null
        if (result != null) {
            localCache.put(cacheKey, result);
        }

        return result;
    }
}
