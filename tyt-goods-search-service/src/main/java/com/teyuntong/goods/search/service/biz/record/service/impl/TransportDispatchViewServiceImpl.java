package com.teyuntong.goods.search.service.biz.record.service.impl;

import com.teyuntong.goods.search.service.biz.record.entity.TransportDispatchViewDO;
import com.teyuntong.goods.search.service.biz.record.entity.TransportDispatchViewDetailDO;
import com.teyuntong.goods.search.service.biz.record.mapper.TransportDispatchViewDetailMapper;
import com.teyuntong.goods.search.service.biz.record.mapper.TransportDispatchViewMapper;
import com.teyuntong.goods.search.service.biz.record.service.TransportDispatchViewService;
import com.teyuntong.goods.search.service.common.util.TransportUtil;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * 货源查看、联系统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportDispatchViewServiceImpl implements TransportDispatchViewService {

    private final TransportDispatchViewMapper transportDispatchViewMapper;
    private final TransportDispatchViewDetailMapper transportDispatchViewDetailMapper;

    @Override
    public TransportDispatchViewDO getByUserAndSrcMsgId(Long userId, Long srcMsgId) {
        return transportDispatchViewMapper.getByUserAndSrcMsgId(userId, srcMsgId);
    }

    @Override
    public Integer contactCount(Long userId) {
        return 0;
    }

    /**
     * 保存拨打或者查看统计表和明细表
     *
     * @param srcMsgId 货源id
     * @param type     1:查看  2：联系
     */
    @Override
    public void saveViewAndDetail(Long srcMsgId, Integer type) {
        LoginUserDTO user = LoginHelper.getRequiredLoginUser();
        saveOrUpdateView(user, srcMsgId, type);
        saveViewDetail(user, srcMsgId, type);
    }

    /**
     * 保存查看/联系统计
     */
    private void saveOrUpdateView(LoginUserDTO user, Long srcMsgId, Integer type) {
        TransportDispatchViewDO dispatchViewDO = transportDispatchViewMapper.getByUserAndSrcMsgId(user.getUserId(), srcMsgId);
        if (dispatchViewDO == null) {
            dispatchViewDO = new TransportDispatchViewDO();
            dispatchViewDO.setSrcMsgId(srcMsgId);
            dispatchViewDO.setCarUserId(user.getUserId());
            dispatchViewDO.setCarUserName(TransportUtil.formatUserName(user.getTrueName(), user.getUserId()));
            dispatchViewDO.setCarNickName(TransportUtil.formatUserName(user.getUserName(), user.getUserId()));
            dispatchViewDO.setCarPhone(user.getCellPhone());
            //1:查看  2：联系
            if (type == 1) {
                dispatchViewDO.setViewTime(new Date());
                dispatchViewDO.setViewCount(1);
                dispatchViewDO.setContactCount(0);
            } else {
                dispatchViewDO.setContactTime(new Date());
                dispatchViewDO.setContactCount(1);
                dispatchViewDO.setViewCount(0);
            }
            dispatchViewDO.setCreateTime(new Date());
            dispatchViewDO.setModifyTime(new Date());
            transportDispatchViewMapper.insert(dispatchViewDO);
        } else {
            //1:查看  2：联系
            if (type == 1) {
                dispatchViewDO.setViewTime(new Date());
                dispatchViewDO.setViewCount(dispatchViewDO.getViewCount() == null ? 0 : dispatchViewDO.getViewCount() + 1);
            } else {
                dispatchViewDO.setContactTime(new Date());
                dispatchViewDO.setContactCount(dispatchViewDO.getContactCount() == null ? 0 : dispatchViewDO.getContactCount() + 1);
            }
            dispatchViewDO.setModifyTime(new Date());
            transportDispatchViewMapper.updateById(dispatchViewDO);
        }
    }

    /**
     * 保存查看/联系详情
     */
    private void saveViewDetail(LoginUserDTO user, Long srcMsgId, Integer type) {
        TransportDispatchViewDetailDO dispatchViewDetailDO = new TransportDispatchViewDetailDO();
        dispatchViewDetailDO.setSrcMsgId(srcMsgId);
        dispatchViewDetailDO.setCarUserId(user.getUserId());
        dispatchViewDetailDO.setCarUserName(TransportUtil.formatUserName(user.getTrueName(), user.getUserId()));
        dispatchViewDetailDO.setCarNickName(TransportUtil.formatUserName(user.getUserName(), user.getUserId()));
        dispatchViewDetailDO.setCarPhone(user.getCellPhone());
        dispatchViewDetailDO.setType(type);
        dispatchViewDetailDO.setCreateTime(new Date());
        dispatchViewDetailDO.setModifyTime(new Date());
        transportDispatchViewDetailMapper.insert(dispatchViewDetailDO);
    }
}
