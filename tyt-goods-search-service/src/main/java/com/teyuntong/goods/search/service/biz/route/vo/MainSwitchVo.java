package com.teyuntong.goods.search.service.biz.route.vo;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2021/12/21 17:52
 */
@Getter
@Setter
public class MainSwitchVo {

    /**
     * 是否播报（0:不播报,1:播报）
     */
    @NotNull
    private Integer reportStatus;

    /**
     * 开始时间
     */
    @NotNull
    private Integer startTime;

    /**
     * 结束时间
     */
    @NotNull
    private Integer endTime;

    public Integer getReportStatus() {
        return reportStatus != null && reportStatus > 0 ? 1 : 0;
    }
}
