package com.teyuntong.goods.search.service.biz.record.service;


import com.github.pagehelper.PageInfo;
import com.teyuntong.goods.search.service.biz.goods.dto.SearchRecordQueryDTO;
import com.teyuntong.goods.search.service.biz.record.entity.TransportQuotedPriceDO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 无价货源报价表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
public interface TransportQuotedPriceService {

    /**
     * 获取最近的车主出价记录
     */
    PageInfo<TransportQuotedPriceDO> getRecentQuotedPage(SearchRecordQueryDTO queryDTO);

    /**
     * 获取今日报价记录
     */
    List<TransportQuotedPriceDO> getTodayQuoted(Long carUserId);

    /**
     * 获取近期报价记录数量
     */
    Integer getRecentCountByUserId(Long userId, Date today);

    /**
     * 获取报价记录
     * @param carUserId 车主id
     * @param srcMsgId 货源id
     */
    TransportQuotedPriceDO getQuoted(Long carUserId, Long srcMsgId);
}
