package com.teyuntong.goods.search.service.biz.goods.service;


import com.teyuntong.goods.search.service.biz.goods.entity.TransportHistoryDO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * transport表历史记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public interface TransportHistoryService {

    TransportHistoryDO getBySrcMsgIdAndOriginal(Long srcMsgId);

    /**
     * 根据srcMsgId查询原始价格
     *
     * @return {"srcMsgId":"price"}
     */
    Map<Long, String> getOriginalPrice(@Param("srcMsgIds") List<Long> srcMsgIds);
}
