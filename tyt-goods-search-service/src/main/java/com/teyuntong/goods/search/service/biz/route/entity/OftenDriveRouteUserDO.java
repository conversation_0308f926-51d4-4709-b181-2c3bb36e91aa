package com.teyuntong.goods.search.service.biz.route.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 常跑路线主表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Getter
@Setter
@TableName("often_drive_route_user")
public class OftenDriveRouteUserDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 路线id
     */
    private Long routeId;

    /**
     * 是否播报（0:不播报,1:播报）
     */
    private Integer reportStatus;

    /**
     * 是否启用（1启用；0禁用）
     */
    private Integer enableStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}
