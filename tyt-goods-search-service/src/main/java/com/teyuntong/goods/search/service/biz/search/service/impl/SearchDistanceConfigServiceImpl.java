package com.teyuntong.goods.search.service.biz.search.service.impl;

import com.teyuntong.goods.search.service.biz.search.mapper.SearchDistanceConfigMapper;
import com.teyuntong.goods.search.service.biz.search.service.SearchDistanceConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 搜索外扩距离配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SearchDistanceConfigServiceImpl implements SearchDistanceConfigService {

    private final SearchDistanceConfigMapper searchDistanceConfigMapper;

    /**
     * 返回搜索距离，优先取城市，不存在取省
     */
    @Override
    public Integer getOptimalDistance(String province, String city) {
        return searchDistanceConfigMapper.getOptimalDistance(province, city);
    }
}
