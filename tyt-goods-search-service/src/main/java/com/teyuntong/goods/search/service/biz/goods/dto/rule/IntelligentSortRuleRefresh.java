package com.teyuntong.goods.search.service.biz.goods.dto.rule;

import lombok.Getter;
import lombok.Setter;

/**
 * 智能排序因子：刷新规则
 * 格式：{"useCard":[{"interval":5,"score":100},{"interval":30,"score":80},{"interval":60,"score":20}],"other":[{"interval":5,"score":50},{"interval":30,"score":40},{"interval":60,"score":0}]}
 * useCard是使用曝光卡刷新，other是非曝光卡刷新，interval是距上次刷新间隔，score是分数
 *
 * <AUTHOR>
 * @since 2024/8/29 15:59
 */
@Getter
@Setter
public class IntelligentSortRuleRefresh {
    /**
     * 刷新时间
     */
    private Float interval;
    /**
     * 评分
     */
    private Integer score;

}
