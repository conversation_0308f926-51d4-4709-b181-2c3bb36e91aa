package com.teyuntong.goods.search.service.biz.record.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.record.entity.SpecialCarDispatchDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 专车派单详情表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Mapper
@DS("tyt")
public interface SpecialCarDispatchDetailMapper extends BaseMapper<SpecialCarDispatchDetailDO> {
    /**
     * 查询货源是否是指派货源
     *
     * @param goodsId
     * @param userId
     * @return
     */
    Integer selectCountByUserAndGoodsId(@Param("goodsId") Long goodsId, @Param("userId") Long userId);


}
