package com.teyuntong.goods.search.service.biz.record.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 加价档位配置，运费区间左闭右开
 * 格式：
 * [{"start":0,"end":1000,"options":[50,100,150,200]},
 * {"start":1000,"end":2000,"options":[100,150,200,300]},
 * {"start":2000,"end":5000,"options":[100,200,300,500]},
 * {"start":5000,"end":100000000,"options":[100,200,500,800]}]
 *
 * <AUTHOR>
 * @since 2024/12/14 18:02
 */
@Getter
@Setter
public class TransportAddPriceOptionDTO {

    /**
     * 起始值，包含
     */
    private Integer start;

    /**
     * 截止值，不包含
     */
    private Integer end;

    /**
     * 加价选项列表
     */
    private List<Integer> options;

}
