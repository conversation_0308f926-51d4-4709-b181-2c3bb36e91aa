package com.teyuntong.goods.search.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.car.service.SuperiorCarSignRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 查询用户权益
 *
 * <AUTHOR>
 * @since 2024/07/17 16:43
 */
@FeignClient(name = "tyt-user-service", path = "user", contextId = "SuperiorCarSignRpcService", fallbackFactory = SuperiorCarSignRemoteService.SuperiorCarSignRemoteServiceFallback.class)
public interface SuperiorCarSignRemoteService extends SuperiorCarSignRpcService {

    @Component
    class SuperiorCarSignRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<SuperiorCarSignRemoteService> {
        public SuperiorCarSignRemoteServiceFallback() {
            super(true, SuperiorCarSignRemoteService.class);
        }
    }
}
