package com.teyuntong.goods.search.service.biz.record.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 货源查看、联系详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@Getter
@Setter
@TableName("tyt_transport_dispatch_view_detail")
public class TransportDispatchViewDetailDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货源id
     */
    private Long srcMsgId;

    /**
     * 车主id
     */
    private Long carUserId;

    /**
     * 车主姓名
     */
    private String carUserName;

    /**
     * 车主昵称
     */
    private String carNickName;

    /**
     * 车主手机号
     */
    private String carPhone;

    /**
     * 类型（1查看；2联系）
     */
    private Integer type;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}
