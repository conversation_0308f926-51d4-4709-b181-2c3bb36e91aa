package com.teyuntong.goods.search.service.common.property;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 读取配置文件
 *
 * <AUTHOR>
 * @since 2024/07/15 14:47
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "tyt.key")
public class TytEncryptProperty {

    private String xxtea;
    private String aes;

}
