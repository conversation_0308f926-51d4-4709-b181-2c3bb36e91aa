package com.teyuntong.goods.search.service.biz.behavior.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户订阅货源表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-14
 */
@Getter
@Setter
@TableName("tyt_transport_subscribe")
public class TransportSubscribeDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 出发地省
     */
    private String startProvince;

    /**
     * 出发地市
     */
    private String startCity;

    /**
     * 出发地区
     */
    private String startArea;

    /**
     * 出发地省市区
     */
    private String startPoint;

    /**
     * 目的地省
     */
    private String destProvince;

    /**
     * 目的地市
     */
    private String destCity;

    /**
     * 目的地区
     */
    private String destArea;

    /**
     * 目的地省市区
     */
    private String destPoint;

    /**
     * 出发地坐标x
     */
    private Integer startCoordX;

    /**
     * 出发地坐标y
     */
    private Integer startCoordY;

    /**
     * 目的地坐标x
     */
    private Integer destCoordX;

    /**
     * 目的地坐标y
     */
    private Integer destCoordY;

    /**
     * 预计装货时间
     */
    private Date loadingDate;

    /**
     * 吨重-下限
     */
    private BigDecimal weightMin;

    /**
     * 吨重-上限
     */
    private BigDecimal weightMax;

    /**
     * 状态：1正常 0无效
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;
}
