package com.teyuntong.goods.search.service.biz.goods.service;

import com.teyuntong.goods.search.client.transport.enums.BenefitLabelEnum;
import com.teyuntong.goods.search.client.transport.vo.BenefitLabelVO;
import com.teyuntong.goods.search.service.biz.goods.dto.BaseTransportSearchDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.SearchHallCountDTO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportLabelJsonVO;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.goods.search.service.common.enums.EsOrDbEnum;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/07/15 17:14
 */
public interface TransportService {

    /**
     * 对非会员车主隐藏货物敏感信息
     *
     * @param transportList 货源
     * @param userId        车主id
     */
    void hideSensitiveInfo(List<? extends TransportVO> transportList, Long userId);

    /**
     * 对非会员车主隐藏货物敏感信息(是否需要加密昵称由传参控制)
     *
     * @param transportList 货源
     * @param userId        车主id
     */
    void hideSensitiveInfoIsShowTrueNickName(List<? extends TransportVO> transportList, Long userId, boolean isNeedEncrypt);

    /**
     * 对货源进行打标，如：给专票货源打标；给专车货源打标
     *
     * @param transportList 货源
     */
    void handleTransportTag(List<? extends TransportVO> transportList, Long userId);

    /**
     * 查询大厅货源
     *
     * @param searchDTO
     * @return
     */
    List<TransportDO> searchHallList(BaseTransportSearchDTO searchDTO);

    /**
     * 根据货源id查询大厅货源
     *
     * @param srcMsgIdList
     * @return
     */
    List<TransportDO> selectBySrcMsgIdsAndStatus(List<Long> srcMsgIdList);

    /**
     * 根据货源id查询大厅货源
     *
     * @param srcMsgId
     * @return
     */
    TransportDO getBySrcMsgIdAndStatus(Long srcMsgId);

    /**
     * 根据货源tsId查询有效的货源
     *
     * @param tsIdList
     * @return
     */
    List<TransportDO> selectValidByTsIds(List<Long> tsIdList);

    /**
     * 根据货源tsId查询货源
     *
     * @param tsIdList
     * @return
     */
    List<TransportDO> selectByTsIds(List<Long> tsIdList);

    /**
     * 根据tsId集合查询货源srcMsgId集合
     * @param tsIdList
     * @return
     */
    List<TransportDO> selectSrcMsgIdByTsIds(List<Long> tsIdList);

    /**
     * 过滤有效的货源id
     */
    List<Long> filterValidTransport(List<Long> srcMsgIds);

    /**
     * 过滤测试账号数据
     *
     * @param transportVOList
     * @param userId
     */
    List<TransportVO> filterTest(List<TransportVO> transportVOList, Long userId);

    /**
     * 屏蔽发货人逻辑，并截取相应页数
     *
     * @param shieldingUserList 当前用户屏蔽的货主id
     */
    List<TransportVO> filterPublishUser(List<Long> shieldingUserList, List<Long> shieldSrcMsgIds,
                                        List<TransportVO> transportDOList, Integer pageSize);

    /**
     * 根据tsOrderNo和userId查询货源
     *
     * @param tsOrderNo
     * @return
     */
    TransportDO getByTsOrderNoAndStatus(String tsOrderNo);

    /**
     * 处理相似货源是否展示逻辑
     *
     * @param transportVOList
     * @param userId
     */
    void handleSimilarTransport(List<TransportVO> transportVOList, Long userId);

    /**
     * 找货大厅时间排序数量
     *
     * @param searchHallCountDTO
     * @return
     */
    Long searchHallCount(SearchHallCountDTO searchHallCountDTO);

    /**
     * 处理固定货源位
     * @param searchDTO
     * @param transportVOList
     * @param userId
     */
    void handleFixedTransport(BaseTransportSearchDTO searchDTO, List<TransportVO> transportVOList, Long userId);

    /**
     * 找货大厅时间排序气泡接口查一条货源出来
     *
     * @param searchDTO
     * @return
     */
    List<TransportDO> searchHallCountDetail(SearchHallCountDTO searchDTO);

    /**
     * 查询最新价格
     *
     * @param srcMsgIdList
     * @return {"srcMsgId":"price"}
     */
    Map<Long, String> getLatestPrice(List<Long> srcMsgIdList);

    /**
     * 根据userId判断是走es还是db
     * @param userId
     * @return
     */
    EsOrDbEnum getEsDbEnum(Long userId);

    /**
     * 处理利益点标签
     * @param transportList
     */
    void dealBenefitLabel(List<? extends TransportVO> transportList);

    /**
     * 获取利益点标签
     * @param labelJsonVO
     * @param transportVO
     * @return
     */
    BenefitLabelVO getBenefitLabel(TransportVO transportVO, Integer showSwitch);
}
