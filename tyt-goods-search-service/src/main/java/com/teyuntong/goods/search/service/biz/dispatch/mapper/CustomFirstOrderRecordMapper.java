package com.teyuntong.goods.search.service.biz.dispatch.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.dispatch.entity.CustomFirstOrderRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Mapper
@DS("tyt")
public interface CustomFirstOrderRecordMapper extends BaseMapper<CustomFirstOrderRecordDO> {

    int countFinishOrder(@Param("cellPhone") String cellPhone);
}
