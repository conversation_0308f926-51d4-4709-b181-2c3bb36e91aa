package com.teyuntong.goods.search.service.biz.route.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 常跑路线地址表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Getter
@Setter
@TableName("often_drive_route_address")
public class OftenDriveRouteAddressDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 路线id
     */
    private Long routeId;

    /**
     * 省
     */
    private String provinc;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 1出发地；2目的地
     */
    private Integer type;

    /**
     * 路线地址拼接字段
     */
    private String addressText;
}
