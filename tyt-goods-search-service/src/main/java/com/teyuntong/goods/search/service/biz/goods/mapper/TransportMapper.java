package com.teyuntong.goods.search.service.biz.goods.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.goods.dto.BaseTransportSearchDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.SearchHallCountDTO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.similarity.dto.SimilarityQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运输信息表主表 mapper
 *
 * <AUTHOR>
 * @since 2024/07/03 15:34
 */
@Mapper
@DS("good_search")
public interface TransportMapper extends BaseMapper<TransportDO> {

    /**
     * 获取相似货源列表
     */
    List<TransportDO> getSimilarityList(SimilarityQueryDTO dto);

    /**
     * 获取急走专区货源列表
     */
    List<TransportDO> getExposureList(BaseTransportSearchDTO searchDTO);

    /**
     * 获取意向货源列表
     */
    List<TransportDO> getIntentionList(BaseTransportSearchDTO searchDTO);

    /**
     * 获取大厅货源列表
     *
     * @param searchDTO
     * @return
     */
    List<TransportDO> searchHallList(BaseTransportSearchDTO searchDTO);

    /**
     * 根据货源id集合查询货源列表
     *
     * @param srcMsgIdList
     * @return
     */
    List<TransportDO> selectBySrcMsgIds(@Param("srcMsgIdList") List<Long> srcMsgIdList);

    /**
     * 根据货源tsId集合查询有效货源列表
     *
     * @param tsIdList
     * @return
     */
    List<TransportDO> selectValidByTsIds(@Param("tsIdList") List<Long> tsIdList);

    /**
     * 根据货源tsId集合查询货源列表
     *
     * @param tsIdList
     * @return
     */
    List<TransportDO> selectByTsIds(@Param("tsIdList") List<Long> tsIdList);

    /**
     * 根据tsId集合查询货源srcMsgId集合
     * @param tsIdList
     * @return
     */
    List<TransportDO> selectSrcMsgIdByTsIds(@Param("tsIdList") List<Long> tsIdList);

    /**
     * 获取急走专区货源列表中最高分值
     *
     * @param searchDTO
     * @return
     */
    TransportDO getExposureOfScore(BaseTransportSearchDTO searchDTO);

    /**
     * 根据tsOrderNo和userId查询货源
     *
     * @param tsOrderNo
     * @return
     */
    TransportDO getByTsOrderNoAndStatus(@Param("tsOrderNo") String tsOrderNo);

    /**
     * 根据货源id和状态查询货源
     *
     * @param srcMsgId
     * @return
     */
    TransportDO getBySrcMsgIdAndStatus(@Param("srcMsgId") Long srcMsgId);

    /**
     * 找货大厅时间排序气泡数量
     *
     * @param searchHallCountDTO
     * @return
     */
    Long searchHallCount(SearchHallCountDTO searchHallCountDTO);

    /**
     * 找货大厅时间排序气泡详情
     *
     * @param searchDTO
     * @return
     */
    List<TransportDO> searchHallCountDetail(SearchHallCountDTO searchDTO);

    /**
     * 获取最新价格
     */
    List<TransportDO> getLatestPrice(@Param("srcMsgIds") List<Long> srcMsgIds);
}
