package com.teyuntong.goods.search.service.biz.record.service;


import com.teyuntong.goods.search.service.biz.record.entity.TransportDispatchViewDO;

/**
 * <p>
 * 货源查看、联系统计表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
public interface TransportDispatchViewService {
    /**
     * 根据用户id和货源id查询用户是否有拨打或者是查看
     *
     * @param userId
     * @param srcMsgId
     * @return
     */
    TransportDispatchViewDO getByUserAndSrcMsgId(Long userId, Long srcMsgId);

    /**
     * 根据用户id查询用户拨打的货源
     *
     * @param userId
     * @return
     */
    Integer contactCount(Long userId);

    /**
     * 保存拨打或者查看统计表和明细表
     *
     * @param srcMsgId 货源id
     * @param type     1:查看  2：联系
     */
    void saveViewAndDetail(Long srcMsgId, Integer type);
}
