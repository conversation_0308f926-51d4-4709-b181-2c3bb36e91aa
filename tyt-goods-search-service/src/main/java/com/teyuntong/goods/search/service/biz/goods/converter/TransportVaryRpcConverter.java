package com.teyuntong.goods.search.service.biz.goods.converter;

import com.teyuntong.goods.search.service.biz.goods.entity.TransportVaryDO;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVaryVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/07/15 17:30
 */
@Mapper
public interface TransportVaryRpcConverter {

    TransportVaryRpcConverter INSTANCE = Mappers.getMapper(TransportVaryRpcConverter.class);

    List<TransportVaryVO> convertDOs2VOs(List<TransportVaryDO> transportVaryDOList);

}
