package com.teyuntong.goods.search.service.biz.record.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.teyuntong.goods.search.service.biz.goods.dto.SearchRecordQueryDTO;
import com.teyuntong.goods.search.service.biz.record.entity.AppCallLogDO;
import com.teyuntong.goods.search.service.biz.record.mapper.AppCallLogMapper;
import com.teyuntong.goods.search.service.biz.record.service.AppCallLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 记录APP拨打电话的信息（电话标注）  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AppCallLogServiceImpl extends ServiceImpl<AppCallLogMapper, AppCallLogDO> implements AppCallLogService {

    private final AppCallLogMapper appCallLogMapper;

    /**
     * 获取最近的车主拨打记录
     */
    @Override
    public PageInfo<AppCallLogDO> getRecentCallPage(SearchRecordQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getCurrentPage(), queryDTO.getPageSize());
        List<AppCallLogDO> list = appCallLogMapper.getRecentCallList(queryDTO.getUserId(), queryDTO.getStartDate());
        return new PageInfo<>(list);
    }

    @Override
    public AppCallLogDO getBySrcMsgIdAndUserId(Long userId, Long srcMsgId) {
        return appCallLogMapper.getBySrcMsgIdAndUserId(userId, srcMsgId);
    }

    @Override
    public Integer getRecentCountByUserId(Long userId, Date startTime) {
        return appCallLogMapper.getRecentCountByUserId(userId, startTime);
    }
}
