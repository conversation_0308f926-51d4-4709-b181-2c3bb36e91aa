package com.teyuntong.goods.search.service.rpc.goods.service;


import com.teyuntong.goods.search.service.biz.exposure.vo.ExposureVO;
import com.teyuntong.goods.search.service.biz.goods.dto.*;
import com.teyuntong.goods.search.service.biz.goods.vo.*;
import com.teyuntong.goods.search.service.biz.search.dto.SearchDistanceDTO;
import com.teyuntong.inner.export.service.client.bidata.vo.BiRecommendVo;
import com.teyuntong.inner.export.service.client.common.bean.ResultMsgBean;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/18 19:51
 */
public interface SearchHallRpcService {

    /**
     * 查询前置处理
     *
     * @param searchDTO
     * @param isBanner
     */
    void beforeSearch(BaseTransportSearchDTO searchDTO, boolean isBanner);

    /**
     * 智能排序
     *
     * @param intelligenceSortDTO
     * @return
     * @throws IOException
     */
    IntelligenceSortVO intelligenceSortList(IntelligenceSortDTO intelligenceSortDTO);

    /**
     * 智能排序气泡数量
     *
     * @param intelligenceSortDTO
     * @return
     */
    IntelligenceCountVO intelligenceBubbleCount(IntelligenceSortDTO intelligenceSortDTO);

    /**
     * 找货大厅列表
     *
     * @param searchDTO
     * @return
     */
    List<TransportVO> searchHallList(BaseTransportSearchDTO searchDTO);

    /**
     * 找货大厅新列表
     *
     * @param searchDTO
     * @return
     */
    SearchHallVO searchHallNewList(BaseTransportSearchDTO searchDTO);

    /**
     * 找货大厅推荐列表
     *
     * @param hallRecommendDTO
     * @return
     */
    List<TransportVO> recommendList(HallRecommendDTO hallRecommendDTO);

    /**
     * 找货大厅智能推荐列表
     *
     * @param intelligenceRecommendDTO
     * @return
     */
    List<TransportVO> intelligenceRecommendList(IntelligenceRecommendDTO intelligenceRecommendDTO);

    /**
     * 推荐货源后续处理
     *
     * @param resultMsgBean
     * @param userId
     * @return
     */
    List<TransportVO> afterRecommend(ResultMsgBean<List<BiRecommendVo>> resultMsgBean, Long userId);

    /**
     * 找货大厅定时刷新变动货源
     *
     * @param transportVaryDTO
     * @return
     */
    List<TransportVaryVO> searchHallVary(TransportVaryDTO transportVaryDTO);

    /**
     * 找货大厅短范围倒短
     *
     * @param shortRangeDTO
     * @return
     */
    List<TransportVO> searchShortRange(ShortRangeDTO shortRangeDTO);

    /**
     * 省内倒短
     *
     * @param shortProvinceDTO
     * @return
     */
    List<TransportVO> searchShortProvince(ShortProvinceDTO shortProvinceDTO);

    /**
     * 同路线货源或同目的地货源列表
     *
     * @param sameRouteDestDTO
     * @return
     */
    List<ExposureVO> searchRouteDestList(SameRouteDestDTO sameRouteDestDTO);

    /**
     * 返回找货大厅配置的搜索距离，优先匹配市，再匹配省，最后返回默认值
     */
    SearchDistanceDTO getSearchDistance(SearchDistanceDTO sdDTO);

    /**
     * 找货大厅气泡数量
     *
     * @param searchHallCountDTO
     * @return
     */
    SearchHallCountVO searchHallCount(SearchHallCountDTO searchHallCountDTO);

    /**
     * 多车找货气泡提醒接口
     */
    CarBubbleVO carBubble();
}
