package com.teyuntong.goods.search.service.biz.enterprise.service.impl;

import com.teyuntong.goods.search.service.biz.enterprise.entity.TransportEnterpriseLogDO;
import com.teyuntong.goods.search.service.biz.enterprise.mapper.TransportEnterpriseLogMapper;
import com.teyuntong.goods.search.service.biz.enterprise.service.TransportEnterpriseLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 开票货源企业信息记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportEnterpriseLogServiceImpl implements TransportEnterpriseLogService {

    private final TransportEnterpriseLogMapper transportEnterpriseLogMapper;

    /**
     * 返回专票货源的开票主体ID
     *
     * @return {"srcMsgId":"invoiceSubjectId"}
     */
    @Override
    public Map<Long, Long> getInvoiceSubjectIdMap(List<Long> srcMsgIds) {
        if (CollectionUtils.isEmpty(srcMsgIds)) {
            return Map.of();
        }
        List<TransportEnterpriseLogDO> list = transportEnterpriseLogMapper.getInvoiceSubjectIdBySrcMsgIds(srcMsgIds);
        return list.stream().collect(Collectors.toMap(
                TransportEnterpriseLogDO::getSrcMsgId,
                TransportEnterpriseLogDO::getInvoiceSubjectId));
    }

    @Override
    public TransportEnterpriseLogDO selectBySrcMsgId(Long srcMsgId) {
        return transportEnterpriseLogMapper.selectBySrcMsgId(srcMsgId);
    }
}
