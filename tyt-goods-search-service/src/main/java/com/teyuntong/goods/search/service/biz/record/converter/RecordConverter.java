package com.teyuntong.goods.search.service.biz.record.converter;

import com.teyuntong.goods.search.service.biz.record.dto.AppCallLogDTO;
import com.teyuntong.goods.search.service.biz.record.entity.AppCallLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/10/28 14:12
 */
@Mapper
public interface RecordConverter {
    RecordConverter INSTANCE = Mappers.getMapper(RecordConverter.class);

    AppCallLogDTO toCallLogDTO(AppCallLogDO appCallLogDO);

    AppCallLogDO toCallLogDO(AppCallLogDTO appCallLogDTO);
}
