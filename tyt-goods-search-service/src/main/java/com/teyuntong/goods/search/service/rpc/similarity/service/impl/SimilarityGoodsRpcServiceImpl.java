package com.teyuntong.goods.search.service.rpc.similarity.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.teyuntong.goods.search.client.transport.dto.SimilarityGoodsDTO;
import com.teyuntong.goods.search.client.transport.service.SimilarityGoodsRpcService;
import com.teyuntong.goods.search.service.biz.goods.converter.TransportConverter;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportExtendDO;
import com.teyuntong.goods.search.service.biz.goods.service.TransportExtendService;
import com.teyuntong.goods.search.service.biz.similarity.converter.SimilarityRpcConverter;
import com.teyuntong.goods.search.service.biz.similarity.dto.SimilarityQueryDTO;
import com.teyuntong.goods.search.service.biz.similarity.service.SimilarityTransportService;
import com.teyuntong.goods.search.service.biz.similarity.vo.SimilarityVO;
import com.teyuntong.goods.search.service.common.error.GoodsSearchErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024/7/22 15:36
 */
@RequiredArgsConstructor
@Service
@Slf4j
@RestController
public class SimilarityGoodsRpcServiceImpl implements SimilarityGoodsRpcService {

    private final SimilarityTransportService similarityTransportService;
    private final TransportExtendService transportExtendService;

    @Qualifier("searchThreadPool")
    private final Executor searchThreadPool;

    @Override
    public Boolean similarityGoodsIsTop(Long srcMsgId, String similarityCode) {
        SimilarityQueryDTO queryDTO = new SimilarityQueryDTO();
        queryDTO.setSimilarityCode(similarityCode);
        // 获取当前用户屏蔽的发货人列表
        List<TransportDO> similarityList = similarityTransportService.getSimilarityList(queryDTO);
        List<SimilarityVO> similarityVOList = SimilarityRpcConverter.INSTANCE.convertDOs2VOs(similarityList);
        if (CollUtil.isNotEmpty(similarityVOList)) {
            // 设置扩展表字段
            List<Long> tsIdList = similarityVOList.stream().map(SimilarityVO::getId).toList();
            Map<Long, TransportExtendDO> extendDOMap = transportExtendService.getMapByTsIds(tsIdList);
            similarityVOList.forEach(vo -> TransportConverter.INSTANCE.transportExtend2VO(vo, extendDOMap.get(vo.getId())));
            // 按照相似货源逻辑排序
            similarityTransportService.sortSimilarityList(similarityVOList);
            return similarityVOList.get(0).getSrcMsgId().equals(srcMsgId);
        }
        return false;
    }

    /**
     * 查询货源货源是否首位
     *
     * @param srcMsgId
     * @param similarityCode
     * @return
     */
    @Override
    public Boolean goodsIsTop(Long srcMsgId, String similarityCode) {
        if (Objects.isNull(srcMsgId) || !StringUtils.hasLength(similarityCode)) {
            throw new BusinessException(GoodsSearchErrorCode.ARGUMENTS_IS_NULL);
        }
        SimilarityQueryDTO queryDTO = new SimilarityQueryDTO();
        queryDTO.setSimilarityCode(similarityCode);
        List<TransportDO> similarityList = similarityTransportService.getSimilarityList(queryDTO);
        List<SimilarityVO> similarityVOList = SimilarityRpcConverter.INSTANCE.convertDOs2VOs(similarityList);

        if (CollUtil.isEmpty(similarityVOList)) {
            // 没有相似货源一定位于首位
            return true;
        }
        // 设置扩展表字段
        // List<Long> tsIdList = similarityVOList.stream().map(SimilarityVO::getId).toList();
        // Map<Long, TransportExtendDO> extendDOMap = transportExtendService.getMapByTsIds(tsIdList);
        // similarityVOList.forEach(vo -> TransportConverter.INSTANCE.transportExtend2VO(vo, extendDOMap.get(vo.getId())));
        // 按照相似货源逻辑排序
        similarityTransportService.sortSimilarityList(similarityVOList);
        return similarityVOList.get(0).getSrcMsgId().equals(srcMsgId);
    }

    /**
     * 批量查询货源是否位于首位
     * 查询不到或查询异常时，返回null，注意判空
     *
     * @param dtoList
     * @return
     */
    @Override
    public Map<Long, Optional<Boolean>> batchSelectGoodsIsTop(List<SimilarityGoodsDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Map.of();
        }

        Map<Long, Optional<Boolean>> resultMap = new ConcurrentHashMap<>();
        List<CompletableFuture<Void>> futures = new ArrayList<>(dtoList.size());

        for (SimilarityGoodsDTO dto : dtoList) {
            Long srcMsgId = dto.getSrcMsgId();
            String similarityCode = dto.getSimilarityCode();

            resultMap.putIfAbsent(srcMsgId, Optional.empty());
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    Boolean result = goodsIsTop(srcMsgId, similarityCode);
                    resultMap.put(srcMsgId, Optional.ofNullable(result));
                } catch (Exception e) {
                    log.error("batchSelectGoodsIsTop 查询相似货源首位失败，srcMsgId: {}, similarityCode: {}", srcMsgId, similarityCode, e);
                    resultMap.put(srcMsgId, Optional.empty());
                }
            }, searchThreadPool).orTimeout(3, TimeUnit.SECONDS);

            futures.add(future);
        }

        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .orTimeout(5, TimeUnit.SECONDS)
                    .join();
        } catch (Exception e) {
            log.error("batchSelectGoodsIsTop 批量查询相似货源首位超时或异常", e);
        }

        return resultMap;
    }



}
