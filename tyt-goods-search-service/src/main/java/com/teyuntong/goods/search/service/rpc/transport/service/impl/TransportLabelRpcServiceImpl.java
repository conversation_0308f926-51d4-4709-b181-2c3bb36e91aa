package com.teyuntong.goods.search.service.rpc.transport.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.search.client.transport.enums.BenefitLabelEnum;
import com.teyuntong.goods.search.client.transport.service.TransportLabelRpcService;
import com.teyuntong.goods.search.client.transport.vo.BenefitLabelVO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportMainDO;
import com.teyuntong.goods.search.service.biz.goods.service.TransportMainService;
import com.teyuntong.goods.search.service.biz.goods.service.TransportService;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportLabelJsonVO;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.goods.search.service.remote.basic.ConfigRemoteService;
import com.teyuntong.goods.search.service.remote.basic.constant.TytConfigKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/05/16 17:08
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class TransportLabelRpcServiceImpl implements TransportLabelRpcService {

    private final TransportService transportService;
    private final TransportMainService transportMainService;
    private final ConfigRemoteService configRemoteService;


    @Override
    public BenefitLabelVO getTransportBenefitLabel(Long srcMsgId) {
        int switchOn = configRemoteService.getIntValue(TytConfigKey.BENEFIT_LABEL_SHOW_SWITCH, 0);
        if (switchOn == 0){ //关闭
            return null;
        }
        List<Long> srcMsgIdList = new ArrayList<>();
        srcMsgIdList.add(srcMsgId);
        List<TransportMainDO> transportMainDOList = transportMainService.getBySrcMsgIds(srcMsgIdList);
        if (CollectionUtils.isEmpty(transportMainDOList)){
            return null;
        }
        TransportMainDO transportMainDO = transportMainDOList.get(0);
        TransportVO transportVO = new TransportVO();
        BeanUtil.copyProperties(transportMainDO, transportVO);
        return transportService.getBenefitLabel(transportVO, switchOn);
    }
}
