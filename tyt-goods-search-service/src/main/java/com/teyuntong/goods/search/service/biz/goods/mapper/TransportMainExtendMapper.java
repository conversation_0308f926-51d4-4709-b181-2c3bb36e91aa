package com.teyuntong.goods.search.service.biz.goods.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportMainExtendDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 货源信息扩展表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Mapper
@DS("tyt")
public interface TransportMainExtendMapper extends BaseMapper<TransportMainExtendDO> {

    /**
     * 根据货源信息主键查询
     */
    List<TransportMainExtendDO> getBySrcMsgIds(List<Long> srcMsgIds);
}
