package com.teyuntong.goods.search.service.biz.goods.service.impl;

import com.teyuntong.goods.search.service.biz.goods.entity.TransportMainExtendDO;
import com.teyuntong.goods.search.service.biz.goods.mapper.TransportMainExtendMapper;
import com.teyuntong.goods.search.service.biz.goods.service.TransportMainExtendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/08/13 20:49
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportMainExtendServiceImpl implements TransportMainExtendService {

    private final TransportMainExtendMapper transportMainExtendMapper;

    /**
     * 返回srcMsgId对应的map
     */
    @Override
    public Map<Long, TransportMainExtendDO> getMapBySrcMsgIds(List<Long> srcMsgIds) {
        return getBySrcMsgIds(srcMsgIds).stream()
                .collect(Collectors.toMap(TransportMainExtendDO::getSrcMsgId, t -> t));
    }

    /**
     * 根据srcMsgId查询
     */
    @Override
    public List<TransportMainExtendDO> getBySrcMsgIds(List<Long> srcMsgIds) {
        if (CollectionUtils.isEmpty(srcMsgIds)) {
            return List.of();
        }
        return transportMainExtendMapper.getBySrcMsgIds(srcMsgIds);
    }

    /**
     * 根据srcMsgId查询
     */
    @Override
    public TransportMainExtendDO getBySrcMsgId(Long srcMsgId) {
        if (srcMsgId == null) {
            return null;
        }
        return getBySrcMsgIds(List.of(srcMsgId)).stream().findFirst().orElse(null);
    }
}
