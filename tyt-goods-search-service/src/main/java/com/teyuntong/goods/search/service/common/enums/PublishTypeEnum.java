package com.teyuntong.goods.search.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 货源类型（电议1，一口价2）
 *
 * <AUTHOR>
 * @since 2024/12/02 14:18
 */
@Getter
@AllArgsConstructor
public enum PublishTypeEnum {

    TEL(1, "电议"),
    FIXED(2, "一口价");

    private final Integer code;
    private final String name;

    public static boolean isFixed(Integer code) {
        return Objects.equals(code, FIXED.getCode());
    }
}
