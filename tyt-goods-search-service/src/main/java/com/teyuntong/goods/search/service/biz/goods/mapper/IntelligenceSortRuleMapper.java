package com.teyuntong.goods.search.service.biz.goods.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.goods.entity.IntelligenceSortRuleDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 智能排序计分规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Mapper
@DS("tyt")
public interface IntelligenceSortRuleMapper extends BaseMapper<IntelligenceSortRuleDO> {

    /**
     * 查询所有智能排序计分规则
     * @return
     */
    List<IntelligenceSortRuleDO> selectAll();
}
