package com.teyuntong.goods.search.service.biz.behavior.serivce.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teyuntong.goods.search.service.biz.behavior.entity.ShieldingShipperDO;
import com.teyuntong.goods.search.service.biz.behavior.mapper.ShieldingShipperMapper;
import com.teyuntong.goods.search.service.biz.behavior.serivce.ShieldingShipperService;
import com.teyuntong.goods.search.service.common.constant.ConfigConstant;
import com.teyuntong.goods.search.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.search.service.remote.basic.ConfigRemoteService;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.teyuntong.goods.search.service.common.constant.ConfigConstant.CARGO_OWNER_TEST_ACCOUNT;
import static com.teyuntong.goods.search.service.common.constant.ConfigConstant.CAR_OWNER_TEST_ACCOUNT;

/**
 * 屏蔽发货人 service
 *
 * <AUTHOR>
 * @since 2024/07/12 17:12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShieldingShipperServiceImpl extends ServiceImpl<ShieldingShipperMapper, ShieldingShipperDO> implements ShieldingShipperService {

    private final RedisUtil redisUtil;
    private final ShieldingShipperMapper shieldingShipperMapper;
    @Autowired
    private ConfigRemoteService configRemoteService;

    /**
     * 获取当前用户屏蔽的发货人id
     *
     * @param userId 车主id
     * @return 屏蔽的发货人userIds
     */
    @Override
    public List<Long> getShieldingUserList(Long userId) {
        try {
            Integer shieldingUserSwitch = configRemoteService.getIntValue(ConfigConstant.SHIELDING_USER_SWITCH, 1);
            // 屏蔽发货人开关打开时，才进行屏蔽逻辑
            if (Objects.equals(shieldingUserSwitch, 1)) {
                List<Long> defaultShieldingUserIds = getDefaultShieldingUserIds(userId);
                if (userId == null) {
                    return defaultShieldingUserIds;
                }
                String cacheKey = RedisKeyConstant.SHIELD_SHIPPER_KEY + userId;
                List<Long> shieldingUserIdList = redisUtil.hashValues(cacheKey, Long.class);
                if (CollUtil.isEmpty(shieldingUserIdList)) {
                    shieldingUserIdList = shieldingShipperMapper.getShieldingUserIdList(userId);
                    Map<String, Long> collect;
                    if (CollUtil.isNotEmpty(shieldingUserIdList)) {
                        collect = shieldingUserIdList.stream().collect(Collectors.toMap(Object::toString, v -> v, (k1, k2) -> k1));
                    } else {
                        collect = Map.of("0", 0L);
                    }
                    redisUtil.hashPutAll(cacheKey, collect, Duration.ofSeconds(10));
                }
                defaultShieldingUserIds.addAll(shieldingUserIdList);
                return defaultShieldingUserIds;
            }
        } catch (Exception e) {
            log.error("getShieldingUserList userId:{} error:", userId, e);
        }
        return new ArrayList<>();
    }

    /**
     * 获取需要全局屏蔽的货主账号
     * 配置的不受屏蔽影响的车主账号不屏蔽
     *
     * @param userId
     * @return
     */
    @Override
    public List<Long> getDefaultShieldingUserIds(Long userId) {
        List<Long> list = new ArrayList<>();
        try {
            if (Objects.nonNull(userId)) {
                String carOwnerTestUserIds = configRemoteService.getStringValue(CAR_OWNER_TEST_ACCOUNT);
                if (StringUtils.isNotBlank(carOwnerTestUserIds)) {
                    String[] testCarOwnerUserIds = carOwnerTestUserIds.split(",");
                    for (String carIdStr : testCarOwnerUserIds) {
                        if (carIdStr.equals(userId.toString())) {
                            return list;
                        }
                    }
                }
            }
            String cargoOwnerTestUserIds = configRemoteService.getStringValue(CARGO_OWNER_TEST_ACCOUNT);
            if (StringUtils.isNotBlank(cargoOwnerTestUserIds)) {
                String[] testCargoOwnerUserIds = cargoOwnerTestUserIds.split(",");
                for (String userIdStr : testCargoOwnerUserIds) {
                    list.add(Long.parseLong(userIdStr));
                }
            }
        } catch (Exception e) {
            log.error("getDefaultShieldingUserIds userId:{} error:", userId, e);
        }
        log.info("getDefaultShieldingUserIds userId:{}, result:{}", userId, JSONObject.toJSON(list));
        return list;
    }

    /**
     * 从数据库中获取当前用户屏蔽的发货人id
     *
     * @param userId 车主id
     * @return 屏蔽的发货人userIds
     */
    @Override
    public List<Long> getShieldingUserFromDB(Long userId) {
        return shieldingShipperMapper.getShieldingUserIdList(userId);
    }

    /**
     * 删除数据
     *
     * @param userId
     * @param shieldingUserId
     */
    @Override
    public void delete(Long userId, Long shieldingUserId) {
        shieldingShipperMapper.deleteData(userId, shieldingUserId);
    }
}
