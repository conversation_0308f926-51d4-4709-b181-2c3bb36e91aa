package com.teyuntong.goods.search.service.biz.goods.dto;

import cn.hutool.core.date.DateUtil;
import com.teyuntong.goods.search.service.common.error.GoodsSearchErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.teyuntong.goods.search.service.common.constant.SearchParamConstant.*;

/**
 * 货源搜索条件类
 */
@Getter
@Setter
@Slf4j
public class BaseTransportSearchDTO {

    /**
     * 查询类型
     * 0、自动刷新加载新数据
     * 1、默认查询，或者下拉时重新加载
     * 2、上滑，往前翻请求历史数据
     */
//    @NotNull
//    @Range(min = 0, max = 2)
    private Integer queryType;

    /**
     * 查询标记点，找货大厅、急走专区列表需要
     * queryType=0，查询大于querySign，为列表最大id
     * queryType=1，该字段无效
     * queryType=2，查询小于querySign，为列表最小id
     * 6620版本后，时间排序该字段弃用，已最大，最小id为准
     */
    private Long querySign;
    /**
     * 出发地坐标
     */
    private String startCoord;
    /**
     * 目的地坐标
     */
    private String destCoord;
    /**
     * 出发地范围
     */
    private String startDistance;
    /**
     * 目的地范围
     */
    private String destDistance;
    /**
     * 重量最小值
     */
    private Integer startWeight;
    /**
     * 重量最大值
     */
    private Integer endWeight;
    /**
     * 货物名称，以英文逗号隔开
     */
    private String goodsName;
    /**
     * 货物尺寸长度最小值
     */
    private BigDecimal minLength;
    /**
     * 货物尺寸长度最大值
     */
    private BigDecimal maxLength;
    /**
     * 货物尺寸宽度最大值
     */
    private BigDecimal minWidth;
    /**
     * 货物尺寸宽度最大值
     */
    private BigDecimal maxWidth;
    /**
     * 货物尺寸高度最大值
     */
    private BigDecimal minHeight;
    /**
     * 货物尺寸高度最大值
     */
    private BigDecimal maxHeight;
    /**
     * 开始装车时间
     */
    private Long startLoadingTime;
    /**
     * 截止装车时间
     */
    private Long endLoadingTime;
    /**
     * 最小运价
     */
    private Integer minPrice;
    /**
     * 最大运价
     */
    private Integer maxPrice;

    /**
     * 出发地范围
     */
    private Long startRange;
    /**
     * 目的地范围
     */
    private Long destRange;

    /**
     * 出发地省
     */
    private String startProvinc;
    /**
     * 出发地市
     */
    private String startCity;
    /**
     * 出发地区
     */
    private String startArea;
    /**
     * 目的地省
     */
    private String destProvinc;
    /**
     * 目的地市
     */
    private String destCity;
    /**
     * 目的地区
     */
    private String destArea;

    /**
     * 货源是否有价格（空不限；0无价；1有价）
     */
    private Integer priceFlag;

    /**
     * 货源类型（电议1，一口价2）
     */
    private Integer publishType;
    /**
     * 订金是否退还（0不退还；1退还）
     */
    private Integer refundFlag;
    /**
     * 货源类型：0普通货源；1优车货源；2专车货源
     */
    private Integer excellentGoods;

    /**
     * 每页条数
     */
    private Integer pageSize = 30;

    /**
     * 车id
     */
    private Long carId;

    /**
     * 出发地经纬度，格式：112.539558,33.003784
     */
    private String startLonLat;

    /**
     * 目的地经纬度，格式：116.638329,36.933811
     */
    private String destLonLat;

    // ==============================================

    /**
     * 最大货源id，急走专区轮播接口,时间排序气泡接口需要
     */
    private Long maxTsId;

    /**
     * 最小货源id，急走专区轮播接口需要
     */
    private Long minTsId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 需要过滤的货主id
     */
    private List<Long> excludeUserIds;

    /**
     * 需要过滤的货源id
     */
    private List<Long> excludeSrcMsgIds;

    /**
     * 需要查询的货源id，相当于是看这些货源是不是符合这些查询条件
     */
    private List<Long> useSrcMsgIds;


    /**
     * SQL查询需要
     */
    private Long startCoordX;
    /**
     * SQL查询需要
     */
    private Long startCoordY;
    /**
     * SQL查询需要
     */
    private Long destCoordX;
    /**
     * SQL查询需要
     */
    private Long destCoordY;

    /**
     * 是否查询存 loadingTime 字段为空的数据
     * 此字段为了处理《今明两日，随到随装》问题
     */
    private Boolean queryNullLoadingTime;

    /**
     * 排序类型1时间，2距离，3智能
     * 仅记录日志使用，与业务无关
     */
    private String sortType;

    /**
     * 秒抢货源：1是0否
     */
    private Integer seckillGoods;

    /**
     * 用车类型：1-整车，2-拼车
     */
    private Integer useCarType;

    /**
     * 好货推荐  1是0否
     */
    private Integer goodTransportRecommend;

    /**
     * 找货大厅额外参数
     */
    @Setter
    private SearchHallExtra searchHallExtra;

    public SearchHallExtra getSearchHallExtra() {
        if (searchHallExtra == null) {
            searchHallExtra = SearchHallExtra.builder().build();
        }
        return searchHallExtra;
    }


    /**
     * 查询数据库前预处理
     */
    public void preHandle() {
        dealCoordAndRange();
        unitConversion();
        handleLoadingTime();
        dealAddress();
    }

    private void dealAddress() {

        if (StringUtils.isNotBlank(startArea) && StringUtils.isNotBlank(startCity) && startArea.equals(startCity)) {
            startArea = null;
        }
        if (StringUtils.isNotBlank(destArea) && StringUtils.isNotBlank(destCity) && destArea.equals(destCity)) {
            destArea = null;
        }
    }


    /**
     * 处理《今明两日，随到随装》
     */
    private void handleLoadingTime() {
        Date today = DateUtil.beginOfDay(new Date());
        Long afterTomorrow = DateUtils.addDays(today, 2).getTime();


        // 强制设置装货时间起始日期为3天前，防止参数含有 null
        if (startLoadingTime == null) {
            startLoadingTime = DateUtils.addDays(today, -3).getTime();
        }

        // 强制设置装货时间结束日期为2个月后，防止参数含有 null
        if (endLoadingTime == null) {
            endLoadingTime = DateUtils.addMonths(today, 2).getTime();
        }
        // startLoadingTime 到 endLoadingTime 和 today 到 afterTomorrow 如果存在交集，说明查询条件满足《今明两日，随到随装》
        queryNullLoadingTime = startLoadingTime < afterTomorrow && endLoadingTime > today.getTime();
    }


    /**
     * Sql 处理坐标和范围
     */
    private void dealCoordAndRange() {
        if (StringUtils.isNotBlank(startCoord)) {
            // 处理范围
            startRange = DEFAULT_DISTANCE_RANGE;
            if (StringUtils.isNumeric(startDistance)) {
                long sd = Long.parseLong(startDistance);
                if (MIN_DISTANCE < sd && sd <= MAX_DISTANCE) {
                    startRange = sd * RANG_SIZE;
                }
            }

            // 处理坐标
            String[] startCoordArray = startCoord.split(",");
            if (startCoordArray.length < 2 || !NumberUtils.isCreatable(startCoordArray[0]) || !NumberUtils.isCreatable(startCoordArray[1])) {
                log.error("出发地坐标格式错误，非x,y形式！");
                throw new BusinessException(GoodsSearchErrorCode.ILLEGAL_COORD);
            }
            startCoordX = new BigDecimal(startCoordArray[0]).multiply(new BigDecimal(RANG_SIZE)).longValue();
            startCoordY = new BigDecimal(startCoordArray[1]).multiply(new BigDecimal(RANG_SIZE)).longValue();
        }
        if (StringUtils.isNotBlank(destCoord)) {
            // 处理范围
            if (StringUtils.isNumeric(destDistance)) {
                destRange = Long.parseLong(destDistance) * 100;
            } else {
                destRange = DEFAULT_DISTANCE_RANGE;
            }

            // 处理坐标
            String[] destCoordArray = destCoord.split(",");
            if (destCoordArray.length < 2 || !NumberUtils.isCreatable(destCoordArray[0]) || !NumberUtils.isCreatable(destCoordArray[1])) {
                log.error("目的地坐标格式错误，非x,y形式！");
                throw new BusinessException(GoodsSearchErrorCode.ILLEGAL_COORD);
            }
            destCoordX = new BigDecimal(destCoordArray[0]).multiply(new BigDecimal(RANG_SIZE)).longValue();
            destCoordY = new BigDecimal(destCoordArray[1]).multiply(new BigDecimal(RANG_SIZE)).longValue();
        }
    }

    /**
     * 单位换算
     */
    private void unitConversion() {
        if (startWeight != null) {
            startWeight = startWeight * 100;
        }
        if (endWeight != null) {
            endWeight = endWeight * 100;
        }
        if (minLength != null) {
            minLength = minLength.movePointRight(2);
        }
        if (maxLength != null) {
            maxLength = maxLength.movePointRight(2);
        }
        if (minWidth != null) {
            minWidth = minWidth.movePointRight(2);
        }
        if (maxWidth != null) {
            maxWidth = maxWidth.movePointRight(2);
        }
        if (minHeight != null) {
            minHeight = minHeight.movePointRight(2);
        }
        if (maxHeight != null) {
            maxHeight = maxHeight.movePointRight(2);
        }
    }
}
