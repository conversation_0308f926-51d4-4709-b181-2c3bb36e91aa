package com.teyuntong.goods.search.service.common.property;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/11/05 14:23
 */
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "rocket-mq.topic")
public class RocketMqTopicProperty {

    /**
     * 常跑路线topic
     */
    private String oftenRoute;
}
