package com.teyuntong.goods.search.service.remote.bidata;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.inner.export.service.client.bidata.service.BiDataRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/10/14 15:43
 */
@FeignClient(name = "tyt-inner-export-service", path = "inner-export", contextId = "biDataRpcService",
        fallbackFactory = BiDataRemoteService.BiDataRemoteServiceFallback.class)
public interface BiDataRemoteService extends BiDataRpcService {

    @Component
    class BiDataRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<BiDataRemoteService> {
        public BiDataRemoteServiceFallback() {
            super(true, BiDataRemoteService.class);
        }
    }
}
