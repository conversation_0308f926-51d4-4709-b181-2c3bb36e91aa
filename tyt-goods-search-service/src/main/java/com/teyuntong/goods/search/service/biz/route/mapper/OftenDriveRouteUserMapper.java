package com.teyuntong.goods.search.service.biz.route.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.route.entity.OftenDriveRouteUserDO;
import com.teyuntong.goods.search.service.biz.route.vo.UserOftenRouteVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 常跑路线主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Mapper
@DS("good_search")
public interface OftenDriveRouteUserMapper extends BaseMapper<OftenDriveRouteUserDO> {

    /**
     * 获取用户所有路线
     */
    List<UserOftenRouteVo> getRouteList(@Param("userId") Long userId);

    /**
     * 根据用户id和路线id获取路线
     */
    OftenDriveRouteUserDO getByUserIdAndRouteId(@Param("userId") Long userId, @Param("routeId") Long routeId);

    /**
     * 根据用户id获取路线数量
     */
    int countByUserId(@Param("userId") Long userId, @Param("reportStatus") Integer reportStatus);

    /**
     * 获取用户启用的路线id
     */
    List<Long> getRouteIdOfEnable(@Param("userId") Long userId);
}
