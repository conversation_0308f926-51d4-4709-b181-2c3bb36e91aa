package com.teyuntong.goods.search.service.remote.order;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.trade.service.client.orders.service.OrdersRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * trade 订单接口
 *
 * <AUTHOR>
 * @since 2024/08/13 10:50
 */
@FeignClient(name = "tyt-trade-service", path = "trade", contextId = "orderRemoteService",
        fallbackFactory = OrderRemoteService.OrderRemoteServiceFallback.class)
public interface OrderRemoteService extends OrdersRpcService {
    @Component
    class OrderRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<OrderRemoteService> {
        public OrderRemoteServiceFallback() {
            super(true, OrderRemoteService.class);
        }
    }
}
