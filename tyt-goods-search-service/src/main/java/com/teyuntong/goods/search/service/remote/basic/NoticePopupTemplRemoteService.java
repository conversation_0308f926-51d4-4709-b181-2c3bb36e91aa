package com.teyuntong.goods.search.service.remote.basic;

import com.teyuntong.infra.basic.resource.client.popup.service.NoticePopupTemplRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 调用开关配置
 *
 * <AUTHOR>
 * @since 2024/11/05 11:31
 */
@FeignClient(name = "tyt-infra-basic-resource", path = "basic-resource", contextId = "NoticePopupTemplRemoteService",
        fallbackFactory = NoticePopupTemplRemoteService.NoticePopupTemplRemoteServiceFallback.class)
public interface NoticePopupTemplRemoteService extends NoticePopupTemplRpcService {

    @Component
    class NoticePopupTemplRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<NoticePopupTemplRemoteService> {
        public NoticePopupTemplRemoteServiceFallback() {
            super(true, NoticePopupTemplRemoteService.class);
        }
    }
}
