package com.teyuntong.goods.search.service.biz.group.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.group.entity.DwsCarLabelDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 车辆标签表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-28
 */
@Mapper
@DS("recommend")
public interface DwsCarLabelMapper extends BaseMapper<DwsCarLabelDO> {

    /**
     * 根据用户ID查询车主用户标识和距离习惯
     *
     * @param userId 用户ID
     * @return 车辆标签信息
     */
    DwsCarLabelDO selectCarLabelByUserId(@Param("userId") Long userId);
}
