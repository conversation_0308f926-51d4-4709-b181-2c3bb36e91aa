package com.teyuntong.goods.search.service.biz.goods.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportHistoryDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * transport表历史记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Mapper
@DS("tyt")
public interface TransportHistoryMapper extends BaseMapper<TransportHistoryDO> {

    /**
     * 根据货源id和状态查询货源,获取最原始的货物信息
     *
     * @param srcMsgId
     * @return
     */
    TransportHistoryDO getBySrcMsgIdAndOriginal(@Param("srcMsgId") Long srcMsgId);

    /**
     * 批量货源每个货源的最早一条记录的价格
     *
     * @param srcMsgIds
     * @return
     */
    List<TransportHistoryDO> getOriginalPrice(@Param("srcMsgIds") List<Long> srcMsgIds);


}
