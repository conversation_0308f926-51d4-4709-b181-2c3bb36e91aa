package com.teyuntong.goods.search.service.biz.tytlog.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.teyuntong.goods.search.service.biz.goods.dto.BaseTransportSearchDTO;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.goods.search.service.biz.tytlog.entity.LogTsSearchDO;
import com.teyuntong.goods.search.service.biz.tytlog.mapper.LogTsSearchMapper;
import com.teyuntong.goods.search.service.biz.tytlog.service.SearchLogRecordService;
import com.teyuntong.goods.search.service.common.enums.QueryTypeEnum;
import com.teyuntong.goods.search.service.common.enums.YesNoEnum;
import com.teyuntong.goods.search.service.remote.basic.ConfigRemoteService;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.teyuntong.goods.search.service.common.constant.ConfigConstant.SEARCH_LOG_KEY;
import static jodd.util.StringPool.COMMA;
import static jodd.util.StringPool.UNDERSCORE;

/**
 * <p>
 * 时间找货查询日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SearchLogRecordServiceImpl implements SearchLogRecordService {


    private final LogTsSearchMapper logTsSearchMapper;
    private final ConfigRemoteService configRemoteService;

    @Async("searchThreadPool")
    @Override
    public void saveTsSearchLog(BaseTransportSearchDTO baseTransportSearchDTO, BaseParamDTO baseParam, List<TransportVO> transportVOList) {
        // 开关开启且是下拉刷新的情况下情况下才记录搜索日志,
        try {
            if (QueryTypeEnum.AUTO.getCode().equals(baseTransportSearchDTO.getQueryType())
                    || baseTransportSearchDTO.getUserId() == null) {
                return;
            }

            String searchLogKey = configRemoteService.getStringValue(SEARCH_LOG_KEY, "0");
            if (YesNoEnum.N.getCode().toString().equals(searchLogKey)) {
                return;
            }

            LogTsSearchDO logTsSearchDO = new LogTsSearchDO();
            BeanUtil.copyProperties(baseTransportSearchDTO, logTsSearchDO, "startLoadingTime", "endLoadingTime");
            BeanUtil.copyProperties(baseParam, logTsSearchDO);
            if (baseTransportSearchDTO.getStartLoadingTime() != null) {
                logTsSearchDO.setStartLoadingTime(new Date(baseTransportSearchDTO.getStartLoadingTime()));
            }
            if (baseTransportSearchDTO.getEndLoadingTime() != null) {
                logTsSearchDO.setEndLoadingTime(new Date(baseTransportSearchDTO.getEndLoadingTime()));
            }

            // 调整日志记录的坐标格式
            String startCoord = baseTransportSearchDTO.getStartCoord();
            String destCoord = baseTransportSearchDTO.getDestCoord();
            String startDistance = baseTransportSearchDTO.getStartDistance();
            String destDistance = baseTransportSearchDTO.getDestDistance();
            if (StringUtils.isNotBlank(startCoord)) {
                startCoord = startCoord.replaceAll(COMMA, UNDERSCORE);
                logTsSearchDO.setStartCoord(startCoord);
            }
            if (StringUtils.isNotBlank(destCoord)) {
                destCoord = destCoord.replaceAll(COMMA, UNDERSCORE);
                logTsSearchDO.setDestCoord(destCoord);
            }
            if (StringUtils.isNotBlank(startDistance)) {
                logTsSearchDO.setStartRange(Long.parseLong(startDistance));
            }
            if (StringUtils.isNotBlank(destDistance)) {
                logTsSearchDO.setDestRange(Long.parseLong(destDistance));
            }

            logTsSearchDO.setCtime(new Date());

            if (CollUtil.isNotEmpty(transportVOList)) {
                List<Long> srcMsgIdList = transportVOList.stream().map(TransportVO::getSrcMsgId).toList();
                logTsSearchDO.setSrcMsgIds(JSONUtil.toJsonStr(srcMsgIdList));
            }
            logTsSearchMapper.insert(logTsSearchDO);

        } catch (Exception e) {
            log.error("保存时间找货日志异常", e);
        }

    }
}
