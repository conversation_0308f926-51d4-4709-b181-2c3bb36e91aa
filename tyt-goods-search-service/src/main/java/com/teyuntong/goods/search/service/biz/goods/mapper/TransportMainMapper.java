package com.teyuntong.goods.search.service.biz.goods.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportMainDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 运输信息表主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Mapper
@DS("tyt")
public interface TransportMainMapper extends BaseMapper<TransportMainDO> {

    /**
     * 根据货源id批量查询
     */
    List<TransportMainDO> getBySrcMsgIds(@Param("srcMsgIds") List<Long> srcMsgIds);

    /**
     * 过滤有效的货源id
     */
    List<Long> filterValidTransport(@Param("srcMsgIds") List<Long> srcMsgIds);

    List<Long> getFreeCommissionTransportBySrcMsgIdList(@Param("srcMsgIds") List<Long> srcMsgIds);

}
