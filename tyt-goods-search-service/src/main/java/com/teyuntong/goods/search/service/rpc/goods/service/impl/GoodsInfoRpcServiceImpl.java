package com.teyuntong.goods.search.service.rpc.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.search.service.biz.dispatch.entity.SpecialCarDispatchFailureDO;
import com.teyuntong.goods.search.service.biz.dispatch.service.SpecialCarDispatchFailureService;
import com.teyuntong.goods.search.service.biz.enterprise.entity.TransportEnterpriseLogDO;
import com.teyuntong.goods.search.service.biz.enterprise.service.TransportEnterpriseLogService;
import com.teyuntong.goods.search.service.biz.goods.converter.TransportConverter;
import com.teyuntong.goods.search.service.biz.goods.dto.GoodsInfoRecommendDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.GoodsPhoneDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.GoodsStatusDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.TransportLabelJsonDTO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportMainDO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportMainExtendDO;
import com.teyuntong.goods.search.service.biz.goods.service.TransportMainExtendService;
import com.teyuntong.goods.search.service.biz.goods.service.TransportMainService;
import com.teyuntong.goods.search.service.biz.goods.service.TransportService;
import com.teyuntong.goods.search.service.biz.goods.vo.GoodsPhoneVO;
import com.teyuntong.goods.search.service.biz.goods.vo.GoodsStatusVO;
import com.teyuntong.goods.search.service.biz.goods.vo.TecServiceFeeVO;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.goods.search.service.biz.record.entity.AppCallLogDO;
import com.teyuntong.goods.search.service.biz.record.entity.UserCallPhoneRecordDO;
import com.teyuntong.goods.search.service.biz.record.service.*;
import com.teyuntong.goods.search.service.common.enums.*;
import com.teyuntong.goods.search.service.common.error.GoodsSearchErrorCode;
import com.teyuntong.goods.search.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.search.service.remote.basic.ConfigRemoteService;
import com.teyuntong.goods.search.service.remote.basic.NoticePopupTemplRemoteService;
import com.teyuntong.goods.search.service.remote.bidata.BiDataRemoteService;
import com.teyuntong.goods.search.service.remote.goods.AxbRemoteService;
import com.teyuntong.goods.search.service.remote.inner.PlatTransportRemoteService;
import com.teyuntong.goods.search.service.remote.order.InfoFeeRemoteService;
import com.teyuntong.goods.search.service.remote.order.OrderRemoteService;
import com.teyuntong.goods.search.service.remote.user.*;
import com.teyuntong.goods.search.service.rpc.goods.service.GoodsInfoRpcService;
import com.teyuntong.goods.search.service.rpc.goods.service.SearchHallRpcService;
import com.teyuntong.goods.service.client.callphonerecord.vo.PrivacyPhoneTabInfo;
import com.teyuntong.infra.basic.resource.client.popup.dto.PopupTypeEnum;
import com.teyuntong.infra.basic.resource.client.popup.vo.NoticePopupTemplVo;
import com.teyuntong.infra.basic.resource.client.tytabtest.dto.ABTestDto;
import com.teyuntong.infra.basic.resource.client.tytabtest.vo.ABTestVo;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.error.CommonErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.inner.export.service.client.bidata.dto.BiGoodsInfoRecommendReq;
import com.teyuntong.inner.export.service.client.bidata.vo.BiRecommendVo;
import com.teyuntong.inner.export.service.client.common.bean.ResultMsgBean;
import com.teyuntong.trade.service.client.infofee.vo.TransportOrdersListVO;
import com.teyuntong.trade.service.client.orders.dto.CheckSeckillGoodsPayLimitDTO;
import com.teyuntong.trade.service.client.orders.dto.TransportOrdersRpcDTO;
import com.teyuntong.trade.service.client.orders.enums.OrderPayStatusEnum;
import com.teyuntong.trade.service.client.orders.service.TransportOrderSnapshotRpcService;
import com.teyuntong.trade.service.client.orders.vo.AcceptOrderLimitInfo;
import com.teyuntong.trade.service.client.orders.vo.TransportOrdersVO;
import com.teyuntong.user.service.client.car.vo.CarInterceptVO;
import com.teyuntong.user.service.client.car.vo.CheckCarDriverVO;
import com.teyuntong.user.service.client.car.vo.SuperiorCarSignVO;
import com.teyuntong.user.service.client.car.vo.TytSigningCarVO;
import com.teyuntong.user.service.client.limit.vo.UserLimitInfoRpcVO;
import com.teyuntong.user.service.client.limit.vo.UserOrdersLimitInfoRpcVO;
import com.teyuntong.user.service.client.permission.dto.AuthPermissionRpcDTO;
import com.teyuntong.user.service.client.permission.dto.UserPermissionRpcDTO;
import com.teyuntong.user.service.client.permission.enums.ServicePermissionEnum;
import com.teyuntong.user.service.client.permission.enums.UserPermissionTypeEnum;
import com.teyuntong.user.service.client.permission.vo.AuthPermissionRpcVO;
import com.teyuntong.user.service.client.permission.vo.UserPermissionRpcVO;
import com.teyuntong.user.service.client.user.enums.UserActivateFlagEnum;
import com.teyuntong.user.service.client.user.vo.ActivateCheckVO;
import com.teyuntong.user.service.client.user.vo.UserIdentityAuthVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

import static cn.hutool.core.date.DatePattern.CHINESE_DATE_PATTERN;
import static com.teyuntong.goods.search.service.common.constant.ConfigConstant.*;
import static com.teyuntong.goods.search.service.remote.basic.constant.ABTestCode.SEC_KILL_GOODS_LIMIT;

/**
 * <AUTHOR>
 * @since 2024/11/15 18:33
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class GoodsInfoRpcServiceImpl implements GoodsInfoRpcService {

    private final BiDataRemoteService biDataRemoteService;
    private final SearchHallRpcService searchHallRpcService;
    private final UserLimitRemoteService userLimitRemoteService;
    private final NoticePopupTemplRemoteService noticePopupTemplRemoteService;
    private final OrderRemoteService orderRemoteService;
    private final UserRemoteService userRemoteService;
    private final ConfigRemoteService configRemoteService;
    private final TransportService transportService;
    private final TransportMainService transportMainService;
    private final SpecialCarDispatchFailureService specialCarDispatchFailureService;
    private final SigningCarRemoteService signingCarRemoteService;
    private final SuperiorCarSignRemoteService superiorCarSignRemoteService;
    private final DriverRemoteService driverRemoteService;
    private final PlatTransportRemoteService platTransportRemoteService;
    private final UserCallPhoneRecordService userCallPhoneRecordService;
    private final TransportEnterpriseLogService transportEnterpriseLogService;
    private final CarRemoteService carRemoteService;
    private final InfoFeeRemoteService infoFeeRemoteService;
    private final AppCallLogService appCallLogService;
    private final UserCallPhoneService userCallPhoneService;
    private final UserPermissionRemoteService userPermissionRemoteService;
    private final SpecialCarDispatchDetailService specialCarDispatchDetailService;
    private final AxbRemoteService axbRemoteService;
    private final CarInterceptRemoteService carInterceptRemoteService;
    private final TransportMainExtendService transportMainExtendService;
    private final TransportOrderSnapshotRpcService transportOrderSnapshotRpcService;
    private final ABTestRemoteService abTestRemoteService;
    private final CallFeedbackLogService callFeedbackLogService;

    @Override
    public List<TransportVO> InfoRecommendList(GoodsInfoRecommendDTO goodsInfoRecommendDTO) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            goodsInfoRecommendDTO.setUserId(loginUser.getUserId());
        }
        BiGoodsInfoRecommendReq goodsInfoRecommendReq = new BiGoodsInfoRecommendReq();
        BeanUtil.copyProperties(goodsInfoRecommendDTO, goodsInfoRecommendReq);
        // 调用推荐接口获取货源id
        ResultMsgBean<List<BiRecommendVo>> resultMsgBean;
        try {
            resultMsgBean = biDataRemoteService.goodsInfoRecommend(goodsInfoRecommendReq);

        } catch (IOException e) {
            log.error("推荐接口返回异常，请求参数：{}", JSONUtil.toJsonStr(goodsInfoRecommendReq), e);
            return List.of();
        }

        return searchHallRpcService.afterRecommend(resultMsgBean, goodsInfoRecommendDTO.getUserId());

    }

    @Override
    public GoodsStatusVO getGoodsStatus(GoodsStatusDTO goodsStatusDTO, LoginUserDTO user) {
        GoodsStatusVO goodsStatusVO = new GoodsStatusVO();
        // 校验是否实名认证
        UserIdentityAuthVO userAuthInfo = userRemoteService.getUserAuthInfo(user.getUserId());
        if (userAuthInfo == null || !Objects.equals(userAuthInfo.getIdentityStatus(), YesNoEnum.Y.getCode())) {
            throw new BusinessException(GoodsSearchErrorCode.USER_AUTH_NO_IDENTITY);
        }
        // 校验是否为有效货源
        TransportDO transportDO = null;
        if (goodsStatusDTO.getSrcMsgId() != null) {
            transportDO = transportService.getBySrcMsgIdAndStatus(goodsStatusDTO.getSrcMsgId());
        } else {
            transportDO = transportService.getByTsOrderNoAndStatus(goodsStatusDTO.getTsOrderNo());
        }
        if (transportDO == null) {
            log.info("该货源已无效,tsOrderNo:{},userId:{}", goodsStatusDTO.getTsOrderNo(), user.getUserId());
            goodsStatusVO.setGoodStatus(YesNoEnum.N.getCode());
            return goodsStatusVO;
        }
        // 判断是否是黑名单用户及订单处罚
        NoticePopupTemplVo noticePopupTemplVo = checkLimitFindGoods(user, transportDO.getSrcMsgId());
        if (noticePopupTemplVo != null) {
            throw new BusinessException(CommonErrorCode.NOTICE_DATA_POP, noticePopupTemplVo);
        }

        // 如果是优车货源，判断是否在优车货源黑名单中
        if (Objects.equals(transportDO.getExcellentGoods(), ExcellentGoodsEnum.EXCELLENT_CAR_GOODS.getCode())) {
            checkSuperiorCarSign(user.getUserId());
        }
        goodsStatusVO = TransportConverter.INSTANCE.transport2GoodsStatus(transportDO);

        TransportMainExtendDO mainExtendDO = transportMainExtendService.getBySrcMsgId(transportDO.getSrcMsgId());
        // 秒抢货源校验，必须在 buildOrderInfo 方法前，因为秒抢货源有多笔支付单
        checkSeckillGoods(goodsStatusVO, mainExtendDO, user);
        // 设置平台补贴费用
        goodsStatusVO.setPerkPrice(mainExtendDO == null ? null : mainExtendDO.getPerkPrice());

        // 获取订单相关信息
        buildOrderInfo(goodsStatusVO, transportDO, user);

        if (goodsStatusVO.getGoodStatus().equals(YesNoEnum.N.getCode())) {
            return goodsStatusVO;
        }
        // 计算技术服务费
        computeTecServiceFee(user, goodsStatusVO);

        // 判断当前用户有没有拨打记录
        UserCallPhoneRecordDO recordDO = userCallPhoneRecordService.getByUserAndSrcMsgId(user.getUserId(), goodsStatusDTO.getSrcMsgId());
        if (recordDO != null) {
            goodsStatusVO.setContacted(YesNoEnum.Y.getCode());
        }
        // 查询开票服务上和主体
        TransportEnterpriseLogDO enterpriseLogDO = transportEnterpriseLogService.selectBySrcMsgId(goodsStatusVO.getSrcMsgId());
        if (enterpriseLogDO != null) {
            goodsStatusVO.setInvoiceSubjectId(enterpriseLogDO.getInvoiceSubjectId());
            goodsStatusVO.setServiceProviderCode(enterpriseLogDO.getServiceProviderCode());
        }
        // 校验该货源是否是抽佣货源
        goodsStatusVO.setCommissionTransport(false);
        String labelJson = transportDO.getLabelJson();
        if (StringUtils.isNotBlank(labelJson)) {
            TransportLabelJsonDTO labelJsonDTO = JSONUtil.toBean(labelJson, TransportLabelJsonDTO.class);
            if (Objects.equals(labelJsonDTO.getCommissionTransport(), YesNoEnum.Y.getCode())) {
                goodsStatusVO.setCommissionTransport(true);
            }
        }
        // 查询用户接单限制
        AcceptOrderLimitInfo acceptOrderLimitInfo = orderRemoteService.getAcceptOrderLimitInfo(user.getUserId(), transportDO.getSrcMsgId());
        goodsStatusVO.setAcceptOrderLimitInfo(acceptOrderLimitInfo);

        //专车货源返回签约合作商是否是平台
        goodsStatusVO.setSpecialCarCooperativeIsNormal(YesNoEnum.N.getCode());
        if (transportDO.getCargoOwnerId() != null && transportDO.getCargoOwnerId() == 1) {
            goodsStatusVO.setSpecialCarCooperativeIsNormal(YesNoEnum.Y.getCode());
        }
        goodsStatusVO.setStartProvinc(transportDO.getStartProvinc());
        goodsStatusVO.setDestProvinc(transportDO.getDestProvinc());
        goodsStatusVO.setDestCity(transportDO.getDestCity());
        goodsStatusVO.setTaskContent(transportDO.getTaskContent());
        goodsStatusVO.setWeight(transportDO.getWeight());
        return goodsStatusVO;
    }

    private void checkSecKillGoodsBlack(LoginUserDTO user) {
        ABTestDto abTestDto = new ABTestDto(List.of(SEC_KILL_GOODS_LIMIT), user.getUserId());
        List<ABTestVo> abTestVoList = abTestRemoteService.getUserTypeList(abTestDto);
        if (CollUtil.isNotEmpty(abTestVoList)) {
            ABTestVo abTestVo = abTestVoList.get(0);
            Date importModifyTime = abTestVo.getImportModifyTime();
            if (abTestVo.getType() == 1 && abTestVo.getImportModifyTime() != null) {
                // 黑名单截止时间为导入时间+3天，精确到年月日
                DateTime dateTime = DateUtil.beginOfDay(DateUtil.offsetDay(importModifyTime, 2));
                // 如果在限制范围内，弹窗提示
                if (new Date().before(dateTime)) {
                    String errorMsg = "由于您接单时填写过不真实车辆，即日起至" + DateUtil.format(dateTime, CHINESE_DATE_PATTERN) + "限制您承接秒抢货源";
                    throw BusinessException.createException(GoodsSearchErrorCode.SUPERIOR_CAR_SIGN_BLACK_ERROR.getCode(), errorMsg);
                }
            }
        }
    }

    /**
     * 秒抢货源校验
     */
    private void checkSeckillGoods(GoodsStatusVO goodsStatusVO, TransportMainExtendDO mainExtendDO, LoginUserDTO user) {
        goodsStatusVO.setSeckillGoods(mainExtendDO == null ? null : mainExtendDO.getSeckillGoods());
        if (Objects.equals(goodsStatusVO.getSeckillGoods(), 1) && Objects.equals(goodsStatusVO.getStatus(), YesNoEnum.Y.code)) {
            // 确定秒抢好货黑名单
            checkSecKillGoodsBlack(user);

            CheckSeckillGoodsPayLimitDTO payLimitDTO = this.checkSeckillGoodsPayLimit(goodsStatusVO.getSrcMsgId(), user.getUserId());
            log.info("goodsStatus秒抢货源校验，payLimitDTO：{}", JSON.toJSONString(payLimitDTO));
            // 如果货源是发布中，且当前用户已支付过，则提示用户
            if (payLimitDTO != null && payLimitDTO.isPayed()) {
                Date firstPayedTime = payLimitDTO.getFirstPayedTime() == null ? new Date() : payLimitDTO.getFirstPayedTime();
                Date endTime = DateUtils.addMinutes(firstPayedTime, 3);
                String errorMsg = "该货源为秒抢货源，您已参与，等待抢单结果，<span style=\"color:#3194EF;\">预计" + new SimpleDateFormat("HH:mm").format(endTime) + "公布结果。</span>";
                throw BusinessException.createException(GoodsSearchErrorCode.SECKILL_GOODS_PAYMENT.getCode(), errorMsg);
            }
        }
    }

    /**
     * 计算技术服务费
     *
     * @param user
     * @param goodsStatusVO
     */
    private void computeTecServiceFee(LoginUserDTO user, GoodsStatusVO goodsStatusVO) {
        if (goodsStatusVO.getTecServiceFee() != null) {
            TecServiceFeeVO tecServiceFeeVO = getTecServiceFeeFromPlat(user.getUserId(), goodsStatusVO.getSrcMsgId());
            if (tecServiceFeeVO != null) {
                if (Objects.nonNull(tecServiceFeeVO.getTecServiceFee())) {
                    goodsStatusVO.setTecServiceFee(tecServiceFeeVO.getTecServiceFee().doubleValue());
                }
                if (Objects.nonNull(tecServiceFeeVO.getTecServiceFeeAfterDiscount())) {
                    BigDecimal strikeThroughPrice = tecServiceFeeVO.getTecServiceFeeAfterDiscount().setScale(0, RoundingMode.CEILING);
                    goodsStatusVO.setStrikeThroughPrice(strikeThroughPrice);
                }
                if (Objects.nonNull(tecServiceFeeVO.getTecServiceFeeAfterDiscountDValue())) {
                    BigDecimal promotionPrice = tecServiceFeeVO.getTecServiceFeeAfterDiscountDValue().setScale(0, RoundingMode.CEILING);
                    goodsStatusVO.setPromotionPrice(promotionPrice);
                }
            }
        }

    }

    /**
     * 从plat获取技术服务费，后续迁移后需要改造
     *
     * @param userId
     * @param srcMsgId
     * @return
     */
    private TecServiceFeeVO getTecServiceFeeFromPlat(Long userId, Long srcMsgId) {
        try {
            ResultMsgBean result = platTransportRemoteService.getTecServiceFee(userId, srcMsgId);
            if (result != null && result.getCode() == 200) {
                return JSONUtil.parseObj(result.getData()).toBean(TecServiceFeeVO.class);
            }
        } catch (Exception e) {
            log.error("getTecServiceFee error srcMsgId: {}  ", srcMsgId, e);
        }
        return null;
    }

    @Override
    public void checkSuperiorCarSign(Long userId) {
        //  校验是否在优车货源签约黑名单中
        SuperiorCarSignVO carSignVO = null;
        try {
            carSignVO = superiorCarSignRemoteService.getSuperiorCarSignByUserId(userId);
        } catch (Exception e) {
            log.error("校验是否在优车货源签约黑名单中异常", e);
        }
        // signStatus = 2为黑名单用户
        if (carSignVO != null && carSignVO.getSignStatus() == 2) {
            if (StringUtils.isNotBlank(carSignVO.getHandleReason())) {
                String errorMsg = "当前账号因<font color='#F52F3E'>【" + carSignVO.getHandleReason() + "】</font>无法接单优车货源，如有疑问请联系客服。";

                throw BusinessException.createException(GoodsSearchErrorCode.SUPERIOR_CAR_SIGN_BLACK_ERROR.getCode(), errorMsg);
            } else {
                throw new BusinessException(GoodsSearchErrorCode.SUPERIOR_CAR_SIGN_BLACK_ERROR);
            }
        }

    }

    /**
     * 获取订单信息
     *
     * @param goodsStatusVO
     * @param transportDO
     * @param user
     */
    private void buildOrderInfo(GoodsStatusVO goodsStatusVO, TransportDO transportDO, LoginUserDTO user) {
        // 只返回待支付(costStatus = 10)和已经支付的记录(costStatus>=15)，已经取消的订单返回null
        TransportOrdersVO ordersVO = orderRemoteService.getPayInfoByPayUserIdAndTsOrder(user.getUserId(), transportDO.getTsOrderNo());
        if (ordersVO != null) {
            if (ordersVO.getCostStatus() >= 15) {
                log.info("该货源已被支付,tsOrderNo:{},userId:{}", transportDO.getTsOrderNo(), user.getUserId());
                goodsStatusVO.setGoodStatus(YesNoEnum.N.getCode());
                return;
            }
            BeanUtil.copyProperties(ordersVO, goodsStatusVO);
            goodsStatusVO.setOrderId(ordersVO.getId());
            goodsStatusVO.setPayAmount(ordersVO.getPayAmount().doubleValue() / 100);
            goodsStatusVO.setTecServiceFee(ordersVO.getTecServiceFee().doubleValue() / 100);
            goodsStatusVO.setTotalOrderAmount(ordersVO.getTotalOrderAmount().doubleValue() / 100);
            goodsStatusVO.setCouponAmount(ordersVO.getCouponAmount().doubleValue() / 100);
            goodsStatusVO.setPayUrl(configRemoteService.getStringValue(INFO_FEE_PAY_REDIRECT_URL_FOR_SERVER));
            goodsStatusVO.setCarId(ordersVO.getCarId());
            if (ordersVO.getDriverId() != null) {
                Optional.ofNullable(driverRemoteService.getInfoByDriverId(ordersVO.getDriverId())).ifPresent(driverInfo -> {
                    goodsStatusVO.setDriverId(driverInfo.getId());
                    goodsStatusVO.setDriverName(driverInfo.getName());
                    goodsStatusVO.setDriverPhone(driverInfo.getPhone());

                });
            }
            Optional.ofNullable(ordersVO.getCarriageFee()).ifPresent(carriageFee -> goodsStatusVO.setCarriageFee(carriageFee.doubleValue()));

            if (Objects.nonNull(ordersVO.getPromotionPrice()) && ordersVO.getPromotionPrice().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal promotionPrice = ordersVO.getPromotionPrice().setScale(0, RoundingMode.CEILING);
                goodsStatusVO.setPromotionPrice(promotionPrice);
            }
            if (Objects.nonNull(ordersVO.getStrikeThroughPrice()) && ordersVO.getStrikeThroughPrice().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal strikeThroughPrice = ordersVO.getStrikeThroughPrice().setScale(0, RoundingMode.CEILING);
                goodsStatusVO.setStrikeThroughPrice(strikeThroughPrice);
            }
        }
    }

    // 支付时校验是否可以支付专车货源
    @Override
    public void checkSpecialCar(GoodsStatusVO goodsStatusVO, LoginUserDTO user) {
        if (Objects.equals(ExcellentGoodsEnum.SPECIAL_CAR_GOODS.getCode(), goodsStatusVO.getExcellentGoods())) {
            //专车货源且出发地为配置中的货源提示一句话，支付成功后自动加入后台专车车主中
            String startPoints = configRemoteService.getStringValue(SPECIAL_CAR_START_POINT_PUBLIC);
            if (StringUtils.isNotBlank(startPoints) && startPoints.contains(goodsStatusVO.getStartCity())) {
                goodsStatusVO.setSpecialCarStartPoint("支付即同意加入专车，享受专车货源优先派");
            }
            //校验是否可支付专车货源
            Integer checkResult = checkSpecialPayment(goodsStatusVO.getSrcMsgId(), user.getUserId());
            goodsStatusVO.setDeclareInPublic(checkResult);
            // 校验专车driver和car是否通过（是否可以支付专车货源）
            if (goodsStatusVO.getCarId() != null && goodsStatusVO.getDriverId() != null) {
                CheckCarDriverVO checkCarDriverVO = carRemoteService.checkCarDriver(goodsStatusVO.getCarId(), goodsStatusVO.getDriverId(), user.getUserId(), null, goodsStatusVO.getDeclareInPublic());
                goodsStatusVO.setCheckCar(checkCarDriverVO != null && Objects.equals(checkCarDriverVO.getCarStatus(), Boolean.TRUE) ? 1 : 0);
                goodsStatusVO.setCheckDriver(checkCarDriverVO != null && Objects.equals(checkCarDriverVO.getDriverStatus(), Boolean.TRUE) ? 1 : 0);
            }

        }
    }


    private NoticePopupTemplVo checkLimitFindGoods(LoginUserDTO user, Long srcMsgId) {
        UserLimitInfoRpcVO userLimitInfo = userLimitRemoteService.getUserLimitInfoNew(user.getUserId(), srcMsgId);
        if (userLimitInfo == null || userLimitInfo.getCarLimitMinutes() == null) {
            return null;
        }
        NoticePopupTemplVo noticePopupTemplVo = null;
        // 发货长期被限制
        if (Objects.nonNull(userLimitInfo.getCarLimitMinutes()) && userLimitInfo.getCarLimitMinutes() == -1) {
            noticePopupTemplVo = longTermLimit(userLimitInfo, user);
        }
        // 发货短期被限制
        if (Objects.nonNull(userLimitInfo.getCarLimitEndTime()) && userLimitInfo.getCarLimitEndTime().after(new Date())) {
            noticePopupTemplVo = shortTermLimit(userLimitInfo, user);
        }
        return noticePopupTemplVo;
    }

    private NoticePopupTemplVo shortTermLimit(UserLimitInfoRpcVO userLimitInfo, LoginUserDTO user) {
        UserOrdersLimitInfoRpcVO userOrdersLimitInfo = userLimitRemoteService.getUserOrdersLimitInfo(user.getUserId(), false);
        TransportOrdersVO ordersVO = null;
        NoticePopupTemplVo noticePopupTemplVo;

        if (userOrdersLimitInfo != null) {
            ordersVO = orderRemoteService.getPayInfoByPayUserIdAndTsOrder(user.getUserId(), userOrdersLimitInfo.getTsOrderNo());
        }

        if (userOrdersLimitInfo == null || ordersVO == null) {
            noticePopupTemplVo = noticePopupTemplRemoteService.getByType(PopupTypeEnum.找货时间段限制);
            String format = String.format(noticePopupTemplVo.getMasterContent(), userLimitInfo.getCellPhone(), DateUtil.formatDateTime(userLimitInfo.getCarLimitEndTime()));
            noticePopupTemplVo.setMasterContent(format);

        } else {
            noticePopupTemplVo = noticePopupTemplRemoteService.getByType(PopupTypeEnum.找货时间段限制_有订单的处罚);
            String taskContent = ordersVO.getTaskContent();
            if (taskContent.length() > 5) {
                taskContent = taskContent.substring(0, 5);
                taskContent = taskContent + "...";
            }
            String format = String.format(noticePopupTemplVo.getMasterContent(), userLimitInfo.getCellPhone(), ordersVO.getTsOrderNo(), ordersVO.getStartPoint(), ordersVO.getDestPoint(), taskContent, DateUtil.formatDateTime(userLimitInfo.getCarLimitEndTime()));
            noticePopupTemplVo.setMasterContent(format);
        }
        return noticePopupTemplVo;
    }

    /**
     * 长期限制
     *
     * @param userLimitInfo
     * @param user
     * @return
     */
    private NoticePopupTemplVo longTermLimit(UserLimitInfoRpcVO userLimitInfo, LoginUserDTO user) {
        UserOrdersLimitInfoRpcVO userOrdersLimitInfo = userLimitRemoteService.getUserOrdersLimitInfo(user.getUserId(), true);
        TransportOrdersVO ordersVO = null;
        NoticePopupTemplVo noticePopupTemplVo;

        if (userOrdersLimitInfo != null) {
            ordersVO = orderRemoteService.getPayInfoByPayUserIdAndTsOrder(user.getUserId(), userOrdersLimitInfo.getTsOrderNo());
        }

        if (userOrdersLimitInfo == null || ordersVO == null) {
            noticePopupTemplVo = noticePopupTemplRemoteService.getByType(PopupTypeEnum.找货长期限制);
            String format = String.format(noticePopupTemplVo.getMasterContent(), userLimitInfo.getCellPhone());
            noticePopupTemplVo.setMasterContent(format);

        } else {
            noticePopupTemplVo = noticePopupTemplRemoteService.getByType(PopupTypeEnum.找货长期限制_有订单的处罚);
            String taskContent = ordersVO.getTaskContent();
            if (taskContent.length() > 5) {
                taskContent = taskContent.substring(0, 5);
                taskContent = taskContent + "...";
            }
            String format = String.format(noticePopupTemplVo.getMasterContent(), userLimitInfo.getCellPhone(), ordersVO.getTsOrderNo(), ordersVO.getStartPoint(), ordersVO.getDestPoint(), taskContent, DateUtil.formatDateTime(userLimitInfo.getCarLimitEndTime()));
            noticePopupTemplVo.setMasterContent(format);
        }
        return noticePopupTemplVo;

    }

    @Override
    public Integer checkSpecialPayment(Long srcMsgId, Long userId) {

        TransportMainDO transportMainDO = transportMainService.getById(srcMsgId);
        // 非专车货源不阻断
        if (transportMainDO == null || !Objects.equals(transportMainDO.getExcellentGoods(), ExcellentGoodsEnum.SPECIAL_CAR_GOODS.getCode())) {
            return YesNoEnum.Y.getCode();
        }

        List<SpecialCarDispatchFailureDO> dispatchFailureDOList = specialCarDispatchFailureService.getBySrcMsgIds(Collections.singletonList(srcMsgId));
        // 如果该货源标识了可大厅抢单，所有车辆都可以支付，否则，只有签约了的才能支付
        if (CollUtil.isNotEmpty(dispatchFailureDOList)) {
            SpecialCarDispatchFailureDO specialCarDispatchFailureDO = dispatchFailureDOList.get(0);
            if (Objects.equals(specialCarDispatchFailureDO.getDeclareInPublic(), YesNoEnum.Y.getCode())) {
                return YesNoEnum.Y.getCode();
            }
        }
        // 判断是否在专车签约车辆内，如果在里面才可以支付专车货源
        List<TytSigningCarVO> signingCarVOList = signingCarRemoteService.getByUserId(userId);
        if (CollUtil.isNotEmpty(signingCarVOList)) {
            return YesNoEnum.Y.getCode();
        }
        // 都不满足上面的条件，则不能支付
        return YesNoEnum.N.getCode();
    }

    @Override
    public boolean checkSpecialCarPayLimit(Long userId) {
        // 10分钟内是否支付过专车货源
        return orderRemoteService.isSpecialCarPayWithInMin(userId, 10);
    }

    @Override
    public GoodsPhoneVO getGoodsPhone(GoodsPhoneDTO goodsPhoneDTO, LoginUserDTO user) {
        GoodsPhoneVO goodsPhoneVO = new GoodsPhoneVO();
        // 校验是否为有效货源
        TransportMainDO transportMainDO = transportMainService.getById(goodsPhoneDTO.getSrcMsgId());

        if (transportMainDO == null) {
            log.error("货源不存在或不在发布中,srcMsgId:{},userId:{}", goodsPhoneDTO.getSrcMsgId(), user.getUserId());
            throw new BusinessException(GoodsSearchErrorCode.GOODS_NOT_PUBLISH);
        }
        // 判断是否是黑名单用户及订单处罚
        NoticePopupTemplVo noticePopupTemplVo = checkLimitFindGoods(user, transportMainDO.getSrcMsgId());
        if (noticePopupTemplVo != null) {
            throw new BusinessException(CommonErrorCode.NOTICE_DATA_POP, noticePopupTemplVo);
        }

        checkTransport(transportMainDO, goodsPhoneDTO, user, goodsPhoneVO);
        // 如果已经支付成功过，直接拨打
        if (goodsPhoneVO.getSrcMsgId() != null) {
            return goodsPhoneVO;
        }

        // 如果是优车货源，判断是否在优车货源黑名单中
        if (Objects.equals(transportMainDO.getExcellentGoods(), ExcellentGoodsEnum.EXCELLENT_CAR_GOODS.getCode())) {
            checkSuperiorCarSign(user.getUserId());
        }
        // 如果是一口价订单，要判断是否支付过了，一口价订单只有支付过后才能够拨打电话
        checkPublishType(transportMainDO, goodsPhoneDTO, user);
        // 校验用户是否成功获取过手机号，如果成功获取过，就直接返回电话
        UserCallPhoneRecordDO userCallPhoneRecordDO = userCallPhoneRecordService.getByUserAndSrcMsgId(user.getUserId(), transportMainDO.getId());
        if (userCallPhoneRecordDO != null) {
            // 如果已经获取过手机号，则直接返回该记录
            successGetPhone(goodsPhoneDTO, user.getUserId(), transportMainDO, goodsPhoneVO);
            log.info("用户已经获取过手机号，直接返回手机号记录，srcMsgId:{},userId:{}", goodsPhoneDTO.getSrcMsgId(), user.getUserId());
        } else {
            //获取用户实名认证信息
            UserIdentityAuthVO userAuthInfo = null;
            try {
                userAuthInfo = userRemoteService.getUserAuthInfo(user.getUserId());
            } catch (Exception e) {
                log.error("获取用户实名认证信息异常，srcMsgId:{},userId:{}", goodsPhoneDTO.getSrcMsgId(), user.getUserId(), e);
            }
            if (userAuthInfo == null || !Objects.equals(userAuthInfo.getIdentityStatus(), YesNoEnum.Y.getCode())) {
                // 未实名认证用户，如果有豁免，返回成功，否则返回失败
                goodsPhoneVO = checkAuthForGetPhone(goodsPhoneDTO, user.getUserId(), transportMainDO, goodsPhoneVO);
                log.info("用户有豁免权益，直接返回手机号记录，srcMsgId:{},userId:{}", goodsPhoneDTO.getSrcMsgId(), user.getUserId());

            } else {
                // 校验车辆认证阻断
                Integer interceptCount = checkCarBlock(goodsPhoneDTO, user, transportMainDO);
                // 已实名认证用户，校验是否有拨打货源的权限
                goodsPhoneVO = checkPermissionForGetPhone(goodsPhoneDTO, user.getUserId(), transportMainDO, goodsPhoneVO);
                if (Objects.nonNull(interceptCount)) {
                    goodsPhoneVO.setRemainCallCount(interceptCount - 1);
                }
                log.info("用户已实名且有拨打权益，返回手机号记录，srcMsgId:{},userId:{}", goodsPhoneDTO.getSrcMsgId(), user.getUserId());
            }
            // 获取电话成功，添加进tyt_user_call_phone_record表
            int a = userCallPhoneRecordService.saveRecord(goodsPhoneDTO, user, transportMainDO);
            log.info("添加电话记录【{}】，【{}】", user.getUserId(), a);

            //异步-新用户增加弹窗
            userCallPhoneRecordService.userActiveGift(user.getUserId());
        }
        //获取货主的会员信息，给车方一个风险提示
        getTextRemind(transportMainDO, goodsPhoneVO);

        // 查询用户接单限制
        try {
            AcceptOrderLimitInfo acceptOrderLimitInfo = orderRemoteService.getAcceptOrderLimitInfo(user.getUserId(), transportMainDO.getSrcMsgId());
            goodsPhoneVO.setAcceptOrderLimitInfo(acceptOrderLimitInfo);
        } catch (Exception e) {
            log.error("查询用户接单限制异常，srcMsgId:{},userId:{}", goodsPhoneDTO.getSrcMsgId(), user.getUserId(), e);
        }
        // 判断是否弹出虚拟号弹窗
        try {
            PrivacyPhoneTabInfo privacyPhoneTabInfo = axbRemoteService.getPrivacyPhoneTabInfo(transportMainDO.getSrcMsgId(), user.getUserId());
            if (privacyPhoneTabInfo != null) {
                goodsPhoneVO.setPrivacyPhoneTabInfo(privacyPhoneTabInfo);
            }
        } catch (Exception e) {
            log.error("查询虚拟号弹窗信息异常，srcMsgId:{},userId:{}", goodsPhoneDTO.getSrcMsgId(), user.getUserId(), e);
        }

        return goodsPhoneVO;

    }


    /**
     * 校验货源信息
     *
     * @param transportMainDO
     * @param user
     * @param goodsPhoneVO
     */
    private GoodsPhoneVO checkTransport(TransportMainDO transportMainDO, GoodsPhoneDTO goodsPhoneDTO, LoginUserDTO user, GoodsPhoneVO goodsPhoneVO) {

        // 如果不在发布中
        if (!Objects.equals(transportMainDO.getStatus(), GoodsStatusEnum.PUBLISHED.getCode())) {
            // 如果已经成交，且成交货源是当前支付人，则可以直接拨打，否则提示货源已下架,或者调车数量大于1
            if (Objects.equals(transportMainDO.getStatus(), GoodsStatusEnum.DEAL.getCode()) || transportMainDO.getShuntingQuantity() > 1) {
                TransportOrdersRpcDTO transportOrdersRpcDTO = new TransportOrdersRpcDTO();
                transportOrdersRpcDTO.setPayUserId(user.getUserId());
                transportOrdersRpcDTO.setTsOrderNoList(Collections.singletonList(transportMainDO.getTsOrderNo()));
                transportOrdersRpcDTO.setPayStatus(OrderPayStatusEnum.PAY_SUCCESS.getCode());
                List<TransportOrdersListVO> transportOrdersListVOList = orderRemoteService.getByPayUserIdAndTsOrderNoList(transportOrdersRpcDTO);
                if (CollUtil.isNotEmpty(transportOrdersListVOList)) {
                    log.info("用户已经支付成功，直接返回手机号,srcMsgId:{},userId:{}", goodsPhoneDTO.getSrcMsgId(), user.getUserId());
                    successGetPhone(goodsPhoneDTO, user.getUserId(), transportMainDO, goodsPhoneVO);
                    // 判断是否弹出虚拟号弹窗
                    PrivacyPhoneTabInfo privacyPhoneTabInfo = axbRemoteService.getPrivacyPhoneTabInfo(transportMainDO.getSrcMsgId(), user.getUserId());
                    if (privacyPhoneTabInfo != null) {
                        goodsPhoneVO.setPrivacyPhoneTabInfo(privacyPhoneTabInfo);
                    }
                    return goodsPhoneVO;
                } else {
                    throw new BusinessException(GoodsSearchErrorCode.GOODS_NOT_PUBLISH);
                }
            }
            throw new BusinessException(GoodsSearchErrorCode.GOODS_NOT_PUBLISH);
        }
        return goodsPhoneVO;
    }

    /**
     * 校验秒抢货源支付限制
     *
     * @param srcMsgId
     * @param userId
     */
    @Override
    public CheckSeckillGoodsPayLimitDTO checkSeckillGoodsPayLimit(Long srcMsgId, Long userId) {
        return transportOrderSnapshotRpcService.checkSeckillGoodsPayLimit(srcMsgId, userId);
    }

    /**
     * 校验车辆认证是否阻断
     *
     * @param goodsPhoneDTO
     * @param user
     * @param transportMainDO
     */
    private Integer checkCarBlock(GoodsPhoneDTO goodsPhoneDTO, LoginUserDTO user, TransportMainDO transportMainDO) {
        // 需要进行车辆认证阻断
        if (Objects.equals(goodsPhoneDTO.getNoCarBlock(), YesNoEnum.Y.getCode())) {
            Date now = new Date();
            long betweenDay = DateUtil.betweenDay(user.getCtime(), now, false);
            // intercept为 1阻断，0不阻断
            CarInterceptVO carInterceptVO = null;
            // 注册时间小于30天，查所有的拨打量，否则查一个月的拨打量
            try {
                if (betweenDay <= 30) {
                    int callCount = userCallPhoneRecordService.getDistinctTsCountByUserId(user.getUserId(), null, null);
                    carInterceptVO = carInterceptRemoteService.carInterceptV2(transportMainDO.getId(), user.getUserId(), callCount, null);
                } else {
                    Date startTime = DateUtil.offsetDay(now, -30);
                    int oneMonthCallCount = userCallPhoneRecordService.getDistinctTsCountByUserId(user.getUserId(), startTime, now);
                    carInterceptVO = carInterceptRemoteService.carInterceptV2(transportMainDO.getId(), user.getUserId(), null, oneMonthCallCount);
                }
            } catch (Exception e) {
                log.error("查询车辆认证阻断信息异常，srcMsgId:{},userId:{}", goodsPhoneDTO.getSrcMsgId(), user.getUserId(), e);
            }
//            if (Objects.equals(intercept, YesNoEnum.Y.getCode())) {
//                if (Objects.equals(ClientSignEnum.PC.getCode(), LoginHelper.getBaseParam().getClientSign())) {
//                    throw new BusinessException(GoodsSearchErrorCode.NEW_CAR_PC_REMARK);
//                } else {
//                    throw new BusinessException(GoodsSearchErrorCode.NEW_CAR_APP_REMARK);
//                }
//            }
            if (Objects.nonNull(carInterceptVO)) {
                if (Boolean.TRUE.equals(carInterceptVO.getIsIntercept())) {
                    if (Objects.isNull(carInterceptVO.getInterceptCount())) {
                        if (Objects.equals(ClientSignEnum.PC.getCode(), LoginHelper.getBaseParam().getClientSign())) {
                            throw new BusinessException(GoodsSearchErrorCode.NEW_CAR_PC_REMARK);
                        } else {
                            throw new BusinessException(GoodsSearchErrorCode.NEW_CAR_APP_REMARK);
                        }
                    } else {
                        return carInterceptVO.getInterceptCount();
                    }
                }
            }
        }
        return null;
    }

    /**
     * 专车货源未成交只显示装卸货电话
     */
    private boolean checkSpecialTransport(TransportMainDO transportMainDO, GoodsPhoneDTO goodsPhoneDTO) {
        return Objects.equals(transportMainDO.getExcellentGoods(), ExcellentGoodsEnum.SPECIAL_CAR_GOODS.getCode())
                && Objects.equals(transportMainDO.getStatus(), GoodsStatusEnum.PUBLISHED.getCode())
                // 专车货源显示装卸货电话特定code
                && Objects.equals(goodsPhoneDTO.getPath(), "detailPage2callPhone4specialCarGoods");
    }

    /**
     * 获取货主的会员信息，给车方一个风险提示
     *
     * @param transportMainDO
     * @param goodsPhoneVO
     */
    private void getTextRemind(TransportMainDO transportMainDO, GoodsPhoneVO goodsPhoneVO) {

        UserPermissionRpcDTO userPermissionRpcDTO = new UserPermissionRpcDTO();
        userPermissionRpcDTO.setUserId(transportMainDO.getUserId());
        userPermissionRpcDTO.setStatus(YesNoEnum.Y.getCode());
        try {
            List<UserPermissionRpcVO> goodsVipPermissionVOList = userPermissionRemoteService.getUserPermissionByUserId(userPermissionRpcDTO);

            // 提取权限类型ID列表
            List<String> permissionTypeIdList = CollUtil.isEmpty(goodsVipPermissionVOList)
                    ? Collections.emptyList()
                    : goodsVipPermissionVOList.stream().map(UserPermissionRpcVO::getServicePermissionTypeId).toList();

            //当非会员发布电议【不退还】车方找货模式的货源，车主app在此处做做风险提示：
            boolean needsReminder = transportMainDO.getPublishType().equals(PublishTypeEnum.TEL.getCode())
                    && RefundFlagEnum.NO_REFUND.getCode().equals(transportMainDO.getRefundFlag())
                    && !permissionTypeIdList.contains(UserPermissionTypeEnum.GOODS_VIP.getTypeId())
                    && !permissionTypeIdList.contains(UserPermissionTypeEnum.NEW_GOODS_VIP.getTypeId());

            if (needsReminder) {
                String textRemind = configRemoteService.getStringValue(NO_GOODSMEMBER_TEXT_REMIND, "该用户不是发货会员，请通过平台支付订金。");
                goodsPhoneVO.setNoGoodsMemberText(textRemind);
            }
        } catch (Exception e) {
            log.error("获取货主的会员信息异常", e);
        }

    }

    private GoodsPhoneVO checkPermissionForGetPhone(GoodsPhoneDTO goodsPhoneDTO, Long userId, TransportMainDO transportMainDO, GoodsPhoneVO goodsPhoneVO) {
        // 校验用户有没有拨打货源的权益
        AuthPermissionRpcDTO authPermissionRpcDTO = new AuthPermissionRpcDTO();
        authPermissionRpcDTO.setUserId(userId);
        authPermissionRpcDTO.setServicePermissionEnum(ServicePermissionEnum.拨打货源电话);
        AuthPermissionRpcVO authPermissionRpcVO = new AuthPermissionRpcVO();
        try {
            authPermissionRpcVO = userPermissionRemoteService.authPermission(authPermissionRpcDTO);
        } catch (Exception e) {
            throw new BusinessException(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
        // 查询用户
        if (!authPermissionRpcVO.isUse()) {
            // 没有权益的时候还要看看是否是自动指派货源（就是在指派表里有没有记录），如果是自动指派货源，也可以直接拨打
            Integer count = specialCarDispatchDetailService.selectCountByUserAndGoodsId(userId, transportMainDO.getId());
            if (count <= 0) {
                // 没有拨打权益，记录获取手机号失败，返回相应弹窗
                failGetPhone(authPermissionRpcVO, userId, transportMainDO.getId());
            }
        }
        return successGetPhone(goodsPhoneDTO, userId, transportMainDO, goodsPhoneVO);
    }

    /**
     * 获取手机号失败的情况
     *
     * @param authPermissionRpcVO
     * @param userId
     * @param id
     * @return
     */
    private void failGetPhone(AuthPermissionRpcVO authPermissionRpcVO, Long userId, Long id) {
        ActivateCheckVO activateCheckVO = userRemoteService.checkUserActivate(userId, UserActivateFlagEnum.CALL_PHONE.getCode(), authPermissionRpcVO.isUse());
        if (activateCheckVO.isActivate()) {
            throw new BusinessException(CommonErrorCode.NOTICE_DATA_POP, activateCheckVO.getTemplBean());
        }
        PopupTypeEnum popupTypeEnum = PopupTypeEnum.getByName(authPermissionRpcVO.getPermissionPopupTypeEnum().name());
        NoticePopupTemplVo noticePopupTemplVo = noticePopupTemplRemoteService.getByType(popupTypeEnum);
        throw new BusinessException(CommonErrorCode.NOTICE_DATA_POP, noticePopupTemplVo);
    }

    /**
     * 获取已经拨打的电话记录，并记录拨打成功日志
     *
     * @param goodsPhoneDTO
     * @param userId
     * @param transportMainDO
     * @return
     */
    private GoodsPhoneVO successGetPhone(GoodsPhoneDTO goodsPhoneDTO, Long userId, TransportMainDO transportMainDO, GoodsPhoneVO goodsPhoneVO) {
        if (transportMainDO == null) {
            transportMainDO = transportMainService.getById(goodsPhoneDTO.getSrcMsgId());
        }
        if (transportMainDO == null) {
            throw new BusinessException(CommonErrorCode.ERROR_PARAMETER_ERROR);
        }
        BeanUtil.copyProperties(transportMainDO, goodsPhoneVO);
        // 如果是专车货源，只显示装卸货电话
        if (checkSpecialTransport(transportMainDO, goodsPhoneDTO)) {
            goodsPhoneVO.setTel(null);
            goodsPhoneVO.setTel3(null);
            goodsPhoneVO.setTel4(null);
        }
        // 获取拨打记录
        AppCallLogDO appCallLogDO = appCallLogService.getBySrcMsgIdAndUserId(userId, goodsPhoneDTO.getSrcMsgId());
        if (appCallLogDO != null) {
            BeanUtil.copyProperties(appCallLogDO, goodsPhoneVO);
            goodsPhoneVO.setCallStatus(appCallLogDO.getCallResultName());
            goodsPhoneVO.setCallStatusCode(appCallLogDO.getCallResultCode() == null ? 0 : appCallLogDO.getCallResultCode());
        }
        // 记录获取手机号日志
        userCallPhoneService.saveRecord(goodsPhoneDTO, userId, ActionTypeEnum.SUCCESS_GET_PHONE);
        return goodsPhoneVO;
    }

    /**
     * 获取手机号时校验是否实名认证
     *
     * @param goodsPhoneDTO
     * @param userId
     * @param transportMainDO
     */
    private GoodsPhoneVO checkAuthForGetPhone(GoodsPhoneDTO goodsPhoneDTO, Long userId, TransportMainDO transportMainDO, GoodsPhoneVO goodsPhoneVO) {
        // 如果有豁免，直接返回成功拨打电话记录
        if (checkExempt(userId)) {
            return successGetPhone(goodsPhoneDTO, userId, transportMainDO, goodsPhoneVO);
        }
        //没有豁免的话返回弹窗
        NoticePopupTemplVo noticePopupTemplVo = noticePopupTemplRemoteService.getByType(PopupTypeEnum.未实名认证);

        throw new BusinessException(GoodsSearchErrorCode.USER_AUTH_NO_IDENTITY, noticePopupTemplVo);

    }

    /**
     * 校验未实名认证用户是否有豁免权益
     *
     * @param userId
     */
    private boolean checkExempt(Long userId) {
        boolean exempt = false;
        // 未认证用户N次豁免拨打开关，0关闭，1奇数开启，2全部开启
        Integer unAuthExemptSwitch = configRemoteService.getIntValue(UNAUTH_USER_CALL_EXEMPT_SWITCH, 0);
        if (unAuthExemptSwitch != 0) {
            // 新增开关配置，配置豁免次数N
            boolean hasCallExempt = unAuthExemptSwitch == 1 && userId % 2 == 1 || unAuthExemptSwitch == 2;
            Integer registerGetCallCount = configRemoteService.getIntValue(
                    hasCallExempt ? UNAUTH_CALL_EXEMPT_NUMS : UNAUTH_CALL_EXEMPT_NUMS_FIX, 0);
            log.info("未实名认证N次拨打豁免，是否能豁免：{}，userId:{}", hasCallExempt, userId);

            // 查看豁免次数是否已经达到数值
            int count = userCallPhoneRecordService.getDistinctTsCountByUserId(userId, null, null);
            log.info("未实名认证N次拨打豁免，豁免次数：{}，已拨打次数：{},userId:{}", registerGetCallCount, count, userId);
            // 如果豁免次数未达到，则直接返回成功拨打电话记录
            if (count < registerGetCallCount) {
                exempt = true;
            }
        }
        return exempt;
    }

    /**
     * 校验用户是否支付过一口价货源，如果没支付，不允许获取手机号
     *
     * @param transportMainDO
     * @param user
     */
    private void checkPublishType(TransportMainDO transportMainDO, GoodsPhoneDTO goodsPhoneDTO, LoginUserDTO user) {
        if (checkSpecialTransport(transportMainDO, goodsPhoneDTO)) {
            return;
        }
        if (Objects.equals(transportMainDO.getPublishType(), PublishTypeEnum.FIXED.getCode())) {
            Boolean b = infoFeeRemoteService.userIsPay(transportMainDO.getId(), user.getUserId());
            if (!b) {
                throw new BusinessException(GoodsSearchErrorCode.FIX_NOT_PAY);
            }
        }
    }


}
