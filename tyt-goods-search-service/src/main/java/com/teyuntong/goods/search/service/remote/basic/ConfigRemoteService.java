package com.teyuntong.goods.search.service.remote.basic;

import com.teyuntong.infra.basic.resource.client.tytconfig.service.ConfigRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 调用开关配置
 *
 * <AUTHOR>
 * @since 2024/07/05 11:31
 */
@FeignClient(name = "tyt-infra-basic-resource", path = "basic-resource", contextId = "configRemoteService",
        fallbackFactory = ConfigRemoteService.ConfigRemoteServiceFallback.class)
public interface ConfigRemoteService extends ConfigRpcService {

    @Component
    class ConfigRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<ConfigRemoteService> {
        public ConfigRemoteServiceFallback() {
            super(true, ConfigRemoteService.class);
        }
    }
}
