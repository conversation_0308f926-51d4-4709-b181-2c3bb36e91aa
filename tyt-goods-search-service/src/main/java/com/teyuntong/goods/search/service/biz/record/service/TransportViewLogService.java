package com.teyuntong.goods.search.service.biz.record.service;


import com.github.pagehelper.PageInfo;
import com.teyuntong.goods.search.service.biz.goods.dto.SearchRecordQueryDTO;
import com.teyuntong.goods.search.service.biz.record.entity.TransportViewLogDO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 货物详情浏览日志表，每人每货仅存储一条 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
public interface TransportViewLogService {

    /**
     * 获取最近的车主浏览记录
     */
    PageInfo<TransportViewLogDO> getRecentViewPage(SearchRecordQueryDTO queryDTO);

    /**
     * 获取车主浏览记录中所有货源的ID
     * @param userId
     * @param startDate
     * @return
     */
    List<Long> getRecentViewAllSrcMsgId(Long userId, Date startDate);

    /**
     * 获取最近浏览记录数量
     * @param userId
     * @param today
     * @return
     */
    Integer getRecentCountByUserId(Long userId, Date startDate);
}
