package com.teyuntong.goods.search.service.common.enums;

import lombok.Getter;

/**
 * 车枚举类
 *
 * <AUTHOR>
 * @since 2024/05/29 13:45
 */
@Getter
public enum ExcellentGoodsEnum {
    NORMAL_GOODS(0, "普通货源"),
    EXCELLENT_CAR_GOODS(1, "优车货源"),
    SPECIAL_CAR_GOODS(2, "专车货源");

    private Integer code;
    private String desc;

    ExcellentGoodsEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}