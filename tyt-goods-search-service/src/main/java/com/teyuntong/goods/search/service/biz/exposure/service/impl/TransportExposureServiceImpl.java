package com.teyuntong.goods.search.service.biz.exposure.service.impl;

import com.teyuntong.goods.search.service.biz.exposure.mapper.TransportExposureResultMapper;
import com.teyuntong.goods.search.service.biz.exposure.service.TransportExposureService;
import com.teyuntong.goods.search.service.biz.exposure.vo.ExposureVO;
import com.teyuntong.goods.search.service.biz.goods.dto.BaseTransportSearchDTO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.goods.mapper.TransportMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 急走专区曝光类
 *
 * <AUTHOR>
 * @since 2024/07/16 10:26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportExposureServiceImpl implements TransportExposureService {

    private final TransportMapper transportMapper;
    private final TransportExposureResultMapper transportExposureResultMapper;

    /**
     * 获取急走专区货源列表
     */
    @Override
    public List<TransportDO> getExposureList(BaseTransportSearchDTO searchDTO) {
        return transportMapper.getExposureList(searchDTO);
    }

    /**
     * 保存货源曝光记录
     *
     * @param transportList 货源集合
     */
    @Async
    @Override
    public void saveExposureResult(List<ExposureVO> transportList) {
        if (CollectionUtils.isEmpty(transportList)) {
            return;
        }

        List<Long> srcMsgIdList = transportList.stream().map(ExposureVO::getSrcMsgId).collect(Collectors.toList());

        // 先update
        int updateCount = transportExposureResultMapper.updateExposureCount(srcMsgIdList);
        // 再insert
        if (updateCount != srcMsgIdList.size()) {
            transportExposureResultMapper.saveExposureResult(transportList);
        }
    }

    @Override
    public TransportDO getExposureOfScore(BaseTransportSearchDTO searchDTO) {
       return transportMapper.getExposureOfScore(searchDTO);
    }
}
