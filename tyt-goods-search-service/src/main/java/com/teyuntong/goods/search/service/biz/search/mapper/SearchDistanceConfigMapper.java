package com.teyuntong.goods.search.service.biz.search.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 搜索外扩距离配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@Mapper
@DS("tyt")
public interface SearchDistanceConfigMapper {

    /**
     * 返回搜索距离，优先取城市，不存在取省
     */
    Integer getOptimalDistance(@Param("province") String province, @Param("city") String city);
}
