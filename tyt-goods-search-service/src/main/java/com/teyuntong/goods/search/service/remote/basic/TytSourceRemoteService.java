package com.teyuntong.goods.search.service.remote.basic;

import com.teyuntong.infra.basic.resource.client.source.service.TytSourceRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * source测试远程调用
 *
 * <AUTHOR>
 * @since 2024/09/19 11:31
 */
@FeignClient(name = "tyt-infra-basic-resource", path = "basic-resource", contextId = "tytSourceRemoteService",
        fallbackFactory = TytSourceRemoteService.ConfigRemoteServiceFallback.class)
public interface TytSourceRemoteService extends TytSourceRpcService {

    @Component
    class ConfigRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<TytSourceRemoteService> {
        public ConfigRemoteServiceFallback() {
            super(true, TytSourceRemoteService.class);
        }
    }

}
