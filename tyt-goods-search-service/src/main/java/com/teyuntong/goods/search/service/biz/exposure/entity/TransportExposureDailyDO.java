package com.teyuntong.goods.search.service.biz.exposure.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 每日货源曝光池模板表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Getter
@Setter
@TableName("tyt_transport_exposure_daily")
public class TransportExposureDailyDO {

    private Long id;

    /**
     * 重发后原信息ID
     */
    private Long srcMsgId;

    /**
     * 曝光状态（1曝光，0不曝光）
     */
    private Integer status;

    /**
     * 是否有人拨打（0没有；1有）
     */
    private Integer hasCall;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 修改id(同步需要)
     */
    private Long changeId;
}
