package com.teyuntong.goods.search.service.biz.goods.dto.rule;

import lombok.Getter;
import lombok.Setter;

/**
 * 智能排序因子：质量分
 * 格式：{"price":[{"modelScore":5,"score":10},{"modelScore":15,"score":20}], "noprice":[{"modelScore":5,"score":10},{"modelScore":15,"score":20}]}
 * 分有价price和无价noprice，modelScore是质量分，有价质量分<=5得分10
 *
 * <AUTHOR>
 * @since 2024/8/29 15:59
 */
@Getter
@Setter
public class IntelligentSortRuleModel {
    /**
     * 质量分，保留一位小数
     */
    private Float modelScore;
    /**
     * 评分
     */
    private Integer score;

}
