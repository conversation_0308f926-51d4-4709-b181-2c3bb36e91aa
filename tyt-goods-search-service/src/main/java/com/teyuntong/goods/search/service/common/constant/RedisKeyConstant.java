package com.teyuntong.goods.search.service.common.constant;

/**
 * redis key常量
 *
 * <AUTHOR>
 * @since 2024/10/18 16:11
 */
public class RedisKeyConstant {

    /**
     * 急走专区开关redis key，后面拼接 userId
     */
    public final static String EXPOSURE_SWITCH_KEY = "goods:search:exposure:switch:";

    /**
     * 进急走列表展示秒抢还是急走redis key，后面拼接userId + carId
     */
    public final static String EXPOSURE_LIST_TYPE_KEY = "goods:search:exposure:list:";
    /**
     * 用户BI经分身份缓存key
     */
    public static final String USER_BI_IDENTITY_CACHE_KEY = "tyt:user:bi:id:type:";

    /**
     * 是否参与现金奖redis key，后面拼接 userId
     */
    public final static String JOIN_CASH_PRIZE_KEY = "goods:search:user:cash:";

    /**
     * 车主屏蔽货主缓存key
     */
    public static final String SHIELD_SHIPPER_KEY = "tyt:shielding:userId:";
    /**
     * 车主屏蔽货源缓存key
     */
    public static final String SHIELD_GOODS_KEY = "tyt:shielding:goods:";
    /**
     * 插入固定货源正在展示的货源缓存key前缀
     */
    public static final String FIXED_SHOWING_PREFIX = "tyt:fixed:showing:";
    /**
     * 插入固定货源已经展示的货源缓存key前缀
     */
    public static final String FIXED_SHOWN_PREFIX = "tyt:fixed:shown:";
    /**
     * 货主成交率缓存key
     */
    public static final String GOODS_USER_DEAL_RATE = "tyt:goods:user:deal:rate:";

    /**
     * 货源使用曝光卡有效期
     */
    public static final String GOODS_EXPOSURE_VALID = "tyt:goods:exposure:valid:";
    /**
     * 货源使用曝光卡有效期
     */
    public static final String ACTIVITY_CASH_PREFIX = "tyt:goods:activity:cash:";

    /**
     * 获取用户基本信息
     */
    public static final String USER_INFO_KEY = "tyt:user:info:";

    /**
     * 货源等级展示缓存key
     */
    public static final String GOODS_USER_LEVEL_SHOW_KEY = "tyt:goods:level:show:";

    /**
     * 常跑路线用户设置的常跑路线id
     */
    public static final String OFTEN_ROUTE_USER = "tyt:often:route:user:";

    /**
     * 常跑路线路线信息
     */
    public static final String OFTEN_ROUTE_ID = "tyt:often:route:id:";

    /**
     * 货源拨打次数缓存key
     */
    public static final String GOODS_CALL_COUNT_KEY = "tyt:goods:call:srcMsgId:";

    /**
     * 货源拨打次数缓存key
     */
    public static final String TRANSPORT_CALL_PHONE_RECORED_COUNT_HASH_KEY = "transportCallPhoneRecoredCount";

    /**
     * 统计发布中货源查看记录和沟通数,hash结构，后缀今天的日期,eg: plat:count:transports:2019-01-01
     */
    public static final String PLAT_COUNT_TRANSPORT_KEY = "plat:count:transports:";

    /**
     * 货源今日查看记录数,hash内数据，后缀货源ID,eg: view:count:5732821
     */
    public static final String VIEW_COUNT_HASH_KEY = "view:count:";

    /**
     * 货源今日沟通数,hash内数据，后缀货源ID,eg: contact:count:5732821
     */
    public static final String CONTACT_COUNT_HASH_KEY = "contact:count:";


    /**
     * 找货大厅多车找货气泡提醒key
     */
    public static final String HALL_CAR_BUBBLE = "goods:search:hall:car:bubble:";
}
