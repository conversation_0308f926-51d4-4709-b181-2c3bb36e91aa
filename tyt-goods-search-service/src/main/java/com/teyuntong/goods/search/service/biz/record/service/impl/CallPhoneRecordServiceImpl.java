package com.teyuntong.goods.search.service.biz.record.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teyuntong.goods.search.service.biz.record.entity.CallPhoneRecordDO;
import com.teyuntong.goods.search.service.biz.record.mapper.CallPhoneRecordMapper;
import com.teyuntong.goods.search.service.biz.record.service.CallPhoneRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 拨打电话记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CallPhoneRecordServiceImpl extends ServiceImpl<CallPhoneRecordMapper, CallPhoneRecordDO> implements CallPhoneRecordService {

}
