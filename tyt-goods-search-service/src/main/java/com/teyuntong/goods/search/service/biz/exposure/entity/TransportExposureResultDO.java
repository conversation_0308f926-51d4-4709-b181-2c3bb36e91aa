package com.teyuntong.goods.search.service.biz.exposure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 货源曝光次数结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Getter
@Setter
@TableName("tyt_transport_exposure_result")
public class TransportExposureResultDO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 发货人id
     */
    private Long userId;

    /**
     * 原始货源id
     */
    private Long srcMsgId;

    /**
     * 曝光次数
     */
    private Integer showCount;

    /**
     * 创建时间
     */
    private Date createTime;

    private Date modifyTime;
}
