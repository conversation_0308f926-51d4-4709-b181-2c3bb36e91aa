package com.teyuntong.goods.search.service.biz.goods.service.impl;

import com.teyuntong.goods.search.service.biz.goods.dto.TransportVaryDTO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportVaryDO;
import com.teyuntong.goods.search.service.biz.goods.mapper.TransportVaryMapper;
import com.teyuntong.goods.search.service.biz.goods.service.TransportVaryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 运输信息变动表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportVaryServiceImpl implements TransportVaryService {

    private final TransportVaryMapper transportVaryMapper;

    @Override
    public List<TransportVaryDO> searchHallVary(TransportVaryDTO transportVaryDTO) {
        return transportVaryMapper.searchHallVary(transportVaryDTO);

    }
}
