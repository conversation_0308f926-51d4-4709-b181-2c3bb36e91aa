package com.teyuntong.goods.search.service.biz.record.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teyuntong.goods.search.service.biz.record.entity.CallFeedbackLogDO;
import com.teyuntong.goods.search.service.biz.record.mapper.CallFeedbackLogMapper;
import com.teyuntong.goods.search.service.biz.record.service.CallFeedbackLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 拨打反馈页面填写记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CallFeedbackLogServiceImpl extends ServiceImpl<CallFeedbackLogMapper, CallFeedbackLogDO> implements CallFeedbackLogService {

    private final CallFeedbackLogMapper callFeedbackLogMapper;

    /**
     * 获取车主的货源反馈记录
     */
    @Override
    public String getLastFeedback(Long carUserId, Long srcMsgId) {
        List<CallFeedbackLogDO> lastFeedback = callFeedbackLogMapper.getLastFeedback(carUserId, List.of(srcMsgId));
        return lastFeedback.isEmpty() ? null : lastFeedback.get(0).getFeedback1();
    }

    /**
     * 批量获取车主的货源反馈记录
     *
     * @return {srcMsgId:"最新一条反馈记录"}
     */
    @Override
    public Map<Long, String> getLastFeedback(Long carUserId, List<Long> srcMsgIds) {
        if (carUserId == null || CollectionUtils.isEmpty(srcMsgIds)) {
            return Map.of();
        }
        List<CallFeedbackLogDO> lastFeedback = callFeedbackLogMapper.getLastFeedback(carUserId, srcMsgIds);
        return lastFeedback.stream().collect(Collectors.toMap(CallFeedbackLogDO::getSrcMsgId, CallFeedbackLogDO::getFeedback1, (a, b) -> a));
    }

    /**
     * 是否填写过反馈页面
     */
    @Override
    public Integer hasFeedback(Long carUserId, Long srcMsgId) {
        List<CallFeedbackLogDO> lastFeedback = callFeedbackLogMapper.getLastFeedback(carUserId, List.of(srcMsgId));
        return lastFeedback.isEmpty() ? 0 : 1;
    }
}
