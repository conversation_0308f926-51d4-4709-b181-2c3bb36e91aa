package com.teyuntong.goods.search.service.biz.goods.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * transport 相关表中 label_json 内部属性
 * 在命名规范的前提下，尽量缩短字段名称
 */
@Getter
@Setter
public class TransportLabelJsonVO {

    /**
     * 用户标签名称
     */
    private String goodService;

    /**
     * 用户标签id，0:无标签，1:正向（服务好），2:负向（客诉多）
     */
    private Integer userLabelIcon;

    /**
     * 重货标识 1重货
     */
    private Integer duplicateFlag;

    /**
     * 加价次数.
     */
    private Integer addPriceCount;

    /**
     * 秒抢货源 1：是
     */
    private Integer instantGrab;

    /**
     * 用户认证状态（1通过）
     */
    private Integer userAuthStatus;
    /**
     * 企业认证状态（1通过）
     */
    private Integer enterpriseAuthStatus;

    /**
     * 调用BI优车好货接口返回结果
     */
    private Integer iGBIResultData;

    /**
     * 调用BI优车好货接口返回结果的分数
     */
    private BigDecimal goodsModelScore;

    /**
     * 优车运价货源 1:是；null：否
     */
    private Integer goodCarPriceTransport;

    /**
     * 是否抽佣货源 1:是；0:否
     */
    private Integer commissionTransport;

    /**
     * 价格标签 :0.无标签 1.该货源价格高于历史成交均价
     */
    private Integer priceLabel;

    /**
     * 是否优车2.0 货源
     */
    public boolean isGoodCarPriceTransport() {
        return Objects.equals(goodCarPriceTransport, 1);
    }

    /**
     * 是否抽佣货源
     */
    public boolean isCommissionTransport() {
        return Objects.equals(commissionTransport, 1);
    }

}
