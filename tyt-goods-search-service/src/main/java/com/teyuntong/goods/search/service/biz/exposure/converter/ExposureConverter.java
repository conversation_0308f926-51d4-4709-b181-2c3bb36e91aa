package com.teyuntong.goods.search.service.biz.exposure.converter;

import com.teyuntong.goods.search.service.biz.exposure.vo.ExposureVO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.goods.es.entity.TransportEsDO;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/07/04 10:27
 */
@Mapper
public interface ExposureConverter {
    ExposureConverter INSTANCE = Mappers.getMapper(ExposureConverter.class);

    List<ExposureVO> convertDOs2VOs(List<TransportDO> transportDOS);

    List<ExposureVO> convertTsVOs2VOs(List<TransportVO> transportVOS);

    ExposureVO convertTsDO2VO(TransportDO transportDO);

    List<ExposureVO> convertEsDOs2VOs(List<TransportEsDO> transportEsDO);

    @Mapping(target = "pubDate", source = "ctime")
    ExposureVO transportEsDO2VO(TransportEsDO transportEsDO);
}
