package com.teyuntong.goods.search.service.biz.route.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 常跑路线查询DTO
 */
@Getter
@Setter
public class OftenRouteSearchDTO {

    /**
     * 用户所选路线id，不传默认全部启用路线
     */
    private String routeIds;

    /**
     * 货源是否有价格（空不限；0无价；1有价）
     */
    private Integer priceFlag;

    /**
     * 货源类型（电议1，一口价2）
     */
    private Integer publishType;

    /**
     * 订金是否退还（0不退还；1退还）
     */
    private Integer refundFlag;

    // ============================================================

    private Long userId;
    /**
     * 需要过滤的货主id
     */
    private List<Long> excludeUserIds;

    /**
     * 需要过滤的货源id
     */
    private List<Long> excludeSrcMsgIds;

    /**
     * 用户常跑路线
     */
    private List<UserOftenRouteDTO> userOftenRoutes;

    /**
     * 每页条数
     */
    private Integer pageSize = 30;

    /**
     * 找货大厅额外参数
     */
    private SearchRouteExtraDTO searchRouteExtra;

    public SearchRouteExtraDTO getSearchRouteExtra() {
        if (searchRouteExtra == null) {
            searchRouteExtra = new SearchRouteExtraDTO();
        }
        return searchRouteExtra;
    }
}
