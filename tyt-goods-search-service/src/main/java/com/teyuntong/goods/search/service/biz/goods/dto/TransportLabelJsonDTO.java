package com.teyuntong.goods.search.service.biz.goods.dto;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.teyuntong.trade.service.client.feedBack.dto.UserFeedbackRatingAndLabelDTO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * transport 相关表中 label_json 内部属性
 * 在命名规范的前提下，尽量缩短字段名称
 *
 * <AUTHOR>
 * @date 2023/1/29 13:54
 */
@Data
public class TransportLabelJsonDTO {

    /**
     * 用户标签名称
     */
    private String goodService;

    /**
     * 用户标签id，0:无标签，1:正向（服务好），2:负向（客诉多）
     */
    private Integer userLabelIcon;
    /**
     * 是否重复货源 0:否，1:是
     */
    private Integer duplicateFlag;

    /**
     * 加价次数.
     */
    private Integer addPriceCount;

    private UserFeedbackRatingAndLabelDTO feedbackLabel;
    /**
     * 秒抢货源 1：是
     */
    private Integer instantGrab;

    /**
     * 用户认证状态（1通过）
     */
    private Integer userAuthStatus;
    /**
     * 企业认证状态（1通过）
     */
    private Integer enterpriseAuthStatus;

    /**
     * 调用BI优车好货接口返回结果
     */
    private Integer iGBIResultData;

    /**
     * 调用BI优车好货接口返回结果的分数
     */
    private BigDecimal goodsModelScore;

    /**
     * 优车运价货源 1:是；null：否
     */
    private Integer goodCarPriceTransport;

    /**
     * 是否抽佣货源 1:是；0:否
     */
    private Integer commissionTransport;


    @JsonIgnore
    public String getJsonText() {
        String jsonText = JSON.toJSONString(this);

        if (jsonText.equals("{}")) {
            jsonText = "";
        }
        return jsonText;
    }


    public static void main(String[] args) {

        TransportLabelJsonDTO labelJson = new TransportLabelJsonDTO();

        String jsonText = labelJson.getJsonText();

        System.out.println(jsonText);

    }
}
