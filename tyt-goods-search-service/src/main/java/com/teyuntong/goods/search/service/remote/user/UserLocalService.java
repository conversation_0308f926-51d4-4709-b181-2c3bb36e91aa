package com.teyuntong.goods.search.service.remote.user;

import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.search.service.common.constant.RedisKeyConstant;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.user.service.client.user.vo.DwsNewIdentiwoDataRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * <AUTHOR>
 * @since 2024/10/17 11:34
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserLocalService {

    private final UserRemoteService userRemoteService;
    private final RedisUtil redisUtil;

    /**
     * 获取用户BI经分身份，缓存1h
     */
    public Integer getUserBiIdentityType(Long userId) {
        try {
            String cacheK = RedisKeyConstant.USER_BI_IDENTITY_CACHE_KEY + userId;
            Integer userType = redisUtil.getInt(cacheK);
            if (userType == null) {
                DwsNewIdentiwoDataRpcVO userIdentity = userRemoteService.getDwsNewIdentiwoDataByUserId(userId);
                log.info("获取用户身份信息，userId:{},返回信息：{}", userId, JSON.toJSONString(userIdentity));
                if (userIdentity != null) {
                    userType = userIdentity.getType();
                    redisUtil.set(cacheK, userType, Duration.ofHours(1));
                }
            }
            return userType;
        } catch (Exception e) {
            log.error("获取用户身份信息失败，userId:{}", userId, e);
        }
        return null;
    }

    /**
     * 获取用户基本信息
     */
    public UserRpcVO getUserById(Long userId) {
        try {
            String userKey = RedisKeyConstant.USER_INFO_KEY + userId;
            UserRpcVO user = redisUtil.getBean(userKey, UserRpcVO.class);
            if (user == null) {
                user = userRemoteService.getUser(userId);
                if (user != null) {
                    redisUtil.set(userKey, user, Duration.ofHours(1));
                }
            }
            return user;
        } catch (Exception e) {
            log.error("获取用户信息失败，userId:{}", userId, e);
        }
        return null;
    }


}
