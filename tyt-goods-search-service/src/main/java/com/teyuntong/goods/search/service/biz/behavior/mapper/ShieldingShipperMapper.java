package com.teyuntong.goods.search.service.biz.behavior.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.behavior.entity.ShieldingShipperDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 屏蔽发货人 mapper
 *
 * <AUTHOR>
 * @since 2024/07/03 16:58
 */
@Mapper
@DS("tyt")
public interface ShieldingShipperMapper extends BaseMapper<ShieldingShipperDO> {

    /**
     * 查询车主屏蔽的货主id列表
     */
    List<Long> getShieldingUserIdList(Long userId);

    /**
     * 逻辑删除数据
     */
    void deleteData(@Param("userId") Long userId, @Param("shieldingUserId") Long shieldingUserId);
}
