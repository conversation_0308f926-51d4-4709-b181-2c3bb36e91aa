package com.teyuntong.goods.search.service.common.util;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/8/1 11:43
 */
public class MapPointUtils {



    /**
     * 经纬度转换
     *
     * @param pointNumber
     * @return
     */
    public static String toMapPointStr(Integer pointNumber) {

        if (pointNumber == null) {
            return "";
        }
        if (pointNumber.toString().length() > 6) {
            return new BigDecimal(pointNumber).movePointLeft(6).toString();
        } else {
            return new BigDecimal(pointNumber).movePointLeft(2).toString();
        }

    }

    /**
     * 坐标系转换
     *
     * @param coordNumber
     * @return
     */
    public static String toCoordStr(Integer coordNumber) {
        if (coordNumber == null) return "";
        return new BigDecimal(coordNumber).movePointLeft(2).toString();
    }


}
