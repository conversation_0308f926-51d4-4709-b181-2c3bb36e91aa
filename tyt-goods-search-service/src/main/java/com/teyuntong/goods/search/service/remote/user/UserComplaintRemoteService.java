package com.teyuntong.goods.search.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.custom.service.CsComplaintRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 用户投诉service
 *
 * <AUTHOR>
 * @since 2024/07/17 16:43
 */
@FeignClient(name = "tyt-user-service", path = "user", contextId = "userComplaintRemoteService",
        fallbackFactory = UserComplaintRemoteService.UserComplaintRemoteServiceFallback.class)
public interface UserComplaintRemoteService extends CsComplaintRpcService {

    @Component
    class UserComplaintRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<UserComplaintRemoteService> {
        public UserComplaintRemoteServiceFallback() {
            super(true, UserComplaintRemoteService.class);
        }
    }
}
