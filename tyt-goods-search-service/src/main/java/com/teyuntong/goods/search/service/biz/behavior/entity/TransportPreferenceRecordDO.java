package com.teyuntong.goods.search.service.biz.behavior.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 货源用户偏好记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Getter
@Setter
@TableName("transport_preference_record")
public class TransportPreferenceRecordDO {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 货源id
     */
    private Long srcMsgId;

    /**
     * 状态  0：不喜欢  1：喜欢
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;
}
