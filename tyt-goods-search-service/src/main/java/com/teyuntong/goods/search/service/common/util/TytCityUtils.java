package com.teyuntong.goods.search.service.common.util;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @description: 省市区工具
 * @date 2021/11/17 10:39
 */
public class TytCityUtils {

    /**
     * 直辖市
     **/
    public final static Set<String> MUNICIPALITY_SET = new HashSet<>();

    static {
        MUNICIPALITY_SET.add("北京市");
        MUNICIPALITY_SET.add("天津市");
        MUNICIPALITY_SET.add("上海市");
        MUNICIPALITY_SET.add("重庆市");
    }

    /**
     * 是否是直辖市
     */
    public static boolean isMunicipality(String city) {
        return MUNICIPALITY_SET.contains(city);
    }

    /**
     * 拼接地址信息，不带省
     */
    public static String createAddressInfo(String city, String area) {
        return createAddressInfo("", city, area);
    }

    /**
     * 拼接地址信息，不带省
     */
    public static String createAddressInfo(String province, String city, String area) {
        StringBuilder builder = new StringBuilder();

        if (province == null) {
            province = "";
        }

        if (TytCityUtils.isMunicipality(city)) {
            //直辖市去掉“市”字
            province = "";
            city = city.substring(0, city.length() - 1);
        }
        builder.append(province);
        builder.append(city);
        builder.append(area);
        return builder.toString();
    }

    /**
     * 经纬度
     */
    public static String toMapPointStr(Integer pointNumber) {
        if (pointNumber == null) {
            return "";
        }
        if (pointNumber.toString().length() > 6) {
            return new BigDecimal(pointNumber).movePointLeft(6).toString();
        } else {
            return new BigDecimal(pointNumber).movePointLeft(2).toString();
        }

    }

    public static String toMapPointStr(String pointNumber) {
        if (StringUtils.isBlank(pointNumber)) {
            return "";
        }
        if (pointNumber.length() > 6) {
            return new BigDecimal(pointNumber).movePointLeft(6).toString();
        } else {
            return new BigDecimal(pointNumber).movePointLeft(2).toString();
        }

    }

    /**
     * 坐标系转换
     */
    public static String toCoordStr(Integer coordNumber) {
        if (coordNumber == null) {
            return "";
        }
        return new BigDecimal(coordNumber).movePointLeft(2).toString();
    }

}
