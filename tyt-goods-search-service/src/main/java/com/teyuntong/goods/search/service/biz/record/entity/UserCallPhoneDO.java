package com.teyuntong.goods.search.service.biz.record.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 用户获取电话表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Getter
@Setter
@TableName("tyt_user_call_phone")
public class UserCallPhoneDO {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 运输信息ID
     */
    private Long tsId;

    /**
     * 拨打时间
     */
    private Date createTime;

    /**
     * 版本号
     */
    private String clientVersion;

    /**
     * 客户端标识 1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB
     */
    private Integer clientSign;

    /**
     * 1精准货源模块，2 货源列表模块 3 货源详情模块 4 板车购买模块 5 板车司机招聘模块 6 板车维修模块 7 板车配件模块 8 机械设备维修模块 9 机械设备配件模块 10 维修师模块 11 机械司机招聘模块 12 机械司机求职模块
     */
    private Integer muduleType;

    /**
     * 1 获取电话 2 获取电话成功 3 获取电话失败 4 拨打电话
     */
    private Integer actionType;

    /**
     * 路径
     */
    private String path;
}
