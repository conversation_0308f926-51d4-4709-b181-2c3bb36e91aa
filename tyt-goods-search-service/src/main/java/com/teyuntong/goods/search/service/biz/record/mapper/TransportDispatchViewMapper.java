package com.teyuntong.goods.search.service.biz.record.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.record.entity.TransportDispatchViewDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 货源查看、联系统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@Mapper
@DS("tyt")
public interface TransportDispatchViewMapper extends BaseMapper<TransportDispatchViewDO> {
    /**
     * 根据用户id和货源id查询
     *
     * @param userId
     * @param srcMsgId
     * @return
     */
    TransportDispatchViewDO getByUserAndSrcMsgId(@Param("userId") Long userId, @Param("srcMsgId") Long srcMsgId);
}
