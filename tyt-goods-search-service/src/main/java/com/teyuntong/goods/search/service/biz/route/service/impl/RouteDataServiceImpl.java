package com.teyuntong.goods.search.service.biz.route.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.teyuntong.goods.search.service.biz.behavior.serivce.ShieldingShipperService;
import com.teyuntong.goods.search.service.biz.behavior.serivce.TransportShieldService;
import com.teyuntong.goods.search.service.biz.goods.converter.TransportConverter;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.goods.es.entity.TransportEsDO;
import com.teyuntong.goods.search.service.biz.goods.es.service.EsTransportService;
import com.teyuntong.goods.search.service.biz.goods.service.TransportService;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.goods.search.service.biz.route.constant.RouteConstant;
import com.teyuntong.goods.search.service.biz.route.converter.RouteConverter;
import com.teyuntong.goods.search.service.biz.route.dto.OftenRouteSearchDTO;
import com.teyuntong.goods.search.service.biz.route.dto.SearchRouteExtraDTO;
import com.teyuntong.goods.search.service.biz.route.dto.UserOftenRouteDTO;
import com.teyuntong.goods.search.service.biz.route.entity.OftenDriveRouteAddressDO;
import com.teyuntong.goods.search.service.biz.route.entity.OftenDriveRouteDO;
import com.teyuntong.goods.search.service.biz.route.mapper.OftenDriveRouteAddressMapper;
import com.teyuntong.goods.search.service.biz.route.mapper.OftenDriveRouteMapper;
import com.teyuntong.goods.search.service.biz.route.mapper.OftenDriveRouteUserMapper;
import com.teyuntong.goods.search.service.biz.route.service.RouteDataService;
import com.teyuntong.goods.search.service.biz.route.vo.OftenRouteBubbleVO;
import com.teyuntong.goods.search.service.biz.route.vo.OftenRouteSearchVO;
import com.teyuntong.goods.search.service.biz.route.vo.RouteTransportCountVo;
import com.teyuntong.goods.search.service.biz.route.vo.RouteTransportQuery;
import com.teyuntong.goods.search.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.search.service.common.enums.EsOrDbEnum;
import com.teyuntong.goods.search.service.remote.basic.ConfigRemoteService;
import com.teyuntong.goods.search.service.common.config.OftenRouteRedisTemplate;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 常跑路线总开关 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RouteDataServiceImpl implements RouteDataService {

    private final OftenRouteRedisTemplate oftenRouteRedisTemplate;
    private final TransportService transportService;
    private final ShieldingShipperService shieldingShipperService;
    private final TransportShieldService transportShieldService;
    private final ConfigRemoteService configRemoteService;
    private final EsTransportService esTransportService;

    private final OftenDriveRouteMapper oftenDriveRouteMapper;
    private final OftenDriveRouteUserMapper oftenDriveRouteUserMapper;
    private final OftenDriveRouteAddressMapper oftenDriveRouteAddressMapper;


    @Override
    public List<TransportVO> getTransportByIds(List<Long> transportIdList) {
        if (CollectionUtils.isEmpty(transportIdList)) {
            return List.of();
        }

        List<TransportDO> transportDOList = transportService.selectByTsIds(transportIdList);
        List<TransportVO> transportVOS = TransportConverter.INSTANCE.convertDOs2VOs(transportDOList);

        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        transportService.handleTransportTag(transportVOS, userId);
        transportService.hideSensitiveInfo(transportVOS, userId);
        return transportVOS;
    }


    @Override
    public List<TransportVO> queryRedisTransportIdList(Date nowTime, RouteTransportQuery transportQuery) {
        List<TransportVO> resultList = new ArrayList<>();

        Long routeId = transportQuery.getRouteId();
        Integer queryType = transportQuery.getQueryType();
        Long querySign = transportQuery.getQuerySign();
        Integer pageSize = transportQuery.getPageSize();

        String routeGoodsKey = RouteConstant.getRouteGoodsKey(nowTime, routeId);

        // queryType : 1向下拉；2向上滑
        // querySign 跟 index 一样

        long startIndex = 0;
        long stopIndex = 0;

        if (querySign == null) {
            querySign = -1L;
        }

        if (queryType < 2) {
            //第一次加载，或者向下刷新时
            //加载最新的第一页数据
            long maxIndex = oftenRouteRedisTemplate.opsForZSet().zCard(routeGoodsKey);
            stopIndex = maxIndex - 1;

            startIndex = stopIndex - pageSize;

            if (querySign > startIndex) {
                startIndex = querySign;
            }
            startIndex = startIndex + 1;
        } else {
            //2向上滑
            if (querySign.compareTo(0L) == 0) {
                //最后一页
                return resultList;
            }
            stopIndex = querySign - 1;
            startIndex = stopIndex - pageSize + 1;//不能包含最后一个
        }

        if (startIndex < 0) {
            startIndex = 0;
        }
        if (stopIndex < 0) {
            stopIndex = 0;
        }

        //分数值递增(从小到大)
        Set<ZSetOperations.TypedTuple<Object>> valueSet = oftenRouteRedisTemplate.opsForZSet().rangeWithScores(routeGoodsKey, startIndex, stopIndex);

        if (CollectionUtils.isNotEmpty(valueSet)) {
            long index = startIndex;
            for (ZSetOperations.TypedTuple oneValue : valueSet) {
                Double score = oneValue.getScore();
                Long transportId = score.longValue();

                TransportVO transportRouteVo = new TransportVO();
                transportRouteVo.setIndex(index);
                transportRouteVo.setId(transportId);

                resultList.add(transportRouteVo);
                index++;
            }

            resultList.sort((o1, o2) -> {
                Long i1 = o1.getIndex();
                Long i2 = o2.getIndex();
                return i2.compareTo(i1);
            });
        }

        return resultList;
    }

    @Override
    public List<TransportVO> getRouteTransportList(List<TransportVO> redisIndexList) {
        // 2025-02-14修改，刷新不再走常跑路线，缓存中的tsId可能是已失效的，需要根据tsId找到srcMsgId，再查询发布中的货源
        Map<Long, Long> idsMap = redisIndexList.stream().collect(Collectors.toMap(
                TransportVO::getId, TransportVO::getIndex, (t1, t2) -> t2));
        // 查询tsId对应的srcMsgId
        List<TransportDO> srcMsgIdList = transportService.selectSrcMsgIdByTsIds(idsMap.keySet().stream().toList());
        if (CollectionUtils.isEmpty(srcMsgIdList)) { // transport表切换，可能数据为空
            return List.of();
        }
        // 映射：srcMsgId -> old tsId
        Map<Long, Long> srcMsgIdMap = srcMsgIdList.stream().collect(
                Collectors.toMap(TransportDO::getSrcMsgId, TransportDO::getId, (t1, t2) -> t1));
        // 根据srcMsgId查询发布中的货源
        List<TransportDO> transportDOList = transportService.selectBySrcMsgIdsAndStatus(srcMsgIdMap.keySet().stream().toList());
        List<TransportVO> transportVOList = TransportConverter.INSTANCE.convertDOs2VOs(transportDOList);
        // 映射：new tsId -> srcMsgId
        Map<Long, Long> tsIdMap = transportDOList.stream().collect(
                Collectors.toMap(TransportDO::getId, TransportDO::getSrcMsgId, (t1, t2) -> t1));
        for (TransportVO transportVO : transportVOList) {
            transportVO.setIndex(idsMap.get(srcMsgIdMap.get(tsIdMap.get(transportVO.getId()))));
        }
        return transportVOList;
    }

    @Override
    public List<TransportVO> getRouteTransportList(RouteDataService routeDataService, RouteTransportQuery transportQuery) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return List.of();
        }
        Long userId = loginUser.getUserId();

        Integer queryType = transportQuery.getQueryType();
        Integer pageSize = transportQuery.getPageSize();
        Long querySign = transportQuery.getQuerySign();

        Date nowTime = new Date();

        List<TransportVO> transportVOList = new ArrayList<>();

        //预先多取，防止部分货源失效，查询数量不够
        transportQuery.setPageSize(pageSize * 2);
        List<Long> shieldingUserList = shieldingShipperService.getShieldingUserList(userId);
        List<Long> shieldSrcMsgIds = transportShieldService.getShieldSrcMsgIds(userId);

        //最多查询3次，防止redis有id，但是数据库查询货源全部失效情况
        for (int i = 0; i < 3; i++) {

            List<TransportVO> redisIndexList = this.queryRedisTransportIdList(nowTime, transportQuery);

            if (CollectionUtils.isEmpty(redisIndexList)) {
                //不需要进行下次查询了
                break;
            }

            Long minIndex = redisIndexList.get(redisIndexList.size() - 1).getIndex();

            transportVOList = routeDataService.getRouteTransportList(redisIndexList);

            transportVOList = transportService.filterPublishUser(shieldingUserList, shieldSrcMsgIds, transportVOList, pageSize);

            if (CollectionUtils.isNotEmpty(transportVOList)) {
                // 有数据，直接返回
                break;
            }

            if (queryType == 0) {
                // 自动刷新，不需要重复查询
                break;
            } else if (queryType == 1 && querySign != null && querySign >= 0) {
                //向下拉，不需要重复查询
                break;
            } else {
                //向下刷新，或第一次加载情况
                //如果是第一次加载没数据，需改成向下刷新
                transportQuery.setQueryType(2);
                //上划
                if (minIndex <= 0) {
                    log.info("transportIndexList_end_of_zero!");
                    break;
                }

                transportQuery.setQuerySign(minIndex);
                log.info("getRouteTransportList_db_null, try_next_page : {}", minIndex);
            }
        }

        if (CollectionUtils.isNotEmpty(transportVOList)) {
            if (transportVOList.size() > pageSize) {
                transportVOList = transportVOList.subList(0, pageSize);
            }

            transportService.handleTransportTag(transportVOList, userId);
            transportService.hideSensitiveInfo(transportVOList, userId);
        }
        return transportVOList;
    }

    @Override
    public RouteTransportCountVo getOneRouteCount(Date nowTime, Long routeId) {
        String countKey = RouteConstant.getRouteCountKey(nowTime, routeId);
        Integer count = (Integer) oftenRouteRedisTemplate.opsForValue().get(countKey);

        RouteTransportCountVo transportCountVo = new RouteTransportCountVo();
        transportCountVo.setRouteId(routeId);
        transportCountVo.setCount(count == null ? 0 : count);
        return transportCountVo;
    }

    @Override
    public List<RouteTransportCountVo> getRouteTransportCount(List<Long> routeIdList) {
        List<RouteTransportCountVo> tsCountList = new ArrayList<>();
        Date nowTime = new Date();

        for (Long oneRouteId : routeIdList) {
            tsCountList.add(this.getOneRouteCount(nowTime, oneRouteId));
        }
        return tsCountList;
    }

    /**
     * 常跑路线气泡数量接口
     */
    @Override
    public OftenRouteBubbleVO bubbleCount(OftenRouteSearchDTO searchDTO) {
        OftenRouteBubbleVO oftenRouteBubbleVO = new OftenRouteBubbleVO();
        // 查询常跑路线前置处理
        if (preSearchHandle(searchDTO)) {
            oftenRouteBubbleVO.setBubbleCount(esTransportService.oftenRouteBubbleCount(searchDTO));
        }
        oftenRouteBubbleVO.setSearchRouteExtra(searchDTO.getSearchRouteExtra());
        return oftenRouteBubbleVO;
    }

    /**
     * 常跑路线找货大厅接口
     */
    @Override
    public OftenRouteSearchVO hallList(OftenRouteSearchDTO searchDTO) {

        OftenRouteSearchVO oftenRouteSearchVO = new OftenRouteSearchVO();
        // 查询常跑路线前置处理
        if (!preSearchHandle(searchDTO)) {
            return oftenRouteSearchVO;
        }

        List<TransportEsDO> transportEsDOList = esTransportService.oftenRouteHallList(searchDTO);

        if (CollUtil.isNotEmpty(transportEsDOList)) {
            List<TransportVO> transportVOList = TransportConverter.INSTANCE.convertEsDOs2VOs(transportEsDOList);

            oftenRouteSearchVO.setHasNext(transportVOList.size() >= searchDTO.getPageSize());
            oftenRouteSearchVO.setResponseSize(transportVOList.size());
            oftenRouteSearchVO.setSearchRouteExtra(searchDTO.getSearchRouteExtra());

            //如果是相似货源，打上标识，客户端是否展示该相似货源
            transportService.handleSimilarTransport(transportVOList, searchDTO.getUserId());
            // 移除不展示的相似货源
            transportVOList.removeIf(transportVO -> transportVO.getShowSimilarGoods() == 0);
            // 对货源进行打标
            transportService.handleTransportTag(transportVOList, searchDTO.getUserId());
            // 加密货源字段
            transportService.hideSensitiveInfo(transportVOList, searchDTO.getUserId());

            oftenRouteSearchVO.setList(transportVOList);
        }
        return oftenRouteSearchVO;
    }


    /**
     * 查询常跑路线大厅前置处理
     *
     * @return false直接返回，true继续走
     */
    private boolean preSearchHandle(OftenRouteSearchDTO searchDTO) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return false;
        }
        searchDTO.setUserId(loginUser.getUserId());

        // 如果走db，直接返回
        EsOrDbEnum esOrDbEnum = transportService.getEsDbEnum(searchDTO.getUserId());
        if (esOrDbEnum == EsOrDbEnum.DB) {
            return false;
        }

        // 预设extra参数
        SearchRouteExtraDTO searchRouteExtra = searchDTO.getSearchRouteExtra();
        if (searchRouteExtra.getFirstSearchTime() == null) {
            searchRouteExtra.setFirstSearchTime(System.currentTimeMillis());
        }

        // 屏蔽货主
        searchDTO.setExcludeUserIds(shieldingShipperService.getShieldingUserList(searchDTO.getUserId()));
        // 屏蔽货源
        searchDTO.setExcludeSrcMsgIds(transportShieldService.getShieldSrcMsgIds(searchDTO.getUserId()));

        // 查询用户开启的路线id
        List<Long> routeIdList = getUserRouteIdList(searchDTO);
        if (routeIdList.isEmpty() || routeIdList.contains(0L)) {
            return false;
        }

        // 根据路线id查询路线信息
        List<UserOftenRouteDTO> userOftenRoutes = getUserRouteInfoList(routeIdList);
        if (CollUtil.isEmpty(userOftenRoutes)) {
            return false;
        }
        searchDTO.setUserOftenRoutes(userOftenRoutes);

        return true;
    }

    /**
     * 返回用户启用路线
     */
    private List<Long> getUserRouteIdList(OftenRouteSearchDTO searchDTO) {
        if (StringUtils.isNotBlank(searchDTO.getRouteIds())) {
            return Arrays.stream(searchDTO.getRouteIds().split(",")).map(Long::parseLong).toList();
        } else {
            Long userId = searchDTO.getUserId();
            List<Object> range = oftenRouteRedisTemplate.opsForList().range(RedisKeyConstant.OFTEN_ROUTE_USER + userId, 0, -1);
            List<Long> routeIdList;
            if (CollUtil.isEmpty(range)) {
                routeIdList = oftenDriveRouteUserMapper.getRouteIdOfEnable(userId);
                if (CollUtil.isEmpty(routeIdList)) {
                    routeIdList.add(0L); // 都没有路线，默认给一个0
                }
                oftenRouteRedisTemplate.opsForList().rightPushAll(RedisKeyConstant.OFTEN_ROUTE_USER + userId, routeIdList.toArray());
                oftenRouteRedisTemplate.expire(RedisKeyConstant.OFTEN_ROUTE_USER + userId, Duration.ofMinutes(10));
            } else {
                routeIdList = range.stream().map(Object::toString).map(Long::parseLong).toList();
            }
            return routeIdList;
        }
    }

    /**
     * 根据路线id查询路线信息
     */
    private List<UserOftenRouteDTO> getUserRouteInfoList(List<Long> routeIdList) {
        List<String> routeKeyList = routeIdList.stream().map(t -> RedisKeyConstant.OFTEN_ROUTE_ID + t).toList();
        List<Object> objects = oftenRouteRedisTemplate.opsForValue().multiGet(routeKeyList);
        Objects.requireNonNull(objects);

        // 如果路线都不为空，则直接返回
        if (objects.stream().allMatch(Objects::nonNull)) {
            return objects.stream().map(t -> (UserOftenRouteDTO) t).toList();
        } else {
            // 有空的缓存，从数据库查
            List<OftenDriveRouteDO> routeList = oftenDriveRouteMapper.selectBatchIds(routeIdList);
            if (routeList.isEmpty()) {
                return Collections.emptyList();
            }

            List<UserOftenRouteDTO> userOftenRoutes = new ArrayList<>();

            List<OftenDriveRouteAddressDO> addressList = oftenDriveRouteAddressMapper.getByRouteIds(routeIdList);
            for (OftenDriveRouteDO routeDO : routeList) {
                UserOftenRouteDTO userOftenRouteDTO = new UserOftenRouteDTO();
                userOftenRouteDTO.setMinW(routeDO.getMinWeight());
                userOftenRouteDTO.setMaxW(routeDO.getMaxWeight());

                userOftenRouteDTO.setStartAddr(addressList.stream()
                        .filter(address -> address.getRouteId().equals(routeDO.getId()))
                        .filter(address -> address.getType() == 1)
                        .map(RouteConverter.INSTANCE::buildAddressDTO).toList());
                userOftenRouteDTO.setDestAddr(addressList.stream()
                        .filter(address -> address.getRouteId().equals(routeDO.getId()))
                        .filter(address -> address.getType() == 2)
                        .map(RouteConverter.INSTANCE::buildAddressDTO).toList());
                userOftenRoutes.add(userOftenRouteDTO);

                // 设置缓存
                oftenRouteRedisTemplate.opsForValue().set(
                        RedisKeyConstant.OFTEN_ROUTE_ID + routeDO.getId(), userOftenRouteDTO, Duration.ofDays(1));
            }

            return userOftenRoutes;
        }
    }

}
