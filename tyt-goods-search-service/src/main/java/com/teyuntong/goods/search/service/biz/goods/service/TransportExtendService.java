package com.teyuntong.goods.search.service.biz.goods.service;

import com.teyuntong.goods.search.service.biz.goods.entity.TransportExtendDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/07/15 17:14
 */
public interface TransportExtendService {

    /**
     * 返回tsId对应的map
     */
    Map<Long, TransportExtendDO> getMapByTsIds(List<Long> tsIds);

    /**
     * 根据tsId查询
     */
    List<TransportExtendDO> getByTsIds(List<Long> tsIds);

    /**
     * 根据tsId查询
     */
    TransportExtendDO getByTsId(Long tsId);
}
