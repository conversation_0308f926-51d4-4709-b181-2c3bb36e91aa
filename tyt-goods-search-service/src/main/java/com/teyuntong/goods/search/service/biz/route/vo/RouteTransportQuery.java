package com.teyuntong.goods.search.service.biz.route.vo;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description: 路线查询
 * @date 2022/1/4 15:02
 */
@Getter
@Setter
public class RouteTransportQuery {
    /**
     * 路线id
     */
    @NotNull(message = "路线id不能为空")
    private Long routeId;

    /**
     * 加载方向（0加载新增；1默认进来；2向上滑）
     */
    @NotNull(message = "查询类型不能为空")
    private Integer queryType;

    /**
     * index条件(默认传null 或 -1，因为数组下标从0开始)
     */
    private Long querySign;

    /**
     * 每页显示条数，默认30条
     */
    private Integer pageSize = 30;

}
