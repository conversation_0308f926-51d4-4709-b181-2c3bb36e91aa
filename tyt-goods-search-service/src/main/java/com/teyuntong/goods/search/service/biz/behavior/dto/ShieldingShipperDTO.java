package com.teyuntong.goods.search.service.biz.behavior.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * 屏蔽货主
 *
 * <AUTHOR>
 * @since 2024/12/14 18:02
 */
@Getter
@Setter
public class ShieldingShipperDTO {

    /**
     * 当前用户id
     */
    private Long userId;

    /**
     * 屏蔽的货主
     */
    @NotNull(message = "屏蔽货主不能为空")
    private Long shieldingUserId;

    /**
     * 类型：1屏蔽；2解除屏蔽
     */
    @NotNull(message = "屏蔽类型不能为空")
    private Integer type;

    /**
     * 货源id
     */
    // @NotNull(message = "货源id不能为空")
    private Long srcMsgId;

}
