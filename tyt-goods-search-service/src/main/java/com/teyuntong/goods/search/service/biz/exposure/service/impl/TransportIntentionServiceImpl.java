package com.teyuntong.goods.search.service.biz.exposure.service.impl;

import com.teyuntong.goods.search.service.biz.exposure.service.TransportIntentionService;
import com.teyuntong.goods.search.service.biz.goods.dto.BaseTransportSearchDTO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.goods.mapper.TransportMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 急走专区曝光类
 *
 * <AUTHOR>
 * @since 2024/07/16 10:26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportIntentionServiceImpl implements TransportIntentionService {

    private final TransportMapper transportMapper;


    /**
     * 获取意向货源池列表
     */
    @Override
    public List<TransportDO> getIntentionList(BaseTransportSearchDTO searchDTO) {
        return transportMapper.getIntentionList(searchDTO);
    }
}
