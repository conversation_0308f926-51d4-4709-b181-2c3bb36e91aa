package com.teyuntong.goods.search.service.biz.exposure.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.exposure.entity.TransportExposureDailyDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 每日货源曝光池模板表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Mapper
@DS("good_search")
public interface TransportExposureDailyMapper extends BaseMapper<TransportExposureDailyDO> {

}
