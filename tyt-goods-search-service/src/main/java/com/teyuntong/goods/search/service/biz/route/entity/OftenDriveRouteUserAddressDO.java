package com.teyuntong.goods.search.service.biz.route.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户路线地址冗余表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Getter
@Setter
@TableName("often_drive_route_user_address")
public class OftenDriveRouteUserAddressDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户路线主键id
     */
    private Long routeUserId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 路线id
     */
    private Long routeId;

    /**
     * 省
     */
    private String provinc;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 1出发地；2目的地
     */
    private Integer type;
}
