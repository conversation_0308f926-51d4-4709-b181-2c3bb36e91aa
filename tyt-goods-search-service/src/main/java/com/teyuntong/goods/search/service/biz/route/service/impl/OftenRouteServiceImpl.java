package com.teyuntong.goods.search.service.biz.route.service.impl;

import cn.hutool.core.net.URLDecoder;
import com.alibaba.fastjson.JSON;
import com.teyuntong.goods.search.service.biz.route.constant.RouteConstant;
import com.teyuntong.goods.search.service.biz.route.converter.RouteConverter;
import com.teyuntong.goods.search.service.biz.route.entity.*;
import com.teyuntong.goods.search.service.biz.route.enums.RouteAddressTypeEnum;
import com.teyuntong.goods.search.service.biz.route.enums.RouteReportStatusEnum;
import com.teyuntong.goods.search.service.biz.route.mapper.*;
import com.teyuntong.goods.search.service.biz.route.service.OftenRouteService;
import com.teyuntong.goods.search.service.biz.route.service.RouteDataService;
import com.teyuntong.goods.search.service.biz.route.vo.*;
import com.teyuntong.goods.search.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.search.service.common.error.GoodsSearchErrorCode;
import com.teyuntong.goods.search.service.common.property.RocketMqTopicProperty;
import com.teyuntong.goods.search.service.remote.basic.ConfigRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.rocketmq.core.RocketMqProducer;
import com.teyuntong.infra.common.rocketmq.message.MqMessage;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.Charset;
import java.text.Collator;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 常跑路线主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OftenRouteServiceImpl implements OftenRouteService {

    private final static Comparator<Object> zhCompare = Collator.getInstance(Locale.CHINA);

    private final OftenDriveUserMapper oftenDriveUserMapper;
    private final OftenDriveRouteMapper oftenDriveRouteMapper;
    private final OftenDriveRouteAddressMapper oftenDriveRouteAddressMapper;
    private final OftenDriveRouteUserMapper oftenDriveRouteUserMapper;
    private final OftenDriveRouteUserAddressMapper routeUserAddressMapper;
    private final ConfigRemoteService configRemoteService;

    private final RocketMqProducer rocketMqProducer;
    private final RocketMqTopicProperty rocketMqTopicProperty;
    private final RouteDataService routeDataService;

    private final RedisTemplate<String, Object> oftenRouteRedisTemplate;


    @Override
    public void saveMainSwitch(MainSwitchVo mainSwitchVo) {
        if (mainSwitchVo.getStartTime() > mainSwitchVo.getEndTime()) {
            throw new BusinessException(GoodsSearchErrorCode.ROUTE_TIME_ERROR);
        }

        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        OftenDriveUserDO existRecord = oftenDriveUserMapper.getByUserId(userId);
        if (existRecord != null) {
            RouteConverter.INSTANCE.updateUserDO(existRecord, mainSwitchVo);
            existRecord.setModifyTime(new Date());
            oftenDriveUserMapper.updateById(existRecord);
        } else {
            OftenDriveUserDO driveUserDO = RouteConverter.INSTANCE.buildUserDO(mainSwitchVo);
            driveUserDO.setUserId(userId);
            driveUserDO.setCreateTime(new Date());
            driveUserDO.setModifyTime(new Date());
            oftenDriveUserMapper.insert(driveUserDO);
        }
    }

    @Override
    public MainSwitchVo getMainSwitch() {
        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        OftenDriveUserDO oftenDriveUserDO = oftenDriveUserMapper.getByUserId(userId);
        return RouteConverter.INSTANCE.buildSwitchVo(oftenDriveUserDO);
    }


    private int saveRouteAddress(Long routeId, UserOftenRouteVo sortRouteVo) {

        List<RouteAddressVo> startAddressList = sortRouteVo.getStartAddressList();
        List<RouteAddressVo> destAddressList = sortRouteVo.getDestAddressList();

        List<OftenDriveRouteAddressDO> routeAddressList = new ArrayList<>();

        for (RouteAddressVo oneAddress : startAddressList) {
            String provinc = StringUtils.defaultIfBlank(oneAddress.getProvinc(), "");
            String city = StringUtils.defaultIfBlank(oneAddress.getCity(), "");
            String area = StringUtils.defaultIfBlank(oneAddress.getArea(), "");
            //方便查询用
            String addressText = provinc + "|" + city + "|" + area;

            OftenDriveRouteAddressDO routeAddress = new OftenDriveRouteAddressDO();
            routeAddress.setRouteId(routeId);
            routeAddress.setProvinc(provinc);
            routeAddress.setCity(city);
            routeAddress.setArea(area);
            routeAddress.setType(RouteAddressTypeEnum.START.getCode());
            routeAddress.setAddressText(addressText);
            routeAddressList.add(routeAddress);
        }

        for (RouteAddressVo oneAddress : destAddressList) {
            String provinc = StringUtils.defaultIfBlank(oneAddress.getProvinc(), "");
            String city = StringUtils.defaultIfBlank(oneAddress.getCity(), "");
            String area = StringUtils.defaultIfBlank(oneAddress.getArea(), "");
            //方便查询用
            String addressText = provinc + "|" + city + "|" + area;

            OftenDriveRouteAddressDO routeAddress = new OftenDriveRouteAddressDO();
            routeAddress.setRouteId(routeId);
            routeAddress.setProvinc(provinc);
            routeAddress.setCity(city);
            routeAddress.setArea(area);
            routeAddress.setType(RouteAddressTypeEnum.DEST.getCode());
            routeAddress.setAddressText(addressText);
            routeAddressList.add(routeAddress);
        }

        for (OftenDriveRouteAddressDO oftenDriveRouteAddressDO : routeAddressList) {
            oftenDriveRouteAddressMapper.insert(oftenDriveRouteAddressDO);
        }
        return routeAddressList.size();
    }


    @Override
    public void reloadRouteTransport(Long routeId) {
        try {
            NewRouteMessageVo routeMessageVo = new NewRouteMessageVo(routeId);
            MqMessage message = new MqMessage(rocketMqTopicProperty.getOftenRoute(),
                    routeMessageVo.getTag(),
                    routeMessageVo.getMessageSerialNum(),
                    JSON.toJSONBytes(routeMessageVo));
            // rocketMqProducer.sendNormal(message);
            // 使用ScheduledExecutorService来延迟发送消息
            ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
            executorService.schedule(() -> {
                try {
                    rocketMqProducer.sendNormal(message);
                    log.info("reloadRouteTransport_success : {}", JSON.toJSONString(routeMessageVo));
                } catch (Exception e) {
                    log.error("reloadRouteTransport_error : ", e);
                } finally {
                    executorService.shutdown();
                }
            }, 1000, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("reloadRouteTransport_error : ", e);
        }
    }

    public OftenDriveRouteDO addOftenRoute(UserOftenRouteVo sortRouteVo) {
        String goodsTypes = sortRouteVo.getGoodsTypes();
        OftenDriveRouteDO driveRoute = new OftenDriveRouteDO();
        // 保存重量时设置默认值
        driveRoute.setMinWeight(sortRouteVo.getMinWeight() == null ? RouteConstant.DEFAULT_MIN_WEIGHT : sortRouteVo.getMinWeight());
        driveRoute.setMaxWeight(sortRouteVo.getMaxWeight() == null ? RouteConstant.DEFAULT_MAX_WEIGHT : sortRouteVo.getMaxWeight());
        driveRoute.setGoodsTypes(goodsTypes);
        driveRoute.setCreateTime(new Date());
        driveRoute.setModifyTime(new Date());
        driveRoute.setSignCode(sortRouteVo.getSignCode());
        driveRoute.setSignContent(sortRouteVo.getSignContent());

        oftenDriveRouteMapper.insert(driveRoute);

        Long routeId = driveRoute.getId();
        this.saveRouteAddress(routeId, sortRouteVo);

        this.reloadRouteTransport(routeId);
        return driveRoute;
    }

    /**
     * 保存用户路线顺序
     *
     * @param userOftenRouteVo
     * @param routeUserId
     * @param userId
     * @param routeId
     * @return
     */
    private int saveUserAddress(UserOftenRouteVo userOftenRouteVo, Long routeUserId, Long userId, Long routeId) {

        List<RouteAddressVo> startAddressList = userOftenRouteVo.getStartAddressList();
        List<RouteAddressVo> destAddressList = userOftenRouteVo.getDestAddressList();

        List<OftenDriveRouteUserAddressDO> userAddressList = new ArrayList<>();

        for (RouteAddressVo oneAddress : startAddressList) {
            String provinc = StringUtils.defaultIfBlank(oneAddress.getProvinc(), "");
            String city = StringUtils.defaultIfBlank(oneAddress.getCity(), "");
            String area = StringUtils.defaultIfBlank(oneAddress.getArea(), "");

            OftenDriveRouteUserAddressDO userAddress = new OftenDriveRouteUserAddressDO();
            userAddress.setRouteUserId(routeUserId);
            userAddress.setUserId(userId);
            userAddress.setRouteId(routeId);
            userAddress.setProvinc(provinc);
            userAddress.setCity(city);
            userAddress.setArea(area);
            userAddress.setType(RouteAddressTypeEnum.START.getCode());

            userAddressList.add(userAddress);
        }
        for (RouteAddressVo oneAddress : destAddressList) {
            String provinc = StringUtils.defaultIfBlank(oneAddress.getProvinc(), "");
            String city = StringUtils.defaultIfBlank(oneAddress.getCity(), "");
            String area = StringUtils.defaultIfBlank(oneAddress.getArea(), "");

            OftenDriveRouteUserAddressDO userAddress = new OftenDriveRouteUserAddressDO();
            userAddress.setRouteUserId(routeUserId);
            userAddress.setUserId(userId);
            userAddress.setRouteId(routeId);
            userAddress.setProvinc(provinc);
            userAddress.setCity(city);
            userAddress.setArea(area);
            userAddress.setType(RouteAddressTypeEnum.DEST.getCode());

            userAddressList.add(userAddress);
        }

        for (OftenDriveRouteUserAddressDO oftenDriveRouteUserAddressDO : userAddressList) {
            routeUserAddressMapper.insert(oftenDriveRouteUserAddressDO);
        }
        return userAddressList.size();
    }

    /**
     * 获取对应数据库路线
     */
    private OftenDriveRouteDO getDbOftenRoute(UserOftenRouteVo sortRouteVo) {
        String signCode = sortRouteVo.getSignCode();

        OftenDriveRouteDO oftenDriveRoute = oftenDriveRouteMapper.getBySignCode(signCode);
        if (oftenDriveRoute == null) {
            oftenDriveRoute = this.addOftenRoute(sortRouteVo);
        }

        return oftenDriveRoute;
    }

    private void clearAddressArea(RouteAddressVo oneAddress) {
        String city = oneAddress.getCity();
        String area = oneAddress.getArea();
        if (StringUtils.isNotBlank(area) && area.equals(city)) {
            area = "";
        }
        oneAddress.setArea(area);
    }


    /**
     * 校验用户是否有重复地址路线
     *
     * @param userId
     * @param sortRouteVo
     * @return
     */
    private boolean checkAddressUnique(Long userId, UserOftenRouteVo sortRouteVo) {
        boolean unique = true;

        String addressSignContent = this.getAddressSignContent(sortRouteVo.getStartAddressList(), sortRouteVo.getDestAddressList());

        Long routeUserId = sortRouteVo.getId();

        List<OftenDriveRouteUserAddressDO> userAddressList = routeUserAddressMapper.getByUserId(userId);
        //如果修改，排除当前数据
        if (routeUserId != null) {
            userAddressList.removeIf(t -> t.getRouteUserId().equals(routeUserId));
        }

        if (CollectionUtils.isNotEmpty(userAddressList)) {

            Map<Long, UserOftenRouteVo> userRouteMap = new HashMap<>();

            for (OftenDriveRouteUserAddressDO oneUserAddress : userAddressList) {

                Long dbRouteUserId = oneUserAddress.getRouteUserId();

                UserOftenRouteVo oftenRouteVo = userRouteMap.get(dbRouteUserId);

                if (oftenRouteVo == null) {
                    oftenRouteVo = new UserOftenRouteVo();
                    oftenRouteVo.setId(dbRouteUserId);

                    userRouteMap.put(dbRouteUserId, oftenRouteVo);
                }

                RouteAddressVo addressVo = RouteConverter.INSTANCE.buildAddressVo(oneUserAddress);

                if (oneUserAddress.getType().equals(RouteAddressTypeEnum.START.getCode())) {
                    //出发地
                    oftenRouteVo.addStartAddress(addressVo);
                } else {
                    //目的地
                    oftenRouteVo.addDestAddress(addressVo);
                }
            }

            for (UserOftenRouteVo dbOftenRoute : userRouteMap.values()) {

                List<RouteAddressVo> startAddressList = dbOftenRoute.getStartAddressList();
                List<RouteAddressVo> destAddressList = dbOftenRoute.getDestAddressList();

                this.sortAddressList(startAddressList);
                this.sortAddressList(destAddressList);

                String dbAddressSign = this.getAddressSignContent(startAddressList, destAddressList);

                if (dbAddressSign.equals(addressSignContent)) {
                    unique = false;
                    break;
                }
            }
        }
        return unique;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOftenRoute(UserOftenRouteVo userOftenRouteVo) {
        Long userId = LoginHelper.getRequiredLoginUser().getUserId();

        this.clearAddressArea(userOftenRouteVo);

        Long routeUserId = userOftenRouteVo.getId();

        if (routeUserId == null) {
            // 新增
            int routeCount = oftenDriveRouteUserMapper.countByUserId(userId, null);
            Integer oftenFouteMax = configRemoteService.getIntValue(RouteConstant.OFTEN_ROUTE_MAX, 5);
            if (routeCount >= oftenFouteMax) {
                throw new BusinessException(GoodsSearchErrorCode.ROUTE_COUNT_EXCEED);
            }
        } else {
            this.checkUserRoute(userId, routeUserId);
        }

        UserOftenRouteVo sortRouteVo = this.sortUserOftenRoute(userOftenRouteVo);

        if (!this.checkAddressUnique(userId, sortRouteVo)) {
            throw new BusinessException(GoodsSearchErrorCode.ROUTE_HAS_EXIST);
        }

        String signContent = this.getSignContent(sortRouteVo);
        log.info("saveOftenRoute_signContent: {}", signContent);

        String signCode = DigestUtils.md5Hex(signContent);

        sortRouteVo.setSignContent(signContent);
        sortRouteVo.setSignCode(signCode);

        OftenDriveRouteDO oftenDriveRoute = this.getDbOftenRoute(sortRouteVo);

        Long routeId = oftenDriveRoute.getId();

        if (routeUserId == null) {
            //新增用户路线关系
            OftenDriveRouteUserDO newRouteUser = new OftenDriveRouteUserDO();
            newRouteUser.setUserId(userId);
            newRouteUser.setRouteId(routeId);
            newRouteUser.setReportStatus(RouteReportStatusEnum.ON.getCode());
            newRouteUser.setCreateTime(new Date());
            newRouteUser.setModifyTime(new Date());
            newRouteUser.setEnableStatus(RouteReportStatusEnum.ON.getCode());
            try {
                oftenDriveRouteUserMapper.insert(newRouteUser);
                routeUserId = newRouteUser.getId();
            } catch (DuplicateKeyException e) {
                log.error("用户路线重复", e);
                throw new BusinessException(GoodsSearchErrorCode.ROUTE_HAS_EXIST);
            }
        } else {
            //修改
            OftenDriveRouteUserDO updateRouteUser = new OftenDriveRouteUserDO();
            updateRouteUser.setId(routeUserId);
            updateRouteUser.setRouteId(routeId);
            try {
                oftenDriveRouteUserMapper.updateById(updateRouteUser);
            } catch (Exception e) {
                log.error("编辑用户路线重复", e);
                throw new BusinessException(GoodsSearchErrorCode.ROUTE_HAS_EXIST);
            }
            //删除所有路线
            int i = routeUserAddressMapper.deleteByRouteUserId(routeUserId);
            log.info("delete_user_address => count :{}", i);
        }

        // 保存用户路线顺序
        int i = this.saveUserAddress(userOftenRouteVo, routeUserId, userId, routeId);
        log.info("save_user_address => count :{}", i);

        // 删除缓存中用户启用路线
        oftenRouteRedisTemplate.delete(RedisKeyConstant.OFTEN_ROUTE_USER + userId);
    }

    /**
     * 设置路线播报开关
     *
     * @param routeUserId  路线用户id
     * @param reportStatus 播报状态
     */
    @Override
    public void setRouteSwitch(Long routeUserId, Integer reportStatus) {
        if (routeUserId == null || reportStatus == null) {
            throw new BusinessException(GoodsSearchErrorCode.ARGUMENTS_IS_NULL);
        }

        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        this.checkUserRoute(userId, routeUserId);

        reportStatus = reportStatus > 0 ? RouteReportStatusEnum.ON.getCode() : RouteReportStatusEnum.OFF.getCode();
        if (reportStatus <= 0) {
            int openCount = oftenDriveRouteUserMapper.countByUserId(userId, RouteReportStatusEnum.ON.getCode());
            if (openCount == 1) {
                throw new BusinessException(GoodsSearchErrorCode.ROUTE_REPORT_AT_LEAST_ONE);
            }
        }

        // 修改
        OftenDriveRouteUserDO updateRouteUser = new OftenDriveRouteUserDO();
        updateRouteUser.setId(routeUserId);
        updateRouteUser.setReportStatus(reportStatus);
        oftenDriveRouteUserMapper.updateById(updateRouteUser);

    }

    /**
     * 设置路线启用开关
     *
     * @param routeUserIds 路线用户ids，多个用逗号隔开
     * @param enableStatus 启用状态
     */
    @Override
    public void setRouteEnable(String routeUserIds, Integer enableStatus) {
        List<Long> routeUserIdList;
        if (StringUtils.isBlank(routeUserIds)) {
            routeUserIdList = new ArrayList<>();
        } else {
            routeUserIdList = Arrays.stream(URLDecoder.decode(routeUserIds, Charset.defaultCharset()).split(","))
                    .map(Long::parseLong).toList();
        }

        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        List<UserOftenRouteVo> routeList = oftenDriveRouteUserMapper.getRouteList(userId);
        List<Long> idList = routeList.stream().map(UserOftenRouteVo::getId).toList();

        // 修改
        for (Long id : idList) {
            OftenDriveRouteUserDO updateRouteUser = new OftenDriveRouteUserDO();
            updateRouteUser.setId(id);
            updateRouteUser.setEnableStatus(routeUserIdList.contains(id) ? 1 : 0);
            oftenDriveRouteUserMapper.updateById(updateRouteUser);
        }

        // 删除缓存中用户启用路线
        oftenRouteRedisTemplate.delete(RedisKeyConstant.OFTEN_ROUTE_USER + userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRoute(Long routeUserId) {
        if (routeUserId == null) {
            throw new BusinessException(GoodsSearchErrorCode.ARGUMENTS_IS_NULL);
        }

        Long userId = LoginHelper.getRequiredLoginUser().getUserId();
        this.checkUserRoute(userId, routeUserId);

        //删除路线用户表
        int i = routeUserAddressMapper.deleteByRouteUserId(routeUserId);
        log.info("delete_user_address => count :{}", i);
        // 删除路线表
        oftenDriveRouteUserMapper.deleteById(routeUserId);
        log.info("delete_user_route :{}", routeUserId);

        // 删除缓存中用户启用路线
        oftenRouteRedisTemplate.delete(RedisKeyConstant.OFTEN_ROUTE_USER + userId);
    }


    @Override
    public UserOftenRouteVo viewRoute(Long routeUserId) {
        if (routeUserId == null) {
            throw new BusinessException(GoodsSearchErrorCode.ARGUMENTS_IS_NULL);
        }

        Long userId = LoginHelper.getRequiredLoginUser().getUserId();

        OftenDriveRouteUserDO oftenDriveRouteUser = this.checkUserRoute(userId, routeUserId);

        Long routeId = oftenDriveRouteUser.getRouteId();

        OftenDriveRouteDO driveRoute = oftenDriveRouteMapper.selectById(routeId);

        List<OftenDriveRouteUserAddressDO> userAddressList = routeUserAddressMapper.getByRouteUserId(routeUserId);

        List<RouteAddressVo> startAddressList = new ArrayList<>();
        List<RouteAddressVo> destAddressList = new ArrayList<>();

        userAddressList.forEach(userAddress -> {
            RouteAddressVo routeAddressVo = new RouteAddressVo();
            routeAddressVo.setProvinc(userAddress.getProvinc());
            routeAddressVo.setCity(userAddress.getCity());
            routeAddressVo.setArea(userAddress.getArea());
            this.initAddressArea(routeAddressVo);

            if (userAddress.getType().equals(RouteAddressTypeEnum.START.getCode())) {
                startAddressList.add(routeAddressVo);
            } else {
                destAddressList.add(routeAddressVo);
            }
        });

        UserOftenRouteVo oftenRouteVo = new UserOftenRouteVo();
        oftenRouteVo.setId(routeUserId);
        oftenRouteVo.setUserId(userId);
        oftenRouteVo.setRouteId(oftenDriveRouteUser.getRouteId());
        // 显示时默认值还原为null
        oftenRouteVo.setMinWeight(Objects.equals(RouteConstant.DEFAULT_MIN_WEIGHT, driveRoute.getMinWeight()) ? null : driveRoute.getMinWeight());
        oftenRouteVo.setMaxWeight(Objects.equals(RouteConstant.DEFAULT_MAX_WEIGHT, driveRoute.getMaxWeight()) ? null : driveRoute.getMaxWeight());
        oftenRouteVo.setGoodsTypes(driveRoute.getGoodsTypes());
        oftenRouteVo.setReportStatus(oftenDriveRouteUser.getReportStatus());

        oftenRouteVo.setStartAddressList(startAddressList);
        oftenRouteVo.setDestAddressList(destAddressList);

        return oftenRouteVo;
    }


    @Override
    public List<UserOftenRouteVo> getUserRouteList() {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return List.of();
        }
        Long userId = loginUser.getUserId();
        List<UserOftenRouteVo> userRouteList = oftenDriveRouteUserMapper.getRouteList(userId);

        if (CollectionUtils.isEmpty(userRouteList)) {
            return userRouteList;
        }

        Map<Long, UserOftenRouteVo> userRouteMap = new HashMap<>();
        for (UserOftenRouteVo oneUserRoute : userRouteList) {
            Long oneRouteUserId = oneUserRoute.getId();
            Long routeId = oneUserRoute.getRouteId();
            // 显示时默认值还原为null
            oneUserRoute.setMinWeight(Objects.equals(RouteConstant.DEFAULT_MIN_WEIGHT, oneUserRoute.getMinWeight()) ? null : oneUserRoute.getMinWeight());
            oneUserRoute.setMaxWeight(Objects.equals(RouteConstant.DEFAULT_MAX_WEIGHT, oneUserRoute.getMaxWeight()) ? null : oneUserRoute.getMaxWeight());
            //获取条数
            RouteTransportCountVo oneRouteCount = routeDataService.getOneRouteCount(new Date(), routeId);
            oneUserRoute.setCount(oneRouteCount.getCount());

            userRouteMap.put(oneRouteUserId, oneUserRoute);
        }

        List<OftenDriveRouteUserAddressDO> userAddressList = routeUserAddressMapper.getByUserId(userId);

        for (OftenDriveRouteUserAddressDO oneUserAddress : userAddressList) {
            UserOftenRouteVo oftenRouteVo = userRouteMap.get(oneUserAddress.getRouteUserId());
            if (oftenRouteVo == null) {
                // 有地址但没有主表
                log.warn("UserRoute_address_problem : has address, but no route_user : {}", oneUserAddress.getId());
                continue;
            }

            RouteAddressVo routeAddressVo = RouteConverter.INSTANCE.buildAddressVo(oneUserAddress);
            this.initAddressArea(routeAddressVo);
            if (oneUserAddress.getType().equals(RouteAddressTypeEnum.START.getCode())) {
                oftenRouteVo.addStartAddress(routeAddressVo);
            } else {
                oftenRouteVo.addDestAddress(routeAddressVo);
            }
        }
        return userRouteList;
    }

    // ================================================

    /**
     * 校验路线id是否属于当前用户
     *
     * @param userId      用户id
     * @param routeUserId 路线用户id
     */
    private OftenDriveRouteUserDO checkUserRoute(Long userId, Long routeUserId) {
        OftenDriveRouteUserDO oftenDriveRouteUser = oftenDriveRouteUserMapper.selectById(routeUserId);
        if (oftenDriveRouteUser == null) {
            throw new BusinessException(GoodsSearchErrorCode.ROUTE_ARGUMENTS_ERROR);
        }
        if (!Objects.equals(oftenDriveRouteUser.getUserId(), userId)) {
            throw new BusinessException(GoodsSearchErrorCode.ROUTE_ARGUMENTS_ERROR);
        }
        return oftenDriveRouteUser;
    }

    /**
     * 前端区县一级不选时，会多出一层
     */
    private void clearAddressArea(UserOftenRouteVo userOftenRouteVo) {
        List<RouteAddressVo> startAddressList = userOftenRouteVo.getStartAddressList();
        List<RouteAddressVo> destAddressList = userOftenRouteVo.getDestAddressList();

        for (RouteAddressVo oneAddress : startAddressList) {
            this.clearAddressArea(oneAddress);
        }

        for (RouteAddressVo oneAddress : destAddressList) {
            this.clearAddressArea(oneAddress);
        }
    }

    private void initAddressArea(RouteAddressVo oneAddress) {
        String city = oneAddress.getCity();
        String area = oneAddress.getArea();
        if (StringUtils.isBlank(area)) {
            area = city;
        }
        oneAddress.setArea(area);
    }


    /**
     * 生成地址签名
     */
    public String getAddressSignContent(List<RouteAddressVo> startAddressList, List<RouteAddressVo> destAddressList) {
        //minWeight;maxWeight;1|2|3|4;北京.北京市.海淀区|河北省.保定市|山东省;湖南省.长沙市|湖北省.武汉市|江苏省.南京市|浙江省
        List<String> signList = new ArrayList<>();

        String startAddressStr = this.getAddressListSign(startAddressList);
        signList.add(startAddressStr);

        String destAddressStr = this.getAddressListSign(destAddressList);
        signList.add(destAddressStr);

        return StringUtils.join(signList, ";");
    }

    private String getAddressListSign(List<RouteAddressVo> addressList) {
        List<String> contentList = addressList.stream().map(RouteAddressVo::getAddressSign).collect(Collectors.toList());
        return StringUtils.join(contentList, "|");
    }

    private void sortAddressList(List<RouteAddressVo> addressList) {
        if (CollectionUtils.isEmpty(addressList)) {
            throw new BusinessException(GoodsSearchErrorCode.ROUTE_ADDRESS_IS_NULL);
        }
        if (addressList.size() > 3) {
            throw new BusinessException(GoodsSearchErrorCode.ROUTE_ADDRESS_COUNT_EXCEED);
        }
        addressList.sort((o1, o2) -> zhCompare.compare(o1.getAddressSign(), o2.getAddressSign()));
    }

    /**
     * 货物类型排序
     */
    private String sortGoodsTypes(String goodsTypes) {
        String resultTypes = "";
        if (StringUtils.isNotBlank(goodsTypes)) {
            String[] typeArray = goodsTypes.split(",");
            List<Integer> typeTmpList = Arrays.stream(typeArray).map(Integer::parseInt)
                    .sorted(Comparator.naturalOrder())
                    .collect(Collectors.toList());
            resultTypes = StringUtils.join(typeTmpList, ",");
        }
        return resultTypes;
    }

    /**
     * 将请求结果关键字段排序
     */
    private UserOftenRouteVo sortUserOftenRoute(UserOftenRouteVo userOftenRouteVo) {
        UserOftenRouteVo sortUserRouteVo = new UserOftenRouteVo();
        BeanUtils.copyProperties(userOftenRouteVo, sortUserRouteVo);

        String goodsTypes = sortUserRouteVo.getGoodsTypes();
        sortUserRouteVo.setGoodsTypes(this.sortGoodsTypes(goodsTypes));

        this.sortAddressList(sortUserRouteVo.getStartAddressList());
        this.sortAddressList(sortUserRouteVo.getDestAddressList());

        return sortUserRouteVo;
    }

    /**
     * 生成签名
     *
     * @param sortRouteVo
     * @return
     */
    private String getSignContent(UserOftenRouteVo sortRouteVo) {
        //minWeight;maxWeight;1|2|3|4;北京.北京市.海淀区|河北省.保定市|山东省;湖南省.长沙市|湖北省.武汉市|江苏省.南京市|浙江省
        Integer minWeight = sortRouteVo.getMinWeight();
        Integer maxWeight = sortRouteVo.getMaxWeight();
        String goodsTypes = sortRouteVo.getGoodsTypes();
        List<RouteAddressVo> startAddressList = sortRouteVo.getStartAddressList();
        List<RouteAddressVo> destAddressList = sortRouteVo.getDestAddressList();

        List<String> signList = new ArrayList<>();

        String minWeightStr = minWeight == null ? "" : minWeight + "";
        String maxWeightStr = maxWeight == null ? "" : maxWeight + "";

        signList.add(minWeightStr);
        signList.add(maxWeightStr);

        //货物类型
        String goodsTypeStr = sortRouteVo.getGoodsTypes();
        signList.add(goodsTypeStr);

        String startAddressStr = this.getAddressListSign(startAddressList);
        signList.add(startAddressStr);

        String destAddressStr = this.getAddressListSign(destAddressList);
        signList.add(destAddressStr);

        return StringUtils.join(signList, ";");
    }
}
