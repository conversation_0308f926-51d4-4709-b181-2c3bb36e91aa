package com.teyuntong.goods.search.service.biz.goods.service.impl;

import com.teyuntong.goods.search.service.biz.goods.entity.TransportExtendDO;
import com.teyuntong.goods.search.service.biz.goods.mapper.TransportExtendMapper;
import com.teyuntong.goods.search.service.biz.goods.service.TransportExtendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/03/17 16:05
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportExtendServiceImpl implements TransportExtendService {

    private final TransportExtendMapper transportExtendMapper;

    /**
     * 返回tsId对应的map
     *
     * @param tsIds
     */
    @Override
    public Map<Long, TransportExtendDO> getMapByTsIds(List<Long> tsIds) {
        return getByTsIds(tsIds).stream()
                .collect(Collectors.toMap(TransportExtendDO::getTsId, t -> t));
    }

    /**
     * 根据tsId查询
     *
     * @param tsIds
     */
    @Override
    public List<TransportExtendDO> getByTsIds(List<Long> tsIds) {
        if (CollectionUtils.isEmpty(tsIds)) {
            return List.of();
        }
        return transportExtendMapper.getByTsIds(tsIds);
    }

    /**
     * 根据tsId查询
     *
     * @param tsId
     */
    @Override
    public TransportExtendDO getByTsId(Long tsId) {
        if (tsId == null) {
            return null;
        }
        return getByTsIds(List.of(tsId)).stream().findFirst().orElse(null);
    }
}
