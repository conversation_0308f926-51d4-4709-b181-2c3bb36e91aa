package com.teyuntong.goods.search.service.rpc.similarity.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.teyuntong.goods.search.service.biz.behavior.serivce.ShieldingShipperService;
import com.teyuntong.goods.search.service.biz.goods.converter.TransportConverter;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportExtendDO;
import com.teyuntong.goods.search.service.biz.goods.service.TransportExtendService;
import com.teyuntong.goods.search.service.biz.goods.service.TransportService;
import com.teyuntong.goods.search.service.biz.similarity.converter.SimilarityRpcConverter;
import com.teyuntong.goods.search.service.biz.similarity.dto.SimilarityQueryDTO;
import com.teyuntong.goods.search.service.biz.similarity.service.SimilarityTransportService;
import com.teyuntong.goods.search.service.biz.similarity.vo.SimilarityVO;
import com.teyuntong.goods.search.service.remote.basic.ConfigRemoteService;
import com.teyuntong.goods.search.service.remote.basic.constant.TytConfigKey;
import com.teyuntong.goods.search.service.rpc.similarity.service.SimilarityRpcService;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/7/22 15:36
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SimilarityRpcServiceImpl implements SimilarityRpcService {

    private final SimilarityTransportService similarityTransportService;
    private final ShieldingShipperService shieldingShipperService;
    private final TransportService transportService;
    private final TransportExtendService transportExtendService;
    private final ConfigRemoteService configRemoteService;

    @Override
    public List<SimilarityVO> getSimilarityList(SimilarityQueryDTO queryDTO) {
        queryDTO.setUserId(LoginHelper.getRequiredLoginUser().getUserId());
        // 获取当前用户屏蔽的发货人列表
        List<Long> excludeUserIds = shieldingShipperService.getShieldingUserList(queryDTO.getUserId());

        queryDTO.setExcludeUserIds(excludeUserIds);
        List<TransportDO> similarityList = similarityTransportService.getSimilarityList(queryDTO);

        List<SimilarityVO> similarityVOList = SimilarityRpcConverter.INSTANCE.convertDOs2VOs(similarityList);

        if (CollUtil.isNotEmpty(similarityVOList)) {
            // 设置扩展表字段
            List<Long> tsIdList = similarityVOList.stream().map(SimilarityVO::getId).toList();
            Map<Long, TransportExtendDO> extendDOMap = transportExtendService.getMapByTsIds(tsIdList);
            similarityVOList.forEach(vo -> TransportConverter.INSTANCE.transportExtend2VO(vo, extendDOMap.get(vo.getId())));

            // 按照相似货源逻辑排序
            similarityTransportService.sortSimilarityList(similarityVOList);

            // 处理标签数据
            transportService.handleTransportTag(similarityVOList, queryDTO.getUserId());

            // 加密数据
            transportService.hideSensitiveInfo(similarityVOList, queryDTO.getUserId());

            String rankLevelDisplay = configRemoteService.getStringValue(TytConfigKey.SIMILAR_DISPLAY_ON_OFF);
            similarityVOList.forEach(vo -> vo.setRankLevelDisplay(rankLevelDisplay));
        }

        return similarityVOList;
    }

}
