package com.teyuntong.goods.search.service.biz.behavior.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 捂货记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-14
 */
@Getter
@Setter
@TableName("tyt_cover_goods_log")
public class CoverGoodsLogDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 车主id
     */
    private Long userId;

    /**
     * 货源id
     */
    private Long tsId;

    /**
     * 货源类型：1好货 2中货 3差货
     */
    private Integer goodsLevel;

    /**
     * 当天浏览的第几个货源，按货源类型分别统计
     */
    private Integer viewIndex;

    /**
     * 命中规则
     */
    private String hitRule;

    /**
     * 优推好车主过期时间
     */
    private Date priorityRecommendExpireTime;

    /**
     * 发货倒计时
     */
    private Integer xTimeInSeconds;

    /**
     * 详情倒计时
     */
    private Integer yTimeInSeconds;

    /**
     * 捂货间隔
     */
    private Integer coverInterval;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}
