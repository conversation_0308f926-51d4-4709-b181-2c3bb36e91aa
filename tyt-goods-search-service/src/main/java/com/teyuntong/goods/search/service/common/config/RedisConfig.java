package com.teyuntong.goods.search.service.common.config;

import com.alibaba.fastjson.support.spring.GenericFastJsonRedisSerializer;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.time.Duration;

/**
 * RedisTemplate配置，常跑路线单独的redis实例，需要单独配置
 *
 * <AUTHOR>
 * @since 2024/12/18 11:20
 */
@Configuration
public class RedisConfig {

    // default redis config
    @Bean(name = "defaultLettuceConnectionFactory")
    @Primary
    public LettuceConnectionFactory defaultLettuceConnectionFactory(
            @Qualifier("defaultRedisConfig") RedisStandaloneConfiguration defaultRedisConfig,
            @Qualifier("defaultPoolConfig") GenericObjectPoolConfig defaultPoolConfig) {
        LettucePoolingClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder()
                .commandTimeout(Duration.ofMillis(5000))
                .poolConfig(defaultPoolConfig)
                .build();
        return new LettuceConnectionFactory(defaultRedisConfig, clientConfig);
    }

    @Bean
   @Primary
    public RedisTemplate<String, Object> defaultRedisTemplate(
            @Qualifier("defaultLettuceConnectionFactory") LettuceConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();

        template.setKeySerializer(RedisSerializer.string());
        template.setValueSerializer(new GenericFastJsonRedisSerializer());
        template.setHashKeySerializer(RedisSerializer.string());
        template.setHashValueSerializer(new GenericFastJsonRedisSerializer());

        template.setConnectionFactory(connectionFactory);
        template.afterPropertiesSet();
        return template;
    }

    // often route redis config
    @Bean(name = "oftenRouteLettuceConnectionFactory")
    public LettuceConnectionFactory oftenRouteLettuceConnectionFactory(
            @Qualifier("oftenRouteRedisConfig") RedisStandaloneConfiguration oftenRouteRedisConfig,
            @Qualifier("oftenRoutePoolConfig") GenericObjectPoolConfig oftenRoutePoolConfig) {
        LettucePoolingClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder()
                .commandTimeout(Duration.ofMillis(5000))
                .poolConfig(oftenRoutePoolConfig)
                .build();
        return new LettuceConnectionFactory(oftenRouteRedisConfig, clientConfig);
    }

    @Bean(name = "oftenRouteRedisTemplate")
    public OftenRouteRedisTemplate oftenRouteRedisTemplate(
            @Qualifier("oftenRouteLettuceConnectionFactory") LettuceConnectionFactory connectionFactory) {
        OftenRouteRedisTemplate template = new OftenRouteRedisTemplate(connectionFactory);

        template.setKeySerializer(RedisSerializer.string());
        template.setValueSerializer(new GenericFastJsonRedisSerializer());
        template.setHashKeySerializer(RedisSerializer.string());
        template.setHashValueSerializer(new GenericFastJsonRedisSerializer());

        template.setConnectionFactory(connectionFactory);
        template.afterPropertiesSet();
        return template;
    }

    // ==================================

    @Configuration
    public static class DefaultRedisConfig {
        @Value("${spring.redis.host}")
        private String host;
        @Value("${spring.redis.port}")
        private Integer port;
        @Value("${spring.redis.password}")
        private String password;
        @Value("${spring.redis.database}")
        private Integer database;

        @Value("${spring.redis.lettuce.pool.max-active}")
        private Integer maxActive;
        @Value("${spring.redis.lettuce.pool.max-idle}")
        private Integer maxIdle;
        @Value("${spring.redis.lettuce.pool.min-idle}")
        private Integer minIdle;

        @Bean(name = "defaultPoolConfig")
        @Primary
        public GenericObjectPoolConfig defaultPoolConfig() {
            GenericObjectPoolConfig config = new GenericObjectPoolConfig();
            config.setMaxTotal(maxActive);
            config.setMaxIdle(maxIdle);
            config.setMinIdle(minIdle);
            return config;
        }

        @Bean(name = "defaultRedisConfig")
        @Primary
        public RedisStandaloneConfiguration defaultRedisConfig() {
            RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
            config.setHostName(host);
            config.setPort(port);
            config.setPassword(password);
            config.setDatabase(database);
            return config;
        }
    }

    @Configuration
    public static class OftenRouteRedisConfig {
        @Value("${spring.redis-often-route.host}")
        private String host;
        @Value("${spring.redis-often-route.port}")
        private Integer port;
        @Value("${spring.redis-often-route.password}")
        private String password;
        @Value("${spring.redis-often-route.database}")
        private Integer database;

        @Value("${spring.redis-often-route.lettuce.pool.max-active}")
        private Integer maxActive;
        @Value("${spring.redis-often-route.lettuce.pool.max-idle}")
        private Integer maxIdle;
        @Value("${spring.redis-often-route.lettuce.pool.min-idle}")
        private Integer minIdle;

        @Bean(name = "oftenRoutePoolConfig")
        public GenericObjectPoolConfig oftenRoutePoolConfig() {
            GenericObjectPoolConfig config = new GenericObjectPoolConfig();
            config.setMaxTotal(maxActive);
            config.setMaxIdle(maxIdle);
            config.setMinIdle(minIdle);
            return config;
        }

        @Bean(name = "oftenRouteRedisConfig")
        public RedisStandaloneConfiguration oftenRouteRedisConfig() {
            RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
            config.setHostName(host);
            config.setPort(port);
            config.setPassword(password);
            config.setDatabase(database);
            return config;
        }
    }

}
