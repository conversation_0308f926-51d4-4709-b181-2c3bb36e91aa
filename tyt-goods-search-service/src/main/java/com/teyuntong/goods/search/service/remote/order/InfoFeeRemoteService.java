package com.teyuntong.goods.search.service.remote.order;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.trade.service.client.infofee.service.InfoFeeRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * trade 订单接口
 *
 * <AUTHOR>
 * @since 2024/08/13 10:50
 */
@FeignClient(name = "tyt-trade-service", path = "trade", contextId = "InfoFeeRemoteService",
        fallbackFactory = InfoFeeRemoteService.InfoFeeRemoteServiceFallback.class)
public interface InfoFeeRemoteService extends InfoFeeRpcService {
    @Component
    class InfoFeeRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<InfoFeeRemoteService> {
        public InfoFeeRemoteServiceFallback() {
            super(true, InfoFeeRemoteService.class);
        }
    }
}
