package com.teyuntong.goods.search.service.biz.goods.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * 货源偏好请求
 *
 * <AUTHOR>
 * @since 2024/10/24 14:09
 */
@Getter
@Setter
public class TransportPreferenceDTO {

    @NotNull(message = "用户id不能为空")
    protected Long userId;
    /**
     * 货源id
     */
    @NotNull(message = "货源id不能为空")
    private Long srcMsgId;
    /**
     * 状态 0：不喜欢  1：喜欢
     */
    @NotNull(message = "偏好状态不能为空")
    private Integer status;
}
