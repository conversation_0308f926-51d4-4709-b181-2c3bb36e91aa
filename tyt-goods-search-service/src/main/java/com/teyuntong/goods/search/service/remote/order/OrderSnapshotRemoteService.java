package com.teyuntong.goods.search.service.remote.order;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.trade.service.client.orders.service.TransportOrderSnapshotRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * trade 订单接口
 *
 * <AUTHOR>
 * @since 2024/08/13 10:50
 */
@FeignClient(name = "tyt-trade-service", path = "trade", contextId = "orderSnapshotRemoteService",
        fallbackFactory = OrderSnapshotRemoteService.OrderRemoteServiceFallback.class)
public interface OrderSnapshotRemoteService extends TransportOrderSnapshotRpcService {
    @Component
    class OrderRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<OrderSnapshotRemoteService> {
        public OrderRemoteServiceFallback() {
            super(true, OrderSnapshotRemoteService.class);
        }
    }
}
