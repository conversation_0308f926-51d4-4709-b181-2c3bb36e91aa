package com.teyuntong.goods.search.service.rpc.goods.service;

import com.teyuntong.goods.search.service.biz.goods.dto.SearchRecordQueryDTO;
import com.teyuntong.goods.search.service.biz.record.dto.AppCallLogDTO;
import com.teyuntong.goods.search.service.biz.record.dto.CallFeedBackDTO;
import com.teyuntong.goods.search.service.biz.record.dto.CallPhoneRecordDTO;
import com.teyuntong.goods.search.service.biz.record.vo.CallFeedBackVO;
import com.teyuntong.goods.search.service.biz.record.vo.SearchRecordPageVO;

import java.util.List;

/**
 * 找货记录模块
 *
 * <AUTHOR>
 * @since 2024/10/11 10:00
 */
public interface SearchRecordRpcService {

    /**
     * 查询找货记录
     */
    SearchRecordPageVO queryRecordList(SearchRecordQueryDTO queryDTO);

    /**
     * 车方 被反馈（报价被货方同意）、有回价 气泡提示
     *
     * @param userId 车主id
     */
    String quotedBubble(Long userId);

    /**
     * 获取默认页面类型
     * @param userId
     * @return
     */
    Integer getDefaultType(Long userId);

    String getFreeCommissionBubble(Long userId);

    /**
     * 保存拨打电话记录，点击手机号时调用
     */
    void saveCallRecord(CallPhoneRecordDTO recordDTO);

    /**
     * 获取拨打反馈页面数据
     */
    CallFeedBackVO getCallFeedback(Long srcMsgId);

    /**
     * 保存拨打反馈页面数据
     */
    void saveCallFeedback(CallFeedBackDTO callFeedBackDTO);

    /**
     * 是否填写过反馈页面数据
     */
    Integer hasCallFeedback(Long srcMsgId);

    /**
     * 返回反馈一级选项
     */
    List<CallFeedBackVO.Option> getCallFeedbackOptions(Long srcMsgId);
}
