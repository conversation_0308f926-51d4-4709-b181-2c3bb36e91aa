package com.teyuntong.goods.search.service.biz.goods.dto.rule;

import lombok.Getter;
import lombok.Setter;

/**
 * 智能排序因子：地址匹配度
 * 格式：{"cityCityScore":100,"cityProvinceScore":90,"provinceCityScore":80,"provinceProvinceScore":70,"startCityScore":60,"startProvinceScore":50,"destCityScore":40,"destProvinceScore":30}
 * cityCityScore出发地市目的地市匹配得分；cityProvinceScore出发地市目的地省匹配得分；startCityScore只出发地市匹配得分；后面类似
 *
 * <AUTHOR>
 * @since 2024/8/29 15:59
 */
@Getter
@Setter
public class IntelligentSortRuleAddress {

    /**
     * 出发地目的地均一致
     */
    private Integer cityCityScore;
    /**
     * 出发地市一致，目的地省一致
     */
    private Integer cityProvinceScore;
    /**
     * 出发地省一致，目的地市一致
     */
    private Integer provinceCityScore;
    /**
     * 出发地省一致，目的地省一致
     */
    private Integer provinceProvinceScore;
    /**
     * 仅出发地市一致
     */
    private Integer startCityScore;
    /**
     * 仅目的地市一致
     */
    private Integer destCityScore;
    /**
     * 仅出发地省一致
     */
    private Integer startProvinceScore;
    /**
     * 仅目的地省一致
     */
    private Integer destProvinceScore;
}
