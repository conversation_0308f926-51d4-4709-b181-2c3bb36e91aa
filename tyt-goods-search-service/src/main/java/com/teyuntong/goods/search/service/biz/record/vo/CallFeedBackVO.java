package com.teyuntong.goods.search.service.biz.record.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 拨打反馈页面反显VO
 */
@Getter
@Setter
public class CallFeedBackVO {

    /**
     * 货源id
     */
    private Long srcMsgId;

    /**
     * 是否展示报价文本框 1展示0不展示
     */
    private Integer isShowPriceInput;

    /**
     * 运费，无价货源为null
     */
    private Integer price;

    /**
     * 近30天相似货源平均成交价
     */
    private Integer avgDealPrice;

    /**
     * 反馈选项列表
     */
    private List<Option> feedbackOptions;

    /**
     * 加价选项列表
     */
    private List<Integer> addPriceOptions;

    /**
     * 选项列表
     */
    @Getter
    @Setter
    public static class Option {
        private String value; // 选项值
        private String name; // 选项名称
        private boolean checked = false; // 是否选中
        private List<Option> children; // 子选项
    }
}
