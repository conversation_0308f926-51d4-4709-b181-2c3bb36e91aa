package com.teyuntong.goods.search.service.biz.goods.dto.rule;

import lombok.Data;

/**
 * 智能排序因子：时效性
 * 格式：[{"score":100,"ageing":0.25},{"score":80,"ageing":0.5},{"score":60,"ageing":0.75},{"score":40,"ageing":1},{"score":20,"ageing":3},{"score":5,"ageing":12}]
 * ageing发货失效<=0.25得分100
 *
 * <AUTHOR>
 * @since 2024/8/29 15:59
 */
@Data
public class IntelligentSortRuleAgeing {
    /**
     * 时效性小时
     */
    private Long ageing;
    /**
     * 评分
     */
    private Integer score;

}
