package com.teyuntong.goods.search.service.rpc.certification.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 证件非法提示vo
 *
 * <AUTHOR>
 * @since 2024/08/10 11:30
 */
@Getter
@Setter
public class CertIllegalInfoVo {

    /**
     * 校验是否非法，0合法，1非法
     */
    private Integer isIllegal;

    /**
     * 标题类型：1详情页证件缺失提示；2证件缺失提醒；3找货限制提醒 ；4找货限制提醒（pc小程序）
     */
    private Integer titleType;

    /**
     * 标题：证件缺失提醒、找货限制
     */
    private String title;

    /**
     * 弹窗提示文案
     */
    private String message;

    /**
     * 具体非法信息，存在多条，如：车辆认证不合格、车辆审核失败、司机证件不合格
     */
    private List<String> items;
}


