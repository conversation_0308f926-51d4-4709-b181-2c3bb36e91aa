package com.teyuntong.goods.search.service.rpc.exposure.service;

import com.teyuntong.goods.search.service.biz.exposure.vo.ExposureDataVO;
import com.teyuntong.goods.search.service.biz.exposure.vo.ExposureVO;
import com.teyuntong.goods.search.service.biz.goods.dto.TransportExposureQueryDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.TransportPreferenceDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/07/23 11:04
 */
public interface ExposureRpcService {

    /**
     * 保存急走货源开关状态
     */
    void saveSwitch(Integer switchStatus);
    /**
     * 获取曝光货源数据轮播数据
     */
    ExposureDataVO getHurryBannerList(TransportExposureQueryDTO searchDTO);

    /**
     * 获取急走专区大厅列表
     */
    List<ExposureVO> getHurryList(TransportExposureQueryDTO searchDTO);

    /**
     * 更新货源偏好状态
     */
    void savePreference(TransportPreferenceDTO preferenceDTO);
}
