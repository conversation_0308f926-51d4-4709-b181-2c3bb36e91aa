package com.teyuntong.goods.search.service.biz.goods.vo;

import com.teyuntong.goods.search.service.biz.goods.dto.SearchHallExtra;
import lombok.Data;

import java.util.List;

/**
 * @Describe
 * <AUTHOR>
 * @Date 2023/3/16
 */
@Data
public class SearchHallVO {
    /**
     * 当前查询返回了多少条数据
     */
    private Integer responseSize = 0;
    /**
     * 是否还有数据  true:有  false:无
     */
    private boolean hasNext = false;
    /**
     * 额外信息，客户端翻页下次调用时传入
     */
    private SearchHallExtra searchHallExtra;
    /**
     * 当前查询列表的最大id
     */
    private Long maxTsId;
    /**
     * 货源列表
     */
    private List<TransportVO> list;


}
