package com.teyuntong.goods.search.service.biz.record.service;


import com.teyuntong.goods.search.service.biz.goods.dto.GoodsPhoneDTO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportMainDO;
import com.teyuntong.goods.search.service.biz.record.entity.UserCallPhoneRecordDO;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import org.springframework.scheduling.annotation.Async;

import java.util.Date;

/**
 * <p>
 * 获取电话记录表（货源与用户关系）每个用户一条数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
public interface UserCallPhoneRecordService {
    /**
     * 查询用户货源的拨打记录
     *
     * @param userId
     * @param srcMsgId
     * @return
     */
    UserCallPhoneRecordDO getByUserAndSrcMsgId(Long userId, Long srcMsgId);

    /**
     * 查询用户拨打记录的去重时间
     *
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    int getDistinctTsCountByUserId(Long userId, Date startTime, Date endTime);

    /**
     * 保存获取手机号成功记录
     *
     * @param goodsPhoneDTO
     * @param user
     * @param transportMainDO
     */
    int saveRecord(GoodsPhoneDTO goodsPhoneDTO, LoginUserDTO user, TransportMainDO transportMainDO);

//    @Async
    void userActiveGift(Long userId);

    int getDistinctTsCountBySrcMsgId(Long srcMsgId);
}
