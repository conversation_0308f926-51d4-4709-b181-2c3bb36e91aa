package com.teyuntong.goods.search.service.biz.route.converter;

import com.teyuntong.goods.search.service.biz.route.dto.UserOftenRouteDTO;
import com.teyuntong.goods.search.service.biz.route.entity.OftenDriveRouteAddressDO;
import com.teyuntong.goods.search.service.biz.route.entity.OftenDriveRouteUserAddressDO;
import com.teyuntong.goods.search.service.biz.route.entity.OftenDriveUserDO;
import com.teyuntong.goods.search.service.biz.route.vo.MainSwitchVo;
import com.teyuntong.goods.search.service.biz.route.vo.RouteAddressVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/10/28 14:12
 */
@Mapper
public interface RouteConverter {
    RouteConverter INSTANCE = Mappers.getMapper(RouteConverter.class);

    /**
     * 创建用户开关DO
     */
    OftenDriveUserDO buildUserDO(MainSwitchVo mainSwitchVo);

    /**
     * 更新用户开关DO
     */
    void updateUserDO(@MappingTarget OftenDriveUserDO oftenDriveUserDO, MainSwitchVo mainSwitchVo);

    /**
     * 创建用户开关VO
     */
    MainSwitchVo buildSwitchVo(OftenDriveUserDO oftenDriveUserDO);

    /**
     * 路线地址转换VO
     */
    RouteAddressVo buildAddressVo(OftenDriveRouteUserAddressDO routeUserAddressDO);

    /**
     * 路线地址转换DTO
     */
    @Mapping(target = "p", source = "provinc", conditionExpression = "java(!\"\".equals(addressDO.getProvinc()))")
    @Mapping(target = "c", source = "city", conditionExpression = "java(!\"\".equals(addressDO.getCity()))")
    @Mapping(target = "a", source = "area", conditionExpression = "java(!\"\".equals(addressDO.getArea()))")
    UserOftenRouteDTO.RouteAddressDTO buildAddressDTO(OftenDriveRouteAddressDO addressDO);
}
