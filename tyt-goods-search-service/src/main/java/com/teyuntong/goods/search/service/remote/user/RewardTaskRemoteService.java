package com.teyuntong.goods.search.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.user.service.RewardTaskRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 领奖任务
 *
 * <AUTHOR>
 * @since 2024/07/17 16:43
 */
@FeignClient(name = "tyt-user-service", path = "user", contextId = "rewardTaskRemoteService",
        fallbackFactory = RewardTaskRemoteService.UserLimitRemoteServiceFallback.class)
public interface RewardTaskRemoteService extends RewardTaskRpcService {

    @Component
    class UserLimitRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<RewardTaskRemoteService> {
        public UserLimitRemoteServiceFallback() {
            super(true, RewardTaskRemoteService.class);
        }
    }
}
