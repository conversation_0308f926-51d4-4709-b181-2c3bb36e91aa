package com.teyuntong.goods.search.service.biz.route.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 常跑路线总开关
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Getter
@Setter
@TableName("often_drive_user")
public class OftenDriveUserDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 是否播报（0:不播报,1:播报）
     */
    private Integer reportStatus;

    /**
     * 开始时间(天之内的秒数)
     */
    private Integer startTime;

    /**
     * 结束时间（天之内的秒数）
     */
    private Integer endTime;
}
