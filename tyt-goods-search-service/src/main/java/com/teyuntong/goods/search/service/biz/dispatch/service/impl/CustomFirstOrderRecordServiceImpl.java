package com.teyuntong.goods.search.service.biz.dispatch.service.impl;

import com.teyuntong.goods.search.service.biz.dispatch.mapper.CustomFirstOrderRecordMapper;
import com.teyuntong.goods.search.service.biz.dispatch.service.CustomFirstOrderRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CustomFirstOrderRecordServiceImpl implements CustomFirstOrderRecordService {

    final CustomFirstOrderRecordMapper customFirstOrderRecordMapper;

    @Override
    public int countFinishOrder(String cellPhone) {
        return customFirstOrderRecordMapper.countFinishOrder(cellPhone);
    }
}
