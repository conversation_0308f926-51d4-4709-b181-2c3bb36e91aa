package com.teyuntong.goods.search.service.biz.goods.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 找货记录请求类
 *
 * <AUTHOR>
 * @since 2024/10/11 10:02
 */
@Getter
@Setter
public class SearchRecordQueryDTO {
    /**
     * 当前页数，默认1
     */
    private Integer currentPage = 1;
    /**
     * 默认分页30条
     */
    private Integer pageSize = 30;

    /**
     * 车主id
     */
    private Long userId;

    /**
     * 起始日期
     */
    private Date startDate;

    /**
     * 货源是否被出过价筛选条件，1是 0否
     */
    private Boolean quotedPriceOnce = false;

    /**
     * 是否浏览记录，1是 0否
     */
    private Boolean viewLog = false;

}
