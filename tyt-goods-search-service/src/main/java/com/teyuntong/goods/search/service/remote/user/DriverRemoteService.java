package com.teyuntong.goods.search.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.driver.service.DriverRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 查询用户权益
 *
 * <AUTHOR>
 * @since 2024/07/17 16:43
 */
@FeignClient(name = "tyt-user-service", path = "user", contextId = "DriverRemoteService",
        fallbackFactory = DriverRemoteService.DriverRemoteServiceFallback.class)
public interface DriverRemoteService extends DriverRpcService {

    @Component
    class DriverRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<DriverRemoteService> {
        public DriverRemoteServiceFallback() {
            super(true, DriverRemoteService.class);
        }
    }
}
