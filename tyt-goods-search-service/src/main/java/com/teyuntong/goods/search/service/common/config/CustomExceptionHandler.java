package com.teyuntong.goods.search.service.common.config;

import com.teyuntong.goods.search.service.common.error.GoodsSearchErrorCode;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.web.advice.ExceptionHandlerBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.client.circuitbreaker.NoFallbackAvailableException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/10/19 17:17
 */
@Slf4j
@RestControllerAdvice
public class CustomExceptionHandler extends ExceptionHandlerBase {

    @ExceptionHandler(NoFallbackAvailableException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    WebResult<Object> handleNoFallbackAvailableException(NoFallbackAvailableException exception) {
        log.error("服务内部异常: ", exception);
        String message = Optional.of(exception).map(Exception::getCause).map(Throwable::getMessage).orElse("");
        return WebResult.error(GoodsSearchErrorCode.BUSINESS_ERROR, message);
    }
}
