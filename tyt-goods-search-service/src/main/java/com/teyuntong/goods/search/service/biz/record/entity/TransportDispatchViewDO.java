package com.teyuntong.goods.search.service.biz.record.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 货源查看、联系统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@Getter
@Setter
@TableName("tyt_transport_dispatch_view")
public class TransportDispatchViewDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货源id
     */
    private Long srcMsgId;

    /**
     * 车主id
     */
    private Long carUserId;

    /**
     * 车主姓名
     */
    private String carUserName;

    /**
     * 车主昵称
     */
    private String carNickName;

    /**
     * 车主手机号
     */
    private String carPhone;

    /**
     * 备注
     */
    private String remark;

    /**
     * 备注人id
     */
    private Long remarkUserId;

    /**
     * 备注人姓名
     */
    private String remarkUserName;

    /**
     * 备注时间
     */
    private Date remarkTime;

    /**
     * 用户查看次数
     */
    private Integer viewCount;

    /**
     * 用户联系次数
     */
    private Integer contactCount;

    /**
     * 查看时间
     */
    private Date viewTime;

    /**
     * 联系时间
     */
    private Date contactTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}
