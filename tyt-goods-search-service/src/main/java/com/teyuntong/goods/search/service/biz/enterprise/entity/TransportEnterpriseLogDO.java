package com.teyuntong.goods.search.service.biz.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 开票货源企业信息记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Getter
@Setter
@TableName("tyt_transport_enterprise_log")
public class TransportEnterpriseLogDO {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货源ID
     */
    private Long srcMsgId;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 企业法人姓名
     */
    private String legalPersonName;

    /**
     * 企业法人手机号
     */
    private String legalPersonPhone;

    /**
     * 企业法人身份证号码
     */
    private String legalPersonCard;

    /**
     * 法人身份证URL国徽面
     */
    private String legalPersonCardUrlG;

    /**
     * 法人身份证URL头像面
     */
    private String legalPersonCardUrlT;

    /**
     * 企业认证统一社会信用代码
     */
    private String enterpriseCreditCode;

    /**
     * 企业类型
     */
    private String enterpriseType;

    /**
     * 企业经营范围
     */
    private String enterpriseBusinessScope;

    /**
     * 企业归属地
     */
    private String enterpriseHomeAddress;

    /**
     * 企业注册地址
     */
    private String enterpriseDetailAddress;

    /**
     * 营业执照URL
     */
    private String licenseUrl;

    /**
     * 营业执照开始时间
     */
    private Date licenseStartTime;

    /**
     * 营业执照到期时间
     */
    private Date licenseEndTime;

    /**
     * 道路经营许可证照片URL
     */
    private String transportLicenseUrl;

    /**
     * 授权激活类型（1法人授权;2委托书授权） 
     */
    private Integer signType;

    /**
     * 协议编号(同contract_number)
     */
    private String contractNo;

    /**
     * 协议开始时间
     */
    private Date contractStartTime;

    /**
     * 协议结束时间
     */
    private Date contractEndTime;

    /**
     * 授权人用户ID
     */
    private Long certigierUserId;

    /**
     * 授权人姓名
     */
    private String certigierUserName;

    /**
     * 授权人手机号（平台账号）
     */
    private String certigierUserPhone;

    /**
     * 企业发票税率
     */
    private BigDecimal enterpriseTaxRate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 开票主体ID
     */
    private Long invoiceSubjectId;

    /**
     * 开票服务商code
     */
    private String serviceProviderCode;
}
