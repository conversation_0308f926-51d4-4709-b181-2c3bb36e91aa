package com.teyuntong.goods.search.service.biz.exposure.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/07/16 09:56
 */
@Getter
@Setter
public class ExposureDataVO {

    /**
     * 最大货源id
     */
    private Long maxTsId;
    /**
     * 最小货源id
     */
    private Long minTsId;
    /**
     * 播报开关
     */
    private Integer pushStatus;
    /**
     * 客户端轮播秒数
     */
    private Integer intervalSecond;
    /**
     * 急走专区类型：1秒抢货源 2急走专区
     */
    private Integer exposureType;
    /**
     * 急走专区轮播数据
     */
    private List<ExposureVO> transportList;
}
