package com.teyuntong.goods.search.service.biz.record.service.impl;

import com.teyuntong.goods.search.service.biz.goods.dto.GoodsPhoneDTO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportMainDO;
import com.teyuntong.goods.search.service.biz.record.entity.UserCallPhoneRecordDO;
import com.teyuntong.goods.search.service.biz.record.mapper.UserCallPhoneRecordMapper;
import com.teyuntong.goods.search.service.biz.record.service.UserCallPhoneRecordService;
import com.teyuntong.goods.search.service.remote.activity.NewUserLifeRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * 获取电话记录表（货源与用户关系）每个用户一条数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserCallPhoneRecordServiceImpl implements UserCallPhoneRecordService {

    private final UserCallPhoneRecordMapper userCallPhoneRecordMapper;
    private final NewUserLifeRemoteService newUserLifeRemoteService;

    @Override
    public UserCallPhoneRecordDO getByUserAndSrcMsgId(Long userId, Long srcMsgId) {
        return userCallPhoneRecordMapper.getByUserAndSrcMsgId(userId, srcMsgId);
    }

    @Override
    public int getDistinctTsCountByUserId(Long userId, Date startTime, Date endTime) {
        return userCallPhoneRecordMapper.getDistinctTsCountByUserId(userId, startTime, endTime);
    }

    @Override
    public int saveRecord(GoodsPhoneDTO goodsPhoneDTO, LoginUserDTO user, TransportMainDO transportMainDO) {
        UserCallPhoneRecordDO userCallPhoneRecordDO = new UserCallPhoneRecordDO();
        userCallPhoneRecordDO.setUserId(user.getUserId());
        userCallPhoneRecordDO.setTsId(goodsPhoneDTO.getSrcMsgId());
        // 该字段太老，现在已不准，暂时先默认给一个，后续再优化掉。
        userCallPhoneRecordDO.setLevel(5);
        userCallPhoneRecordDO.setPath(goodsPhoneDTO.getPath());
        Integer clientSign = LoginHelper.getBaseParam().getClientSign();
        userCallPhoneRecordDO.setPlatId(clientSign == null ? "0" : clientSign.toString());
        userCallPhoneRecordDO.setCtime(new Date());
        if (StringUtils.isNotBlank(goodsPhoneDTO.getBenefitLabelCode())){
            userCallPhoneRecordDO.setBenefitLabelCode(goodsPhoneDTO.getBenefitLabelCode());
        }
        return userCallPhoneRecordMapper.insert(userCallPhoneRecordDO);

    }

    //    @Async
    @Override
    public void userActiveGift(Long userId) {
        try {
            newUserLifeRemoteService.userActiveGift(userId);
        } catch (Exception e) {
            log.error("用户激活赠送失败", e);
        }
    }

    @Override
    public int getDistinctTsCountBySrcMsgId(Long srcMsgId) {
        return userCallPhoneRecordMapper.getDistinctTsCountBySrcMsgId(srcMsgId);
    }
}
