package com.teyuntong.goods.search.service.biz.bi.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.bi.entity.DwsCarTypeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * bi车货匹配表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-29
 */
@Mapper
@DS("recommend")
public interface DwsCarTypeMapper extends BaseMapper<DwsCarTypeDO> {

    List<DwsCarTypeDO> getWeightMinByUserId(@Param("userId") String userId);
}
