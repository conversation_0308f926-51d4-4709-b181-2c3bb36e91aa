package com.teyuntong.goods.search.service.remote.order;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.trade.service.client.orders.service.TradeRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * trade 交易接口
 *
 * <AUTHOR>
 * @since 2024/08/13 10:50
 */
@FeignClient(name = "tyt-trade-service", path = "trade", contextId = "tradeRemoteService",
        fallbackFactory = TradeRemoteService.TradeRemoteServiceFallback.class)
public interface TradeRemoteService extends TradeRpcService {
    @Component
    class TradeRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<TradeRemoteService> {
        public TradeRemoteServiceFallback() {
            super(true, TradeRemoteService.class);
        }
    }
}
