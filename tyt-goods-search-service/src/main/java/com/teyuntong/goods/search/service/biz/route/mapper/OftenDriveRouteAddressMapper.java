package com.teyuntong.goods.search.service.biz.route.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.route.entity.OftenDriveRouteAddressDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 常跑路线地址表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Mapper
@DS("good_search")
public interface OftenDriveRouteAddressMapper extends BaseMapper<OftenDriveRouteAddressDO> {

    List<OftenDriveRouteAddressDO> getByRouteId(Long routeId);

    List<OftenDriveRouteAddressDO> getByRouteIds(@Param("routeIds") List<Long> routeIds);
}
