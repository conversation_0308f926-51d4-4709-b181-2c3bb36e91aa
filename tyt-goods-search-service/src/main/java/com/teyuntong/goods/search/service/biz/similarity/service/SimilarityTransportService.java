package com.teyuntong.goods.search.service.biz.similarity.service;

import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.goods.search.service.biz.similarity.dto.SimilarityQueryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/07/03 16:24
 */
public interface SimilarityTransportService {

    /**
     * 相似货源列表db查询
     */
    List<TransportDO> getSimilarityList(SimilarityQueryDTO dto);

    /**
     * 相似货源列表排序
     */
    void sortSimilarityList(List<? extends TransportVO> similarityVOList);
}
