package com.teyuntong.goods.search.service.biz.behavior.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.behavior.entity.TransportPreferenceRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 货源用户偏好记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Mapper
@DS("good_search")
public interface TransportPreferenceRecordMapper extends BaseMapper<TransportPreferenceRecordDO> {

}
