package com.teyuntong.goods.search.service.biz.goods.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 根据货源ID和车主ID获取技术服务费
 *
 * <AUTHOR>
 * @since 2024/06/27 13:26
 */
@Data
public class TecServiceFeeVO {

    /**
     * 技术服务费（折后，实际要付的钱）
     */
    private BigDecimal tecServiceFee;

    /**
     * 技术服务费（折前）
     */
    private BigDecimal tecServiceFeeAfterDiscount;

    /**
     * 技术服务费折前折后差值
     */
    private BigDecimal tecServiceFeeAfterDiscountDValue;
}
