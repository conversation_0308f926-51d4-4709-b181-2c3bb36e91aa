package com.teyuntong.goods.search.service.common.enums;

import lombok.Getter;

/**
 * 保存电话类型枚举类
 *
 * <AUTHOR>
 * @since 2024/05/29 13:45
 */
@Getter
public enum ActionTypeEnum {
    GET_PHONE(1, "获取电话"),
    SUCCESS_GET_PHONE(2, "获取电话成功"),
    FAIL_GET_PHONE(3, "获取电话失败"),
    CALL_PHONE(4, "拨打电话");

    private Integer code;
    private String desc;

    ActionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}