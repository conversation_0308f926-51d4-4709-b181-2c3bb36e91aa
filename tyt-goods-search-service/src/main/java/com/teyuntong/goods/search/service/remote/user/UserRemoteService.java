package com.teyuntong.goods.search.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.user.service.UserRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 查询用户权益
 *
 * <AUTHOR>
 * @since 2024/07/17 16:43
 */
@FeignClient(name = "tyt-user-service", path = "user", contextId = "userRemoteService",
        fallbackFactory = UserRemoteService.UserRemoteServiceFallback.class)
public interface UserRemoteService extends UserRpcService {

    @Component
    class UserRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<UserRemoteService> {
        public UserRemoteServiceFallback() {
            super(true, UserRemoteService.class);
        }
    }
}
