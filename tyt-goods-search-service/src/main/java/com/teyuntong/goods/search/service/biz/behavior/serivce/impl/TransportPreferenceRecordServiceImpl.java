package com.teyuntong.goods.search.service.biz.behavior.serivce.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.teyuntong.goods.search.service.biz.behavior.entity.TransportPreferenceRecordDO;
import com.teyuntong.goods.search.service.biz.behavior.mapper.TransportPreferenceRecordMapper;
import com.teyuntong.goods.search.service.biz.behavior.serivce.TransportPreferenceRecordService;
import com.teyuntong.goods.search.service.biz.goods.dto.TransportPreferenceDTO;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 货源用户偏好记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportPreferenceRecordServiceImpl implements TransportPreferenceRecordService {

    private final TransportPreferenceRecordMapper transportPreferenceRecordMapper;

    /**
     * 返回偏好记录
     *
     * @param srcMsgIdList 货源id集合
     * @param userId       用户id
     * @return 返回map，key=srcMsgId, value=status
     */
    @Override
    public Map<Long, Integer> getPreferenceMap(List<Long> srcMsgIdList, Long userId) {
        if (CollectionUtils.isEmpty(srcMsgIdList) || userId == null) {
            return Map.of();
        }
        List<TransportPreferenceRecordDO> recordDOList = transportPreferenceRecordMapper.selectList(
                new LambdaQueryWrapper<TransportPreferenceRecordDO>()
                        .eq(TransportPreferenceRecordDO::getUserId, userId)
                        .in(TransportPreferenceRecordDO::getSrcMsgId, srcMsgIdList));
        return recordDOList.stream().collect(Collectors.toMap(
                TransportPreferenceRecordDO::getSrcMsgId,
                TransportPreferenceRecordDO::getStatus));
    }

    /**
     * 设置货源偏好值
     *
     * @param transportList 货源集合
     * @param userId        用户id
     */
    @Override
    public void setPreference(List<? extends TransportVO> transportList, Long userId) {
        List<Long> srcMsgIdList = transportList.stream().map(TransportVO::getSrcMsgId).toList();
        Map<Long, Integer> preferenceMap = this.getPreferenceMap(srcMsgIdList, userId);
        for (TransportVO transportVO : transportList) {
            transportVO.setPreferenceStatus(preferenceMap.get(transportVO.getSrcMsgId()));
        }
    }

    /**
     * 保存偏好记录
     */
    @Override
    public void savePreference(TransportPreferenceDTO preferenceDTO) {
        TransportPreferenceRecordDO record = transportPreferenceRecordMapper.selectOne(
                new LambdaQueryWrapper<TransportPreferenceRecordDO>()
                        .eq(TransportPreferenceRecordDO::getSrcMsgId, preferenceDTO.getSrcMsgId())
                        .eq(TransportPreferenceRecordDO::getUserId, preferenceDTO.getUserId())
                        .last("limit 1"));
        if (record == null) {
            record = new TransportPreferenceRecordDO();
            record.setSrcMsgId(preferenceDTO.getSrcMsgId());
            record.setUserId(preferenceDTO.getUserId());
            record.setStatus(preferenceDTO.getStatus());
            record.setCtime(new Date());
            record.setMtime(new Date());
            transportPreferenceRecordMapper.insert(record);
        } else {
            record.setStatus(preferenceDTO.getStatus());
            record.setMtime(new Date());
            transportPreferenceRecordMapper.updateById(record);
        }
    }
}
