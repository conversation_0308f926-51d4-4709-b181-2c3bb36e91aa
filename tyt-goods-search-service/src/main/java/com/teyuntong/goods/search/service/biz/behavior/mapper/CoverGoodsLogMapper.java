package com.teyuntong.goods.search.service.biz.behavior.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.behavior.entity.CoverGoodsLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 捂货记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-14
 */
@Mapper
@DS("tyt")
public interface CoverGoodsLogMapper extends BaseMapper<CoverGoodsLogDO> {

    /**
     * 获取用户被捂货的货源id
     */
    Set<Long> getCoverGoodsIds(@Param("userId") Long userId, @Param("srcMsgIds") List<Long> srcMsgIds);
}
