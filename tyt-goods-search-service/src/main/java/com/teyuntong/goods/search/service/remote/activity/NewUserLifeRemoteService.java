package com.teyuntong.goods.search.service.remote.activity;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.market.activity.client.activity.service.NewUserLifeRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/01/16 13:34
 */
@Service
@FeignClient(name = "tyt-market-activity", path = "market-activity", contextId = "newUserLifeRpcService", fallbackFactory = NewUserLifeRemoteService.NewUserLifeRemoteFallbackFactory.class)
public interface NewUserLifeRemoteService extends NewUserLifeRpcService {

    @Component
    class NewUserLifeRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<NewUserLifeRemoteService> {
        protected NewUserLifeRemoteFallbackFactory() {
            super(true, NewUserLifeRemoteService.class);
        }
    }

}
