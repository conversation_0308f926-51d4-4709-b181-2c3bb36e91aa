package com.teyuntong.goods.search.service.biz.enterprise.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.enterprise.entity.TransportEnterpriseLogDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 开票货源企业信息记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Mapper
@DS("tyt")
public interface TransportEnterpriseLogMapper extends BaseMapper<TransportEnterpriseLogDO> {

    /**
     * 返回专票货源的开票主体ID
     */
    List<TransportEnterpriseLogDO> getInvoiceSubjectIdBySrcMsgIds(List<Long> srcMsgIds);

    /**
     * 根据货源id查询开票主体信息
     *
     * @param srcMsgId
     * @return
     */
    TransportEnterpriseLogDO selectBySrcMsgId(Long srcMsgId);
}
