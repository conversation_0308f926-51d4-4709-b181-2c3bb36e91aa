package com.teyuntong.goods.search.service.biz.groupchat.service.impl;

import cn.hutool.core.date.DateUtil;
import com.teyuntong.goods.search.service.biz.group.entity.DwsCarLabelDO;
import com.teyuntong.goods.search.service.biz.group.entity.TytGroupQrCodeDO;
import com.teyuntong.goods.search.service.biz.group.entity.TytUserGroupApplyDO;
import com.teyuntong.goods.search.service.biz.group.mapper.DwsCarLabelMapper;
import com.teyuntong.goods.search.service.biz.group.mapper.TytGroupQrCodeMapper;
import com.teyuntong.goods.search.service.biz.group.mapper.TytUserGroupApplyMapper;
import com.teyuntong.goods.search.service.biz.groupchat.service.GroupChatService;
import com.teyuntong.goods.search.service.biz.groupchat.vo.AppletEntranceVO;
import com.teyuntong.goods.search.service.biz.groupchat.vo.FindCityGroupQRVO;
import com.teyuntong.goods.search.service.remote.basic.ConfigRemoteService;
import com.teyuntong.goods.search.service.remote.user.UserRemoteService;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
@RequiredArgsConstructor
public class GroupChatServiceImpl implements GroupChatService {

    @Autowired
    private DwsCarLabelMapper dwsCarLabelMapper;

    @Autowired
    private TytGroupQrCodeMapper tytGroupQrCodeMapper;

    @Autowired
    private TytUserGroupApplyMapper tytUserGroupApplyMapper;

    @Autowired
    private UserRemoteService userRemoteService;

    @Autowired
    private ConfigRemoteService configRemoteService;

    @Override
    public FindCityGroupQRVO findCityGroupQR(String provinceCity, String city, Long userId) {
        //获取当前日期往前推7天
        Date startDate = DateUtil.offsetDay(new Date(), -7);
        startDate = DateUtil.beginOfDay(startDate);

        //用城市模糊查询，防止群管理后台录入格式错误导致查不出来
        TytGroupQrCodeDO tytGroupQrCodeDO = tytGroupQrCodeMapper.getGroupQRCodeByProvinceCity(city, startDate);
        if (tytGroupQrCodeDO != null) {
            FindCityGroupQRVO result = new FindCityGroupQRVO();
            BeanUtils.copyProperties(tytGroupQrCodeDO, result);
            result.setHaveQr(true);
            return result;
        }
        //该省市没有可用的群聊二维码
        FindCityGroupQRVO findCityGroupQRVO = new FindCityGroupQRVO();
        //用标准省市格式查询用户申请记录，因为申请记录只会是小程序端和后端交互，不会有人为因素
        TytUserGroupApplyDO joinByProvinceCityAndUserId = tytUserGroupApplyMapper.getJoinByProvinceCityAndUserId(provinceCity, userId);
        if (joinByProvinceCityAndUserId != null) {
            //已申请加群
            findCityGroupQRVO.setHaveApplyJoinedGroup(true);
            if (joinByProvinceCityAndUserId.getIsJoinedGroup() != null && joinByProvinceCityAndUserId.getIsJoinedGroup() == 1) {
                //已加群
                findCityGroupQRVO.setHaveJoinedGroup(true);
            }
        }
        return findCityGroupQRVO;
    }

    @Override
    public void reportedCity(String provinceCity, Long userId) {
        TytUserGroupApplyDO joinByProvinceCityAndUserId = tytUserGroupApplyMapper.getJoinByProvinceCityAndUserId(provinceCity, userId);
        if (joinByProvinceCityAndUserId != null) {
            log.info("上报申请加群 该用户已上报过该省市，不可重复上报 userId:{}", userId);
            return;
        }
        try {
            TytUserGroupApplyDO tytUserGroupApplyDO = new TytUserGroupApplyDO();
            tytUserGroupApplyDO.setApplyProvinceCity(provinceCity);

            UserRpcVO user = userRemoteService.getUser(userId);
            if (user == null) {
                log.info("上报申请加群 获取用户信息失败 userId:{}", userId);
                return;
            }

            tytUserGroupApplyDO.setUserId(userId);
            tytUserGroupApplyDO.setUserName(user.getTrueName() == null ? user.getUserName() : user.getTrueName());
            tytUserGroupApplyDO.setUserAccount(user.getCellPhone());
            tytUserGroupApplyDO.setCreateTime(new Date());
            tytUserGroupApplyDO.setIsJoinedGroup(0);

            DwsCarLabelDO dwsCarLabelDO = dwsCarLabelMapper.selectCarLabelByUserId(userId);
            if (dwsCarLabelDO != null) {
                tytUserGroupApplyDO.setTransportDistancePreference(dwsCarLabelDO.getDistanceHabit());
                tytUserGroupApplyDO.setUserIdentityLabel(dwsCarLabelDO.getCarUserFlag());
            }

            tytUserGroupApplyMapper.insert(tytUserGroupApplyDO);
        } catch (Exception e) {
            log.info("上报申请加群异常 原因：", e);
        }
    }

    @Override
    public AppletEntranceVO getAppletEntrance() {
        //小程序userName
        String goodsWechatAppletUserName = configRemoteService.getStringValue("goods_applet_entrance_car_username", "gh_22c870bbaca6");

        //小程序环境  0：正式 1：测试 2：体验
        Integer goodsAppletEnvironment = configRemoteService.getIntValue("goods_applet_environment", 2);

        //跳转路径
        String goodsAppletEntrancePath = configRemoteService.getStringValue("goods_applet_city_cluster_path", "pages/cityCluster/index");

        AppletEntranceVO appletEntranceVO = new AppletEntranceVO();
        appletEntranceVO.setAppletUserName(goodsWechatAppletUserName);
        appletEntranceVO.setAppletEnvironment(goodsAppletEnvironment);
        appletEntranceVO.setAppletPath(goodsAppletEntrancePath);
        return appletEntranceVO;
    }

}
