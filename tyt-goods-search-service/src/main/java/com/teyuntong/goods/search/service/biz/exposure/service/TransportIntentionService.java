package com.teyuntong.goods.search.service.biz.exposure.service;

import com.teyuntong.goods.search.service.biz.goods.dto.BaseTransportSearchDTO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;

import java.util.List;

/**
 * 意向货源池类
 *
 * <AUTHOR>
 * @since 2024/07/16 10:25
 */
public interface TransportIntentionService {

    /**
     * 获取意向货源池列表
     */
    List<TransportDO> getIntentionList(BaseTransportSearchDTO searchDTO);

}
