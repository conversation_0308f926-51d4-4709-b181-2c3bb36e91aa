package com.teyuntong.goods.search.service.biz.goods.dto.rule;

import lombok.Getter;
import lombok.Setter;

/**
 * 智能排序因子：好货规则
 * 格式：[{"level":0,"score":100},{"level":20,"score":80},{"level":21,"score":50},{"level":22,"score":20},{"level":0,"score":0}]
 * level是好货标签的值，value是分数。好货标签：11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2，0:其他
 *
 * <AUTHOR>
 * @since 2024/8/29 15:59
 */
@Getter
@Setter
public class IntelligentSortRuleLevel {
    /**
     * 好货标签
     */
    private Integer level;
    /**
     * 评分
     */
    private Integer score;

}
