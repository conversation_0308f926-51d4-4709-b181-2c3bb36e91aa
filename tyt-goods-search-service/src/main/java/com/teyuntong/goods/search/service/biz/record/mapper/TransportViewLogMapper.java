package com.teyuntong.goods.search.service.biz.record.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.record.entity.TransportViewLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 货物详情浏览日志表，每人每货仅存储一条 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Mapper
@DS("tyt")
public interface TransportViewLogMapper extends BaseMapper<TransportViewLogDO> {

    /**
     * 获取最近的浏览记录列表
     *
     * @param userId    车主id
     * @param startDate 开始时间
     */
    List<TransportViewLogDO> getRecentViewList(@Param("userId") Long userId, @Param("startDate") Date startDate);

    List<Long> getRecentViewListSrcMsgId(@Param("userId") Long userId, @Param("startDate") Date startDate);

    /**
     * 获取浏览记录所有的货源ID
     * @param userId 车主id
     * @param startDate 开始时间
     */
    List<Long> getRecentViewSrcMsgIdList(@Param("userId") Long userId, @Param("startDate") Date startDate);

    /**
     * 获取浏览记录总数
     * @param userId
     * @param startDate
     * @return
     */
    Integer getRecentCountByUserId(@Param("userId") Long userId, @Param("startDate") Date startDate);
}
