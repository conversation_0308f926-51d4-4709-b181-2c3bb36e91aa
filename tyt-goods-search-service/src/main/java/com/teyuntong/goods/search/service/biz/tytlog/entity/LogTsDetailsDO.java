package com.teyuntong.goods.search.service.biz.tytlog.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 货物详情浏览日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Getter
@Setter
@TableName("tyt_log_ts_details")
public class LogTsDetailsDO {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 货物ID
     */
    private Long tsId;

    /**
     * 客户端版本号
     */
    private String clientVersion;

    /**
     * 客户端标识 1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB
     */
    private Integer clientSign;

    /**
     * 状态 1有效（发布中），0无效，2待定（QQ专用），3阻止（QQ专用），4成交，5取消状态
     */
    private Integer status;

    /**
     * 时间
     */
    private Date ctime;
}
