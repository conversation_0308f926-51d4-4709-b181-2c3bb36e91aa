package com.teyuntong.goods.search.service.biz.route.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.route.entity.OftenDriveRouteDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 常跑路线主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Mapper
@DS("good_search")
public interface OftenDriveRouteMapper extends BaseMapper<OftenDriveRouteDO> {

    /**
     * 根据signCode获取常跑路线
     */
    OftenDriveRouteDO getBySignCode(String signCode);
}
