package com.teyuntong.goods.search.service.remote.activity;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.market.activity.client.activity.service.MarketingActivityRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/01/16 13:34
 */
@Service
@FeignClient(name = "tyt-market-activity", path = "market-activity", contextId = "marketingActivityRemoteService",
        fallbackFactory = MarketingActivityRemoteService.MarketingActivityRemoteServiceFallbackFactory.class)
public interface MarketingActivityRemoteService extends MarketingActivityRpcService {

    @Component
    class MarketingActivityRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<MarketingActivityRemoteService> {
        protected MarketingActivityRemoteServiceFallbackFactory() {
            super(true, MarketingActivityRemoteService.class);
        }
    }

}
