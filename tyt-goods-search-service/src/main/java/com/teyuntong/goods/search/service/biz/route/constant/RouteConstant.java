package com.teyuntong.goods.search.service.biz.route.constant;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/10/28 15:01
 */
public class RouteConstant {

    /**
     * 常跑路线最多条数
     **/
    public static final String OFTEN_ROUTE_MAX = "tyt:config:often_route_max";


    /**
     * 默认最大重量最小重量
     */
    public static final Integer DEFAULT_MAX_WEIGHT = 10000;
    public static final Integer DEFAULT_MIN_WEIGHT = -1;


    /**
     * 返回路线下货源数量的cache key: often_route:{yyyyMMdd}:newCount:{routeId}
     */
    public static String getRouteCountKey(Date date, Long routeId) {
        String dayStr = DateUtil.format(date, DatePattern.PURE_DATE_PATTERN);
        return "often_route:%s:newCount:%d".formatted(dayStr, routeId);
    }

    /**
     * 返回路线下货源id的cache key: often_route:{yyyyMMdd}:goods:{routeId}
     */
    public static String getRouteGoodsKey(Date date, Long routeId) {
        String dayStr = DateUtil.format(date, DatePattern.PURE_DATE_PATTERN);
        return "often_route:%s:goods:%d".formatted(dayStr, routeId);
    }
}
