package com.teyuntong.goods.search.service.biz.route.service;


import com.teyuntong.goods.search.service.biz.route.vo.MainSwitchVo;
import com.teyuntong.goods.search.service.biz.route.vo.UserOftenRouteVo;

import java.util.List;

/**
 * <p>
 * 常跑路线主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface OftenRouteService {

    /**
     * 设置总开关
     *
     * @param mainSwitchVo 总开关设置
     */
    void saveMainSwitch(MainSwitchVo mainSwitchVo);

    /**
     * 获取常跑路线总开关设置
     */
    MainSwitchVo getMainSwitch();

    /**
     * 重新加载路线货源，发送常跑路线消息
     *
     * @param routeId   路线id
     */
    void reloadRouteTransport(Long routeId);

    /**
     * 保存路线
     */
    void saveOftenRoute(UserOftenRouteVo userOftenRouteVo);

    /**
     * 设置路线播报开关
     *
     * @param routeUserId  路线用户id
     * @param reportStatus 播报状态
     */
    void setRouteSwitch(Long routeUserId, Integer reportStatus);

    /**
     * 设置路线启用开关
     *
     * @param routeUserIds 路线用户ids，多个用逗号隔开
     * @param enableStatus 启用状态
     */
    void setRouteEnable(String routeUserIds, Integer enableStatus);

    /**
     * 删除路线
     *
     * @param routeUserId 路线用户id
     */
    void deleteRoute(Long routeUserId);

    /**
     * 查看路线详情
     *
     * @param routeUserId 路线用户id
     */
    UserOftenRouteVo viewRoute(Long routeUserId);

    /**
     * 用户路线列表
     */
    List<UserOftenRouteVo> getUserRouteList();
}
