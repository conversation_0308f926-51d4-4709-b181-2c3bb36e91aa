package com.teyuntong.goods.search.service.rpc.certification.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 证件缺失提醒标题枚举
 *
 * <AUTHOR>
 * @since 2024/08/13 10:30
 */
@Getter
@AllArgsConstructor
public enum CertVerifyTypeEnum {

    TYPE_1(1, "证件缺失提醒", "APP详情页"),
    TYPE_2(2, "证件缺失提醒", "APP"),
    TYPE_3(3, "找货限制", "APP"),
    TYPE_4(4, "找货限制", "PC/小程序"),
    ;

    private final Integer code;
    private final String title;
    private final String location;

    public static CertVerifyTypeEnum getByCode(Integer code) {
        for (CertVerifyTypeEnum certVerifyTypeEnum : values()) {
            if (certVerifyTypeEnum.getCode().equals(code)) {
                return certVerifyTypeEnum;
            }
        }
        return null;
    }
}
