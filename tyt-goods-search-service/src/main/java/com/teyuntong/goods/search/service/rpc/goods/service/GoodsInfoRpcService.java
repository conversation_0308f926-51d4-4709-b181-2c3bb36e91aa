package com.teyuntong.goods.search.service.rpc.goods.service;

import com.teyuntong.goods.search.service.biz.goods.dto.GoodsInfoRecommendDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.GoodsPhoneDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.GoodsStatusDTO;
import com.teyuntong.goods.search.service.biz.goods.vo.GoodsPhoneVO;
import com.teyuntong.goods.search.service.biz.goods.vo.GoodsStatusVO;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.trade.service.client.orders.dto.CheckSeckillGoodsPayLimitDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/15 18:33
 */
public interface GoodsInfoRpcService {
    /**
     * 货源详情页推荐货源列表
     *
     * @param goodsInfoRecommendDTO
     * @return
     */
    List<TransportVO> InfoRecommendList(GoodsInfoRecommendDTO goodsInfoRecommendDTO);

    /**
     * 获取支付前货源状态
     *
     * @param goodsStatusDTO
     * @return
     */
    GoodsStatusVO getGoodsStatus(GoodsStatusDTO goodsStatusDTO, LoginUserDTO user);


    void checkSuperiorCarSign(Long userId);

    /**
     * 支付时校验是否可以支付专车货源
     *
     * @param goodsStatusVO
     * @param user
     */
    void checkSpecialCar(GoodsStatusVO goodsStatusVO, LoginUserDTO user);


    /**
     * 检查是否可以支付专车 1：可以 0：不可以
     *
     * @param srcMsgId
     * @return
     */
    Integer checkSpecialPayment(Long srcMsgId, Long userId);

    /**
     * 校验一段时间内该用户是否支付过专车货源
     *
     * @param userId
     * @return
     */
    boolean checkSpecialCarPayLimit(Long userId);

    /**
     * 获取货源电话
     *
     * @param goodsPhoneDTO
     * @param user
     * @return
     */
    GoodsPhoneVO getGoodsPhone(GoodsPhoneDTO goodsPhoneDTO, LoginUserDTO user);

    /**
     * 校验秒抢货源支付限制
     */
    CheckSeckillGoodsPayLimitDTO checkSeckillGoodsPayLimit(Long srcMsgId, Long userId);
}
