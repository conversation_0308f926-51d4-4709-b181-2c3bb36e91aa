package com.teyuntong.goods.search.service.biz.goods.vo;

import com.teyuntong.trade.service.client.orders.vo.AcceptOrderLimitInfo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/10/17 10:41
 */
@Data
public class GoodsStatusVO {
    /**
     * 货物id
     */
    private Long srcMsgId;
    /**
     * 货物的装货状态 0：货源已撤销或已被成交,是否可以支付:否  1：货源处于发布中，是否可以支付：是
     */
    private Integer goodStatus = 1;
    /**
     * 获取支付页面的url地址
     */
    private String payUrl;
    /**
     * 我的订单ID,支付过就会存在
     */
    private Long orderId;
    /**
     * 支付金额
     */
    private Double payAmount;

    /**
     * 运费
     */
    private Double carriageFee;

    /**
     * 技术服务费
     */
    private Double tecServiceFee;

    /**
     * 货源状态1有效（发布中），0无效（已过期），2待定（QQ专用），3阻止（QQ专用），4成交，5取消状态
     */
    private Integer status;
    /**
     * 货源发布时间
     */
    private String ctime;

    /**
     * 是否启用Tpay收银台版本，1:是 2:否
     */
    private String isTpayVersion;

    /**
     * 订单总金额(单位分)
     */
    private Double totalOrderAmount;

    /**
     * 优惠券金额(单位分)
     */
    private Double couponAmount;
    /**
     * 捂货到期时间
     */
    private Date priorityRecommendExpireTime;

    /**
     * 是否是优车货源（0:否 1：是）
     */

    private Integer excellentGoods;

    private Date pubDate;

    /**
     * 是否联系过货主  0：否  1：是
     */
    private Integer contacted;

    /**
     * 接单限制信息
     */
    private AcceptOrderLimitInfo acceptOrderLimitInfo;
    /**
     * 车头牌照头字母
     */
    private String headCity;
    /**
     * 车头牌照号码
     */
    private String headNo;
    /**
     * 挂车牌照头字母
     */
    private String tailCity;
    /**
     * 挂车牌照号码
     */
    private String tailNo;
    /**
     * 司机id
     */
    private Long driverId;
    /**
     * 车辆id
     */
    private Long carId;

    /**
     * 校验专车司机车辆 0:未通过 1:通过
     */
    private Integer checkCar;

    /**
     * 校验专车司机车辆 0:未通过 1:通过
     */
    private Integer checkDriver;

    /**
     * 划线价
     */
    private BigDecimal strikeThroughPrice;

    /**
     * 优惠价
     */
    private BigDecimal promotionPrice;

    /**
     * 开票主体ID，0表示本公司开票主体，其他的表示三方开票主体
     */
    private Long invoiceSubjectId;

    /**
     * 服务商编码：JCZY-自营 HBWJ-我家
     */
    private String serviceProviderCode;

    /**
     * 是否抽佣货源
     */
    private Boolean commissionTransport;

    /**
     * 出发地
     */
    private String startCity;

    /**
     * 是否可大厅抢单 0：否；1：是
     */
    private Integer declareInPublic;
    /**
     * 专车货源且出发地为XX的货源 提示语
     */
    private String specialCarStartPoint;
    /**
     * 司机姓名
     */
    private String driverName;
    /**
     * 司机电话
     */
    private String driverPhone;

    /**
     * 秒杀货源：1是0否
     */
    private Integer seckillGoods;

    /**
     * 专车签约合作商是否是平台 1:是；0:否
     */
    private Integer specialCarCooperativeIsNormal;

    /**
     * 运费补贴金额
     */
    private Integer perkPrice;



    /**
     * 出发地省
     */
    private String startProvinc;

    /**
     * 目的地省
     */
    private String destProvinc;

    /**
     * 目的地市
     */
    private String destCity;


    /**
     * 货物内容
     */
    private String taskContent;

    /**
     * 重量单位吨
     */
    private String weight;

    /**
     * 信息费（订金，元）
     */
    private BigDecimal infoFee;

    /**
     * 运费
     */
    private String price;

}
