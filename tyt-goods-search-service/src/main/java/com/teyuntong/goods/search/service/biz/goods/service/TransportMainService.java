package com.teyuntong.goods.search.service.biz.goods.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportMainDO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/07/15 17:14
 */
public interface TransportMainService extends IService<TransportMainDO> {

    /**
     * 根据货源id查询大厅货源
     */
    List<TransportMainDO> getBySrcMsgIds(List<Long> srdMsgIdList);


    List<Long> getFreeCommissionTransportBySrcMsgIdList(List<Long> allRecordSrcMsgIdList);

}
