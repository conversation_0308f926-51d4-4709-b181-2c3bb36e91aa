package com.teyuntong.goods.search.service.biz.similarity.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/07/04 10:24
 */
@Getter
@Setter
public class SimilarityQueryDTO {

    /**
     * 相似编码
     */
    @NotBlank(message = "相似编码不能为空")
    private String similarityCode;

    /**
     * 相似货源首发ID，已废弃，APP传0
     */
    @NotNull(message = "相似货源首发ID不能为空")
    private Long similarityFirstId;

    /**
     * 车主ID
     */
    private Long userId;

    /**
     * 需要过滤的货主id
     */
    private List<Long> excludeUserIds;

    /**
     * 需要返回的条数，默认30条
     */
    private Integer pageSize = 30;
}
