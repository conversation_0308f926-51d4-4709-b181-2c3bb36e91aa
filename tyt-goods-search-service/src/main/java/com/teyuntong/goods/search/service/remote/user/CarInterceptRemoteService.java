package com.teyuntong.goods.search.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.car.service.CarInterceptRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 查询用户权益
 *
 * <AUTHOR>
 * @since 2024/07/17 16:43
 */
@FeignClient(name = "tyt-user-service", path = "user", contextId = "CarInterceptRemoteService",
        fallbackFactory = CarInterceptRemoteService.CarInterceptRemoteServiceFallback.class)
public interface CarInterceptRemoteService extends CarInterceptRpcService {

    @Component
    class CarInterceptRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<CarInterceptRemoteService> {
        public CarInterceptRemoteServiceFallback() {
            super(true, CarInterceptRemoteService.class);
        }
    }
}
