package com.teyuntong.goods.search.service.biz.record.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.record.entity.TransportQuotedPriceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 无价货源报价表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Mapper
@DS("tyt")
public interface TransportQuotedPriceMapper extends BaseMapper<TransportQuotedPriceDO> {

    /**
     * 获取最近的出价记录列表
     *
     * @param userId    车主id
     * @param startDate 开始时间
     */
    List<TransportQuotedPriceDO> getRecentQuotedList(@Param("userId") Long userId, @Param("startDate") Date startDate);

    List<Long> getRecentQuotedListSrcMsgId(@Param("userId") Long userId, @Param("startDate") Date startDate);

    /**
     * 获取最近的出价记录数量
     * @param userId
     * @param startDate
     * @return
     */
    Integer getRecentCountByUserId(@Param("userId") Long userId, @Param("startDate") Date startDate);

    /**
     * 根据车主id和货源id获取报价记录
     */
    TransportQuotedPriceDO getByCarIdAndSrcMsgId(@Param("carUserId") Long carUserId, @Param("srcMsgId") Long srcMsgId);
}
