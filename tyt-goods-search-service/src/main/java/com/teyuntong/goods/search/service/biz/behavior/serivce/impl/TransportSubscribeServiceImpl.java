package com.teyuntong.goods.search.service.biz.behavior.serivce.impl;

import com.alibaba.fastjson2.JSON;
import com.teyuntong.goods.search.service.biz.behavior.dto.TransportSubscribeDTO;
import com.teyuntong.goods.search.service.biz.behavior.entity.TransportSubscribeDO;
import com.teyuntong.goods.search.service.biz.behavior.mapper.TransportSubscribeMapper;
import com.teyuntong.goods.search.service.biz.behavior.serivce.TransportSubscribeService;
import com.teyuntong.goods.search.service.remote.basic.TytCityRemoteService;
import com.teyuntong.goods.search.service.remote.order.OrderSnapshotRemoteService;
import com.teyuntong.infra.basic.resource.client.tytcity.vo.TytCityVo;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <p>
 * 用户订阅货源表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportSubscribeServiceImpl implements TransportSubscribeService {

    private final TransportSubscribeMapper subscribeMapper;
    private final TytCityRemoteService cityRemoteService;
    private final OrderSnapshotRemoteService orderSnapshotRemoteService;

    /**
     * 添加货源订阅
     */
    @Override
    public void addTransportSubscribe(TransportSubscribeDTO subscribeDTO) {
        log.info("添加订阅货源，入参：{}", JSON.toJSON(subscribeDTO));

        TransportSubscribeDO subscribeDO = new TransportSubscribeDO();
        subscribeDO.setUserId(LoginHelper.getRequiredLoginUser().getUserId());
        subscribeDO.setOrderId(subscribeDTO.getOrderId());

        TytCityVo startPoint = cityRemoteService.getByAddress(subscribeDTO.getStartPoint());
        if (startPoint != null) {
            subscribeDO.setStartProvince(startPoint.getProvinceName());
            subscribeDO.setStartCity(startPoint.getCityName());
            subscribeDO.setStartArea(startPoint.getAreaName());
            subscribeDO.setStartCoordX(new BigDecimal(startPoint.getPx()).multiply(BigDecimal.valueOf(100)).intValue());
            subscribeDO.setStartCoordY(new BigDecimal(startPoint.getPy()).multiply(BigDecimal.valueOf(100)).intValue());
        } else {
            log.warn("添加订阅货源，起始地未找到，入参：{}", JSON.toJSON(subscribeDTO));
        }
        subscribeDO.setStartPoint(subscribeDTO.getStartPoint());

        TytCityVo destPoint = cityRemoteService.getByAddress(subscribeDTO.getDestPoint());
        if (destPoint != null) {
            subscribeDO.setDestProvince(destPoint.getProvinceName());
            subscribeDO.setDestCity(destPoint.getCityName());
            subscribeDO.setDestArea(destPoint.getAreaName());
            subscribeDO.setDestCoordX(new BigDecimal(destPoint.getPx()).multiply(BigDecimal.valueOf(100)).intValue());
            subscribeDO.setDestCoordY(new BigDecimal(destPoint.getPy()).multiply(BigDecimal.valueOf(100)).intValue());
        } else {
            log.warn("添加订阅货源，目的地未找到，入参：{}", JSON.toJSON(subscribeDTO));
        }
        subscribeDO.setDestPoint(subscribeDTO.getDestPoint());

        subscribeDO.setLoadingDate(subscribeDTO.getLoadingTime());
        subscribeDO.setWeightMin(subscribeDTO.getWeightMin());
        subscribeDO.setWeightMax(subscribeDTO.getWeightMax());
        subscribeDO.setStatus(subscribeDO.getStartProvince() == null || subscribeDO.getDestPoint() == null ? 0 : 1);

        subscribeMapper.insert(subscribeDO);
        log.info("添加订阅货源，落库成功，货源订阅ID：{}", subscribeDO.getId());
        // 同步订单状态
        Integer i = orderSnapshotRemoteService.updateSub(subscribeDTO.getOrderId());
        log.info("添加订阅货源，调用更新订单订阅状态，订单id：{}，结果：{}", subscribeDTO.getOrderId(), i);
    }
}
