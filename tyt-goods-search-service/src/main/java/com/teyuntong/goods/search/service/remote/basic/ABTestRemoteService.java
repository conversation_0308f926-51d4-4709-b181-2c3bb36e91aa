package com.teyuntong.goods.search.service.remote.basic;

import com.teyuntong.infra.basic.resource.client.tytabtest.service.ABTestRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * AB测试远程调用
 *
 * <AUTHOR>
 * @since 2024/09/19 11:31
 */
@FeignClient(name = "tyt-infra-basic-resource", path = "basic-resource", contextId = "aBTestRemoteService",
        fallbackFactory = ABTestRemoteService.ConfigRemoteServiceFallback.class)
public interface ABTestRemoteService extends ABTestRpcService {

    @Component
    class ConfigRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<ABTestRemoteService> {
        public ConfigRemoteServiceFallback() {
            super(true, ABTestRemoteService.class);
        }
    }

    /**
     * 是否在ab测
     *
     * @param code   ab测code
     * @param userId 用户id
     */
    default boolean getInABTest(String code, Long userId) {
        if (StringUtils.isBlank(code) || userId == null) {
            return false;
        }
        Integer userType = this.getUserType(code, userId);
        return userType != null && userType > 0;
    }
}
