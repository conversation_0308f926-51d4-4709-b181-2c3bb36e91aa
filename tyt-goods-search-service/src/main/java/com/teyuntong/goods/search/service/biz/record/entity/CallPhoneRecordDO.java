package com.teyuntong.goods.search.service.biz.record.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 拨打电话记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Getter
@Setter
@TableName("call_phone_record")
public class CallPhoneRecordDO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货源id
     */
    private Long srcMsgId;

    /**
     * 拨打人id
     */
    private Long carUserId;

    /**
     * 拨打人姓名
     */
    private String carUserName;

    /**
     * 是否是车方vip 1.是2.否
     */
    private Integer carIsVip;

    /**
     * 路径
     */
    private String path;

    /**
     * 模块
     */
    private String module;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 客户端标识
     */
    private String platId;
}
