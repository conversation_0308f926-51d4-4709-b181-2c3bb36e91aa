package com.teyuntong.goods.search.service.biz.goods.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-06-03 20:08
 */
@Getter
@AllArgsConstructor
public enum IntelligentSortRuleTypeEnum {
    ADDRESS(1, "地址匹配度"),
    AGEING(2, "时效性"),
    INFO(3, "信息完整度"),
    MODEL(4, "质量分"),
    WEIGHT(5, "车货吨位匹配"),
    REFRESH(6, "刷新规则"),
    DISTANCE(7, "搜索条件与货源出发地目的地距离规则"),
    LEVEL(8, "好货规则"),
    PRICE(9, "公里单价规则"),
    ;

    private final Integer code;
    private final String name;

}
