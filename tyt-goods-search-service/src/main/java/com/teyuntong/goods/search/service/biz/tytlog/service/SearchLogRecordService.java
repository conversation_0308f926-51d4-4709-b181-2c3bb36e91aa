package com.teyuntong.goods.search.service.biz.tytlog.service;


import com.teyuntong.goods.search.service.biz.goods.dto.BaseTransportSearchDTO;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;

import java.util.List;

/**
 * <p>
 * 时间找货查询日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
public interface SearchLogRecordService {
    /**
     * @param baseTransportSearchDTO
     */
    void saveTsSearchLog(BaseTransportSearchDTO baseTransportSearchDTO, BaseParamDTO baseParam, List<TransportVO> transportVOList);
}
