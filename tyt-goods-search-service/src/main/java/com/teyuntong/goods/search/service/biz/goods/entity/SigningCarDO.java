package com.teyuntong.goods.search.service.biz.goods.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 宏信签约车辆
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-29
 */
@Getter
@Setter
@TableName("tyt_signing_car")
public class SigningCarDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 特运通账号
     */
    private String tytCellPhone;

    /**
     * 签约合作商 逗号分隔
     */
    private String signing;

    /**
     * 与调度账号合作次数
     */
    private Integer cooperateNum;

    /**
     * 指派次数
     */
    private Integer assignNum;

    /**
     * 指派成功次数
     */
    private Integer assignSuccessNum;

    /**
     * 接单率
     */
    private BigDecimal receivingOrders;

    /**
     * 好评率
     */
    private BigDecimal favorableComment;

    /**
     * 综合分值
     */
    private BigDecimal compreFraction;

    /**
     * 开关，接单状态 0关闭  1开启
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 经分标记距离
     */
    private String biDistance;
}
