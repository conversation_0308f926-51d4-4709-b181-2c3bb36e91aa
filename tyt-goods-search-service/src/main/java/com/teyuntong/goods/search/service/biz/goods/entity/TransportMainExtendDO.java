package com.teyuntong.goods.search.service.biz.goods.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 货源信息扩展表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Getter
@Setter
@TableName("tyt_transport_main_extend")
public class TransportMainExtendDO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货源ID
     */
    private Long srcMsgId;

    /**
     * 用车类型：1-整车，2-零担
     */
    private Integer useCarType;

    /**
     * 运价模式：1-固定运价，2-灵活运价
     */
    private Integer priceType;

    /**
     * 最低建议价，优车定价最低值，特惠优车价，=fixPriceMin
     */
    private Integer suggestMinPrice;

    /**
     * 最高建议价，优车定价最高值，极速优车价，=fixPriceMax
     */
    private Integer suggestMaxPrice;

    /**
     * 优车定价最快成交价格，快速优车价，=fixPriceFast
     */
    private Integer fixPriceFast;

    /**
     * 成本价，=thMinPrice
     */
    private Integer costPrice;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 好货模型分数
     */
    private BigDecimal goodModelScore;

    /**
     * 好货运价模型分数
     */
    private BigDecimal commissionScore;

    /**
     * 秒杀货源：1是0否
     */
    private Integer seckillGoods;

    /**
     * 运费补贴金额
     */
    private Integer perkPrice;

    /**
     * 好货标签 11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2，0:不符合
     */
    private Integer goodTransportLabel;

    /**
     * 货参不完整好货标签 11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2，0:不符合
     */
    private Integer goodTransportLabelPart;

    /**
     * 是否是融合发货 1是0否
     */
    private Integer clientFusion;
}
