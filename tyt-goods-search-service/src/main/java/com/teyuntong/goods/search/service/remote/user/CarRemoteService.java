package com.teyuntong.goods.search.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.car.service.CarRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 查询用户权益
 *
 * <AUTHOR>
 * @since 2024/07/17 16:43
 */
@FeignClient(name = "tyt-user-service", path = "user", contextId = "CarRemoteService",
        fallbackFactory = CarRemoteService.CarRemoteServiceFallback.class)
public interface CarRemoteService extends CarRpcService {

    @Component
    class CarRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<CarRemoteService> {
        public CarRemoteServiceFallback() {
            super(true, CarRemoteService.class);
        }
    }
}
