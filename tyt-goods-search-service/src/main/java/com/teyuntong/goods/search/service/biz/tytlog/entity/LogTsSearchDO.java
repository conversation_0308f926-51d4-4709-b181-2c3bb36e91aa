package com.teyuntong.goods.search.service.biz.tytlog.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 时间找货查询日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Getter
@Setter
@TableName("tyt_log_ts_search")
public class LogTsSearchDO {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * usreID 
     */
    private Long userId;

    /**
     * 出发地座标
     */
    private String startCoord;

    /**
     * 出发地范围
     */
    private Long startRange;

    /**
     * 目的地座标
     */
    private String destCoord;

    /**
     * 目的地范围
     */
    private Long destRange;

    /**
     * 车辆ID
     */
    private String carId;

    /**
     * 车头牌照号码
     */
    private String headNo;

    /**
     * 车头牌照头字母
     */
    private String headCity;

    /**
     * 客户端版本号
     */
    private String clientVersion;

    /**
     * 客户端标识1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB
     */
    private Integer clientSign;

    /**
     * 排序类型1时间，2距离，3智能
     */
    private String sortType;

    /**
     * 1首次列表查询2点击查询按钮查询
     */
    private String numberType;

    /**
     * 操作系统版本号（能获取到就填上）
     */
    private String osVersion;

    /**
     * 车辆长度
     */
    private String carLength;

    /**
     * 车辆类型
     */
    private String carType;

    /**
     * 特殊要求
     */
    private String specialRequired;

    /**
     * 终端唯一标识（能获取到就填上）
     */
    private String clientId;

    /**
     * 采集时间
     */
    private Date ctime;

    /**
     * 重量开始
     */
    private String startWeight;

    /**
     * 重量结束
     */
    private String endWeight;
    /**
     * 出发地省
     */
    private String startProvinc;
    /**
     * 出发地市
     */
    private String startCity;
    /**
     * 出发地区
     */
    private String startArea;
    /**
     * 目的地省
     */
    private String destProvinc;
    /**
     * 目的地省
     */
    private String destCity;
    /**
     * 目的地省
     */
    private String destArea;
    /**
     * 货源是否有价格（空不限；0无价；1有价）
     */
    private Integer priceFlag;

    /**
     * 货源类型（电议1，一口价2）
     */
    private Integer publishType;
    /**
     * 订金是否退还（0不退还；1退还）
     */
    private Integer refundFlag;
    /**
     * 货源类型：0普通货源；1优车货源；2专车货源
     */
    private Integer excellentGoods;

    /**
     * 货物名称，以英文逗号隔开
     */
    private String goodsName;
    /**
     * 货物尺寸长度最小值
     */
    private BigDecimal minLength;
    /**
     * 货物尺寸长度最大值
     */
    private BigDecimal maxLength;
    /**
     * 货物尺寸宽度最小值
     */
    private BigDecimal minWidth;
    /**
     * 货物尺寸宽度最大值
     */
    private BigDecimal maxWidth;
    /**
     * 货物尺寸高度最小值
     */
    private BigDecimal minHeight;
    /**
     * 货物尺寸高度最大值
     */
    private BigDecimal maxHeight;
    /**
     * 开始装车时间
     */
    private Date startLoadingTime;
    /**
     * 截止装车时间
     */
    private Date endLoadingTime;
    /**
     * 最小运价
     */
    private Integer minPrice;
    /**
     * 最大运价
     */
    private Integer maxPrice;
    /**
     * 返回给找货大厅的货源id集合
     */
    private String srcMsgIds;
    /**
     * 查询标记点，找货大厅、急走专区列表需要
     * queryType=0，查询大于querySign，为列表最大id
     * queryType=1，该字段无效
     * queryType=2，查询小于querySign，为列表最小id
     */
    private Long querySign;

}
