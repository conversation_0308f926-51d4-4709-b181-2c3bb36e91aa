package com.teyuntong.goods.search.service.biz.route.vo;

import com.teyuntong.goods.search.service.biz.route.enums.RouteMqTagEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

/**
 * 新增路线发送mq消息vo
 *
 * <AUTHOR>
 * @since 2024/11/05 14:42
 */
@Getter
@Setter
public class NewRouteMessageVo {

    /**
     * 消息的序列号
     **/
    protected String messageSerialNum;

    /**
     * 常跑路线tag
     */
    protected String tag;

    /**
     * 路线id
     **/
    private Long routeId;

    public NewRouteMessageVo(Long routeId) {
        this.messageSerialNum = UUID.randomUUID().toString().toUpperCase();
        this.tag = RouteMqTagEnum.NEW_ROUTE.getCode();
        this.routeId = routeId;
    }
}
