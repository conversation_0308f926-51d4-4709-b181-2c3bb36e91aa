package com.teyuntong.goods.search.service.rpc.exposure.service.impl;

import com.teyuntong.goods.search.service.biz.behavior.serivce.TransportPreferenceRecordService;
import com.teyuntong.goods.search.service.biz.exposure.converter.ExposureConverter;
import com.teyuntong.goods.search.service.biz.exposure.service.TransportExposureService;
import com.teyuntong.goods.search.service.biz.exposure.service.TransportIntentionService;
import com.teyuntong.goods.search.service.biz.exposure.vo.ExposureDataVO;
import com.teyuntong.goods.search.service.biz.exposure.vo.ExposureVO;
import com.teyuntong.goods.search.service.biz.goods.dto.TransportExposureQueryDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.TransportPreferenceDTO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.goods.es.entity.TransportEsDO;
import com.teyuntong.goods.search.service.biz.goods.es.service.EsTransportService;
import com.teyuntong.goods.search.service.biz.goods.service.TransportService;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.goods.search.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.search.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.search.service.remote.basic.ConfigRemoteService;
import com.teyuntong.goods.search.service.remote.basic.constant.TytConfigKey;
import com.teyuntong.goods.search.service.rpc.exposure.service.ExposureRpcService;
import com.teyuntong.goods.search.service.rpc.goods.service.SearchHallRpcService;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import static com.teyuntong.goods.search.service.common.constant.ConfigConstant.DEPOSIT_PAYMENT_PREFIX;

/**
 * 急走专区RPC服务
 *
 * <AUTHOR>
 * @since 2024/07/23 11:04
 */
@Service
@RequiredArgsConstructor
public class ExposureRpcServiceImpl implements ExposureRpcService {

    private final ConfigRemoteService configService;
    private final TransportService transportService;
    private final SearchHallRpcService searchHallRpcService;
    private final TransportPreferenceRecordService transportPreferenceRecordService;
    private final TransportExposureService transportExposureService;
    private final RedisUtil redisUtil;
    private final EsTransportService esTransportService;
    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 保存急走货源开关状态
     */
    @Override
    public void saveSwitch(Integer switchStatus) {
        LoginUserDTO loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return;
        }
        String redisKey = RedisKeyConstant.EXPOSURE_SWITCH_KEY + loginUser.getUserId();
        // 如果是关闭，就设置缓存
        if (Objects.equals(switchStatus, 0)) {
            // 有效期到第二天0点
            LocalDateTime midnight = LocalDateTime.of(LocalDate.now().plusDays(1), LocalTime.MIDNIGHT);
            redisUtil.set(redisKey, 1, Duration.between(LocalDateTime.now(), midnight));
        } else {
            redisUtil.delete(redisKey);
        }
    }

    /**
     * 获取曝光货源数据轮播数据
     */
    @Override
    public ExposureDataVO getHurryBannerList(TransportExposureQueryDTO searchDTO) {
        // 查询前的前置处理
        searchHallRpcService.beforeSearch(searchDTO, true);

        // 如果开关管理里关闭急走专区，或用户手动关闭急走专区，则返回空数据
        if (Objects.equals(configService.getIntValue(TytConfigKey.IS_SHOW_EXPOSURE), 0)
                || redisUtil.get(RedisKeyConstant.EXPOSURE_SWITCH_KEY + searchDTO.getUserId()) != null) {
            return buildExposureDataVO(null, null, 2, List.of());
        }

        // 优先展示秒抢货源，低版本不展示秒抢货源
        boolean lowVersion = isLowVersion();
        if (!lowVersion) {
            searchDTO.setSeckillGoods(1);
            List<TransportEsDO> transportEsDOS = esTransportService.searchSeckillBannerList(searchDTO);
            if (!transportEsDOS.isEmpty()) {
                List<ExposureVO> transportVOS = ExposureConverter.INSTANCE.convertEsDOs2VOs(transportEsDOS);
                // 加密货源字段
                transportService.handleTransportTag(transportVOS, searchDTO.getUserId());
                transportService.hideSensitiveInfo(transportVOS, searchDTO.getUserId());
                // 客户端进急走列表已经区分不开是秒抢好货还是急走货源了，需要服务端缓存记录。因为客户端是根据样式判断的，现在他俩样式一样了
                redisUtil.set(RedisKeyConstant.EXPOSURE_LIST_TYPE_KEY + searchDTO.getUserId() + ":" + searchDTO.getCarId(),
                        1, Duration.ofMinutes(10));

                // 6700需求：秒抢货源按倒计时排序，有倒计时的排在前面
                sortHurryBannerList(transportVOS);

                return buildExposureDataVO(null, null, 1, transportVOS);
            }
        }

        // 急走专区逻辑
        List<TransportDO> transportList = getTransportByHandle(searchDTO);
        List<ExposureVO> transportVOS = ExposureConverter.INSTANCE.convertDOs2VOs(transportList);

        // 加密货源字段
        transportService.handleTransportTag(transportVOS, searchDTO.getUserId());
        transportService.hideSensitiveInfo(transportVOS, searchDTO.getUserId());
        transportVOS.forEach(vo -> vo.setExposureFlag(1));

        // 异步保存货源曝光记录
        transportExposureService.saveExposureResult(transportVOS);

        // 客户端进急走列表已经区分不开是秒抢好货还是急走货源了，需要服务端缓存记录。因为客户端是根据样式判断的，现在他俩样式一样了
        redisUtil.set(RedisKeyConstant.EXPOSURE_LIST_TYPE_KEY + searchDTO.getUserId() + ":" + searchDTO.getCarId(),
                2, Duration.ofMinutes(10));

        return buildExposureDataVO(searchDTO.getMaxTsId(), searchDTO.getMinTsId(), 2, transportVOS);
    }

    /**
     * 秒抢货源轮播接口排序，开始倒计时的好货，在急走专区第1个显示，多个时随机即可
     */
    private void sortHurryBannerList(List<ExposureVO> transportVOS) {
        HashMap<String, Long> result = new HashMap<>();

        List<String> tsOrderNoList = transportVOS.stream().map(TransportVO::getTsOrderNo).toList();
        if (CollectionUtils.isNotEmpty(tsOrderNoList)) {
            // 构建所有需要查询的 Redis key
            List<String> keys = tsOrderNoList.stream().map(t -> DEPOSIT_PAYMENT_PREFIX + t).toList();

            // 批量获取 Redis 值
            List<String> values = stringRedisTemplate.opsForValue().multiGet(keys);

            // 处理结果
            if (values != null) {
                for (int i = 0; i < keys.size(); i++) {
                    String value = values.get(i);
                    if (StringUtils.isNotBlank(value)) {
                        result.put(tsOrderNoList.get(i), Long.parseLong(value));
                    }
                }
            }
        }
        if (result.isEmpty()) {
            return;
        }

        // 设置秒抢货源倒计时
        transportVOS.forEach(t -> {
            Long seckillEndMs = result.get(t.getTsOrderNo());
            if (seckillEndMs != null && seckillEndMs > System.currentTimeMillis()) {
                t.setSeckillCountdown((seckillEndMs - System.currentTimeMillis()) / 1000);
            }
        });

        // 把第一个有值的元素移动到第一位
        for (ExposureVO transportVO : transportVOS) {
            if (transportVO.getSeckillCountdown() != null) {
                transportVOS.remove(transportVO);
                transportVOS.add(0, transportVO);
                break;
            }
        }
    }

    /**
     * 低于6600版本返回true，默认返回低版本
     */
    private boolean isLowVersion() {
        BaseParamDTO baseParam = LoginHelper.getBaseParam();
        if (baseParam == null) {
            return true;
        }
        String clientVersion = baseParam.getClientVersion();
        if (StringUtils.isBlank(clientVersion) || clientVersion.length() < 4) {
            return true;
        }
        // pc: 586600 app: 6600
        return Integer.parseInt(clientVersion.substring(clientVersion.length() - 4)) < 6600;
    }

    /**
     * 构建banner返回体
     */
    private ExposureDataVO buildExposureDataVO(Long maxTsId, Long minTsId, Integer exposureType, List<ExposureVO> transportList) {
        ExposureDataVO exposureDataVo = new ExposureDataVO();
        exposureDataVo.setMaxTsId(maxTsId);
        exposureDataVo.setMinTsId(minTsId);
        exposureDataVo.setExposureType(exposureType);
        exposureDataVo.setTransportList(transportList);
        exposureDataVo.setPushStatus(1);
        exposureDataVo.setIntervalSecond(configService.getIntValue(TytConfigKey.EXPOSURE_INTERVAL_SECOND, 3));
        return exposureDataVo;
    }

    /**
     * 获取急走专区大厅列表
     */
    @Override
    public List<ExposureVO> getHurryList(TransportExposureQueryDTO searchDTO) {
        // 查询前的前置处理
        searchHallRpcService.beforeSearch(searchDTO, false);

        // 客户端进急走列表已经区分不开是秒抢好货还是急走货源了，需要服务端缓存记录。因为客户端是根据样式判断的，现在他俩样式一样了
        Integer exposureType = redisUtil.getInt(RedisKeyConstant.EXPOSURE_LIST_TYPE_KEY + searchDTO.getUserId() + ":" + searchDTO.getCarId());
        if (Objects.equals(exposureType, 1)) {
            searchDTO.setExposureType(1);
        }

        List<ExposureVO> transportVOS;
        // 如果是秒抢货源，展示秒抢
        if (Objects.equals(searchDTO.getExposureType(), 1)) {
            searchDTO.setSeckillGoods(1);
            List<TransportEsDO> transportEsDOS = esTransportService.searchHallList(searchDTO);
            transportVOS = ExposureConverter.INSTANCE.convertEsDOs2VOs(transportEsDOS);
        } else {
            List<TransportDO> exposureList = transportExposureService.getExposureList(searchDTO);
            transportVOS = ExposureConverter.INSTANCE.convertDOs2VOs(exposureList);
        }

        // 加密货源字段
        transportService.handleTransportTag(transportVOS, searchDTO.getUserId());
        transportService.hideSensitiveInfo(transportVOS, searchDTO.getUserId());

        return transportVOS;
    }

    /**
     * 返回货源，需要额外处理。如果在意向货源AB测中，返回意向货源。为空返回急走货源
     */
    private List<TransportDO> getTransportByHandle(TransportExposureQueryDTO searchDTO) {
        // 判断是否是第一页
        boolean isFirstPage = searchDTO.getMaxTsId() == null || searchDTO.getMaxTsId() == 0L;
        List<TransportDO> transportList = transportExposureService.getExposureList(searchDTO);

        if (transportList.isEmpty()) {
            // 不是第一页，如果数据为空，返回第一页数据
            if (!isFirstPage) {
                searchDTO.setMaxTsId(0L);
                searchDTO.setMinTsId(0L);
                transportList = transportExposureService.getExposureList(searchDTO);
            }
        }

        return transportList;
    }

    @Override
    public void savePreference(TransportPreferenceDTO preferenceDTO) {
        transportPreferenceRecordService.savePreference(preferenceDTO);
    }
}
