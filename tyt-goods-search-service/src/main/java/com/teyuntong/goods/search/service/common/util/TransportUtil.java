package com.teyuntong.goods.search.service.common.util;

import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.goods.search.service.common.enums.PublishGoodsTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 货源模块工具类
 *
 * <AUTHOR>
 * @since 2024/12/02 14:07
 */
public class TransportUtil {

    /**
     * 判断是否有价，货源表price是字符串。
     * <pre>
     *  TransportUtil.hasPrice(null)  = false
     *  TransportUtil.hasPrice("")    = false
     *  TransportUtil.hasPrice(" ")   = false
     *  TransportUtil.hasPrice("0")   = false
     *  TransportUtil.hasPrice("0.0") = false
     * </pre>
     */
    public static boolean hasPrice(String price) {
        if (StringUtils.isBlank(price)) {
            return false;
        }
        return new BigDecimal(price).compareTo(BigDecimal.ZERO) != 0;
    }

    /**
     * 格式化用户名
     */
    public static String formatUserName(String name, Long userId) {
        String userName;
        if (StringUtils.isNotBlank(name) && !"null".equals(name)) {
            userName = name;
        } else {
            userName = "用户" + userId;
        }
        return userName;
    }

    /**
     * 隐藏字符串中的手机号
     *
     * @param value
     * @return
     */
    public static String hidePhoneInStr(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }
        String reg = "\\d{11,}";
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(value);
        while (matcher.find()) {
            value = value.replace(matcher.group(), "***");
        }
        return value;
    }

    /**
     * 无价货源判断
     */
    public static boolean nonPrice(String price) {
        return !TransportUtil.hasPrice(price);
    }


    /**
     * 货源长宽高如果为空或=0或=1返回true
     */
    public static boolean isInvalidSize(String size) {
        if (StringUtils.isBlank(size)) {
            return true;
        }
        BigDecimal num = new BigDecimal(size);
        return num.compareTo(BigDecimal.ZERO) == 0 || num.compareTo(BigDecimal.ONE) == 0;
    }

    /**
     * 是否为有效的长宽高，长宽高不能为空，不能为0，不能为1，<=上限值
     *
     * @param size       长宽高字符串
     * @param upperLimit 上限
     */
    public static boolean isValidSize(String size, String upperLimit) {
        if (StringUtils.isNotBlank(size)) {
            BigDecimal num = new BigDecimal(size);
            return num.compareTo(BigDecimal.ZERO) != 0
                    && num.compareTo(BigDecimal.ONE) != 0
                    && num.compareTo(new BigDecimal(upperLimit)) <= 0;
        }
        return false;
    }

    /**
     * 货源有货参返回true
     */
    public static boolean isValidSize(String size) {
        return !isInvalidSize(size);
    }

    /**
     * 长宽高重都有且都不为1或0
     */
    public static boolean isValidSize(TransportVO transport) {
        return isValidSize(transport.getWeight())
                && isValidSize(transport.getLength())
                && isValidSize(transport.getWide())
                && isValidSize(transport.getHigh());
    }


    /**
     * 根据价格判断新版优车档位: fixPriceMin < fixPriceFast < fixPriceMax
     *
     * @param price        运费
     * @param fixPriceMin  特惠优车价
     * @param fixPriceMax  极速优车价
     * @param fixPriceFast 快速优车价
     */
    public static PublishGoodsTypeEnum judgeExcellentGoodsLevel(String price, Integer fixPriceMin, Integer fixPriceMax, Integer fixPriceFast) {
        if (nonPrice(price) || fixPriceMin == null || fixPriceMax == null || fixPriceFast == null) {
            return null;
        }
        int intPrice = Integer.parseInt(price);
        if (intPrice >= fixPriceMax) {
            return PublishGoodsTypeEnum.SUPER_QUICK_EXCELLENT_GOODS;
        } else if (intPrice >= fixPriceFast) {
            return PublishGoodsTypeEnum.QUICK_EXCELLENT_GOODS;
        } else if (intPrice >= fixPriceMin) {
            return PublishGoodsTypeEnum.EXCELLENT_GOODS;
        } else {
            return null;
        }
    }

    /**
     * 获取用户显示名称
     *
     * @param trueName 真实姓名
     * @param idCard   身份证号码
     * @param userName 昵称
     */
    public static String getUserShowName(String trueName, String idCard, String userName) {
        String userShowName;
        if (StringUtils.isNotBlank(trueName) && StringUtils.isNotBlank(idCard)) {
            userShowName = trueName.charAt(0) + IdCardUtil.getCallGender(idCard);
        } else {
            userShowName = userName;
        }
        return userShowName;
    }

}
