package com.teyuntong.goods.search.service.biz.route.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.route.entity.OftenDriveUserDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 常跑路线总开关 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Mapper
@DS("good_search")
public interface OftenDriveUserMapper extends BaseMapper<OftenDriveUserDO> {

    /**
     * 根据用户id查询常跑路线开关
     */
    OftenDriveUserDO getByUserId(Long userId);
}
