package com.teyuntong.goods.search.service.common.error;

import com.teyuntong.infra.common.definition.error.ErrorCodeBase;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 特运通错误码ErrorCode
 * <p>
 * （1）总共8位，1 2 位保留
 * （2）3 4 位代表具体的子模块
 * （3）5 6 7 8 位表示具体的业务
 * <p>
 * TODO 修改文件名
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum GoodsSearchErrorCode implements ErrorCodeBase {

    /**
     * 00042000   添加新错误码时变更后三位
     */
    BUSINESS_ERROR("00042000", "内部错误", "warn", false),
    ARGUMENTS_IS_NULL("00042001", "请求参数为空", "error", false),
    ILLEGAL_COORD("00042002", "非法坐标", "error", false),
    ARGUMENTS_IS_ERROR("00042003", "参数格式错误", "error", false),
    PAGE_SIZE_EXCEED("00042004", "单页条数超过最大限制", "error", false),
    CHECK_LIMIT_NO_PERMISSION("00042005", "有正在处罚的订单", "info", true),
    USER_AUTH_NO_IDENTITY("00042006", "未实名认证", "warn", false),
    SPECIAL_CAR_PAYMENT("00042007", "您已支付专车货源,是否仍要支付此单,支付后需按要求完成承运", "info", true),
    SUPERIOR_CAR_SIGN_BLACK_ERROR("00042008", "您当前的账号无法接优车货源，如有疑问请联系客服", "error", false),
    NEW_CAR_APP_REMARK("00042009", "您的拨打次数已达上限，需要进行车辆认证才可继续拨打。车辆认证有利于支付定金时选择车辆、且控制平台倒货等行为。", "info", true),
    NEW_CAR_PC_REMARK("00042010", "您的拨打次数已达上限，需要进行车辆认证才可继续拨打。车辆认证有利于支付定金时选择车辆、且控制平台倒货等行为。 请前往特运通车主版APP进行认证。", "info", true),
    GOODS_NOT_PUBLISH("00042011", "货源已下架", "info", true),
    FIX_NOT_PAY("00042012", "一口价货源不允许支付前拨打", "info", true),
    SECKILL_GOODS_PAYMENT("00042013", "该货源为秒抢货源，您已参与，等待抢单结果，<span style=\"color:#3194EF;\">预计%s公布结果。</span>", "info", true),
    ERROR_NO_TRANSPORT("00042014", "货源不存在", "error", false),

    // 常跑路线
    ROUTE_ADDRESS_ERROR("00043001", "地址格式不正确", "error", false),
    ROUTE_TIME_ERROR("00043002", "开始时间不能大于结束时间", "error", false),
    ROUTE_ARGUMENTS_ERROR("00043003", "常跑路线参数错误", "error", false),
    ROUTE_REPORT_AT_LEAST_ONE("00043004", "请保留一条货源播报", "error", false),
    ROUTE_COUNT_EXCEED("00043005", "超过最大路线条数", "error", false),
    ROUTE_HAS_EXIST("00043006", "该路线已添加至常跑路线", "error", false),
    ROUTE_ADDRESS_IS_NULL("00043007", "出发地/目的地不能为空", "error", false),
    ROUTE_ADDRESS_COUNT_EXCEED("00043008", "出发地/目的地最多三个", "error", false),

    // 屏蔽货主
    SHIELDING_USER_HAS_EXIST("00042101", "您已屏蔽过该发货人，请勿重复屏蔽！", "error", false),
    SHIELDING_USER_COUNT_EXCEED("00042102", "屏蔽发货人数量已达上限", "error", false),

    ;

    private final String code;
    private final String msg;
    private final String logLevel;
    private final boolean success;
}
