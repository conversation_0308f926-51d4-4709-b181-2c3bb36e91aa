package com.teyuntong.goods.search.service.biz.tytlog.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 倒短搜索日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Getter
@Setter
@TableName("tyt_log_fall_short_search")
public class LogFallShortSearchDO {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 出发地大地坐标
     */
    private String startCoord;

    /**
     * 范围
     */
    private String startDistance;

    /**
     * 出发地省
     */
    private String startProvinc;

    /**
     * 出发地市
     */
    private String startCity;

    /**
     * 出发地县
     */
    private String startArea;

    /**
     * 客户端版本号
     */
    private String clientVersion;

    /**
     * 客户端标识1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB
     */
    private Integer clientSign;

    /**
     * 1首次列表查询2点击查询按钮查询
     */
    private Integer numberType;

    /**
     * 操作系统版本号（能获取到就填上）
     */
    private String osVersion;

    /**
     * 终端唯一标识（能获取到就填上）
     */
    private String clientId;

    /**
     * 1.范围倒短 2.省内倒短
     */
    private Integer searchType;

    /**
     * 创建时间
     */
    private Date ctime;
}
