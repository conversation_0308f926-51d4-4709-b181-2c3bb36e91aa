package com.teyuntong.goods.search.service.biz.group.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 用户上报申请建群记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-28
 */
@Getter
@Setter
@TableName("tyt_user_group_apply")
public class TytUserGroupApplyDO {

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户账号（一般是手机号）
     */
    private String userAccount;

    /**
     * 用户身份标签
     */
    private String userIdentityLabel;

    /**
     * 运输距离偏好
     */
    private String transportDistancePreference;

    /**
     * 申请省市
     */
    private String applyProvinceCity;

    /**
     * 创建时间（申请时间）
     */
    private Date createTime;

    /**
     * 是否已加群 0：未加；1：已加
     */
    private Integer isJoinedGroup;
}
