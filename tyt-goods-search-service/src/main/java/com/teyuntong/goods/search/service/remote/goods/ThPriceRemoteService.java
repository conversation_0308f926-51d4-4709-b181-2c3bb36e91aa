package com.teyuntong.goods.search.service.remote.goods;

import com.teyuntong.goods.service.client.transport.service.ThPriceRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "ThPriceRpcService", fallbackFactory = ThPriceRemoteService.ThPriceRemoteServiceFallbackFactory.class)
public interface ThPriceRemoteService extends ThPriceRpcService {

    @Slf4j
    @Component
     class ThPriceRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<ThPriceRpcService> {
        protected ThPriceRemoteServiceFallbackFactory() {
            super(true, ThPriceRpcService.class);
        }
    }
}
