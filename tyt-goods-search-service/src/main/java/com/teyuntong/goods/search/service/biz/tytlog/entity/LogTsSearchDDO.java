package com.teyuntong.goods.search.service.biz.tytlog.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 时间找货距离排序日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Getter
@Setter
@TableName("tyt_log_ts_search_d")
public class LogTsSearchDDO {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 采集时间
     */
    private Date createTime;

    /**
     * 版本号
     */
    private String clientVersion;

    /**
     * 客户端标识 1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB
     */
    private Integer clientSign;

    /**
     * 排序类型1时间，2距离
     */
    private String sortType;
}
