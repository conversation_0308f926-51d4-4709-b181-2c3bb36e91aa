package com.teyuntong.goods.search.service.biz.route.vo;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/12/31 15:07
 */
@Getter
@Setter
public class UserOftenRouteVo {

    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 路线id
     */
    private Long routeId;

    /**
     * 最小重量
     */
    private Integer minWeight;

    /**
     * 最大重量
     */
    private Integer maxWeight;

    /**
     * 货物类型（逗号拼接）
     */
    private String goodsTypes;

    /**
     * 是否播报（0:不播报,1:播报）
     */
    private Integer reportStatus = 1;

    /**
     * 是否启用（1启用；0禁用）
     */
    private Integer enableStatus = 1;

    /**
     * 货源条数
     */
    private Integer count;

    /**
     * 出发地集合
     */
    @NotNull(message = "出发地不能为空")
    @Size(min = 1, max = 3, message = "出发地为1-3个")
    private List<RouteAddressVo> startAddressList;

    /**
     * 目的地集合
     */
    @NotNull(message = "目的地不能为空")
    @Size(min = 1, max = 3, message = "目的地为1-3个")
    private List<RouteAddressVo> destAddressList;

    /**
     * 签名
     */
    private String signCode;

    /**
     * 签名原内容
     */
    private String signContent;

    public void addStartAddress(RouteAddressVo addressVo) {
        if (startAddressList == null) {
            startAddressList = new ArrayList<>();
        }
        startAddressList.add(addressVo);
    }

    public void addDestAddress(RouteAddressVo addressVo) {
        if (destAddressList == null) {
            destAddressList = new ArrayList<>();
        }
        destAddressList.add(addressVo);
    }
}
