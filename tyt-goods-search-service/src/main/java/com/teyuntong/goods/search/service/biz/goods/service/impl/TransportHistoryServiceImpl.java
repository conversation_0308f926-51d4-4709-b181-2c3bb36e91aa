package com.teyuntong.goods.search.service.biz.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportHistoryDO;
import com.teyuntong.goods.search.service.biz.goods.mapper.TransportHistoryMapper;
import com.teyuntong.goods.search.service.biz.goods.service.TransportHistoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * transport表历史记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportHistoryServiceImpl implements TransportHistoryService {

    private final TransportHistoryMapper transportHistoryMapper;

    @Override
    public TransportHistoryDO getBySrcMsgIdAndOriginal(Long srcMsgId) {
        return transportHistoryMapper.getBySrcMsgIdAndOriginal(srcMsgId);
    }

    /**
     * 根据srcMsgId查询原始价格
     */
    @Override
    public Map<Long, String> getOriginalPrice(List<Long> srcMsgIds) {
        if (CollectionUtils.isEmpty(srcMsgIds)) {
            return Map.of();
        }
        Map<Long,String> map = new HashMap<>();
        List<TransportHistoryDO> originalPriceList = transportHistoryMapper.getOriginalPrice(srcMsgIds);
        if(CollUtil.isNotEmpty(originalPriceList)){
           for(TransportHistoryDO transportHistoryDO:originalPriceList){
               map.put(transportHistoryDO.getSrcMsgId(),transportHistoryDO.getPrice());
           }
        }
        return map;
    }


}
