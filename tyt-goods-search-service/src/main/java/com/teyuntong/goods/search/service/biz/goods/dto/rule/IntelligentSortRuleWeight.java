package com.teyuntong.goods.search.service.biz.goods.dto.rule;

import lombok.Getter;
import lombok.Setter;

/**
 * 智能排序因子：车货匹配吨重
 * 格式：[{"floatDown":0,"score":100},{"floatDown":20,"score":80},{"floatDown":30,"score":50},{"floatDown":40,"score":20},{"floatDown":40,"score":0},
 * {"floatUp":0,"score":100},{"floatUp":20,"score":80},{"floatUp":30,"score":50},{"floatUp":40,"score":20},{"floatUp":40,"score":0}]
 * floatDown向下浮动，floatDown=0完全匹配得分100，floatDown下浮动20%得发80，floatDown最后一个是浮动上限，超过40%得分0
 * floatUp向下浮动，floatUp=0完全匹配得分100，floatUp下浮动20%得发80，floatUp最后一个是浮动上限，超过40%得分0
 *
 * <AUTHOR>
 * @since 2024/8/29 15:59
 */
@Getter
@Setter
public class IntelligentSortRuleWeight {
    /**
     * 下浮动比例
     */
    private Float floatDown;
    /**
     * 上浮动比例
     */
    private Float floatUp;
    /**
     * 评分
     */
    private Integer score;

}
