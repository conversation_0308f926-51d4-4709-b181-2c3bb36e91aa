package com.teyuntong.goods.search.service.biz.dispatch.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.teyuntong.goods.search.service.biz.dispatch.entity.SpecialCarDispatchFailureDO;

import java.util.List;

/**
 * <p>
 * 专车自动派单无人接单货源表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public interface SpecialCarDispatchFailureService extends IService<SpecialCarDispatchFailureDO> {

    /**
     * 根据srcMsgIds查询
     */
    List<SpecialCarDispatchFailureDO> getBySrcMsgIds(List<Long> srcMsgIds);
}
