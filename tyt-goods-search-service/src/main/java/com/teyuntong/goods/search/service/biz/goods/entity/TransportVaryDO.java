package com.teyuntong.goods.search.service.biz.goods.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 运输信息变动表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-21
 */
@Getter
@Setter
@TableName("tyt_transport_vary")
public class TransportVaryDO {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 运输信息表ID
     */
    private Long tsId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 变动时间
     */
    private Date updateTime;
}
