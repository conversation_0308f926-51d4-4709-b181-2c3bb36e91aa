package com.teyuntong.goods.search.service.biz.goods.vo;

import com.teyuntong.goods.service.client.callphonerecord.vo.PrivacyPhoneTabInfo;
import com.teyuntong.trade.service.client.orders.vo.AcceptOrderLimitInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/10/17 10:41
 */
@Data
public class GoodsPhoneVO {
    /**
     * 联系人1
     */
    private String tel;
    /**
     * 联系人2
     */
    private String tel3;
    /**
     * 联系人3
     */
    private String tel4;
    /**
     * 发货人账号
     */
    private String uploadCellphone;

    /**
     * 装货联系电话
     */
    private String loadCellPhone;

    /**
     * 卸货联系电话
     */
    private String unloadCellPhone;
    /**
     * 货源id
     */
    private Long srcMsgId;
    /**
     * 运单号
     */
    private String tsOrderNo;
    /**
     * 是否需要支付定金
     */
    private String isInfoFee;
    /**
     * 电话标注，对应tyt_app_call_log的call_result_name
     */
    private String callStatus;
    /**
     * 对应tyt_app_call_log的call_result_code
     * 电话咨询信息的结果，货物模块定义如下值：
     *             1：达成交易 2：需要再沟通 3：价格没谈妥 4：电话打不通 5：虚假交易
     *             6：已经拉走 (具体值通过tyt_source查询，group_code值为app_call_result_code)'
     */

    private Integer callStatusCode = 0;
    private Integer isCanCall = 0;  //是否可以打电话，0可以，1不可以
    private String hasMakeOrder;// 是否下过单:0否、1是
    private Long userId;
    private Integer goodStatus;
    /**
     * 拨打电话时提示框提示信息
     */
    private String noGoodsMemberText;

    /**
     * 剩余次数
     */
    private Integer remainCallCount;

    /**
     * 接单限制信息
     */
    private AcceptOrderLimitInfo acceptOrderLimitInfo;
    /**
     * 虚拟号信息
     */
    private PrivacyPhoneTabInfo privacyPhoneTabInfo;


}
