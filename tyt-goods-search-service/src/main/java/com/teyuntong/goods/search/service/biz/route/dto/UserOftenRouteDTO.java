package com.teyuntong.goods.search.service.biz.route.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 常跑路线缓存参数
 *
 * <AUTHOR>
 * @since 2025/04/21 13:13
 */
@Getter
@Setter
public class UserOftenRouteDTO {

    /**
     * 最小重量
     */
    private Integer minW;

    /**
     * 最大重量
     */
    private Integer maxW;

    /**
     * 出发地集合
     */
    private List<RouteAddressDTO> startAddr;

    /**
     * 目的地集合
     */
    private List<RouteAddressDTO> destAddr;

    @Getter
    @Setter
    public static class RouteAddressDTO {
        private String p; // 省
        private String c; // 市
        private String a; // 区
    }
}

