/**
 * service 模块结构定义如下,包目录和结构是灵活的，可以按照自己的需要增加/删除。
 * <p>
 * ├─service                            -- service模块，处理业务的核心模块
 * │  ├─biz                               -- 业务相关的包
 * │  │  ├─业务A                            -- 一级业务，子业务不需要继续划分
 * │  │  │  ├─config                             -- 业务相关配置
 * │  │  │  ├─constant                           -- 业务相关常量
 * │  │  │  ├─es                                 -- 业务相关es
 * │  │  │  ├─mybatis                            -- 业务相关mybatis文件
 * │  │  │  │  ├─mapper                             -- mapper
 * │  │  │  │  ├─entity                             -- mapper对应的实体
 * │  │  │  ├─pojo                               -- 业务相关pojo类
 * │  │  │  ├─service                            -- 业务相关service
 * │  │  ├─业务B                            -- 一级业务，子业务不需要继续划分
 * │  │  │  ├─config                             -- 业务相关配置
 * │  │  │  ├─constant                           -- 业务相关常量
 * │  │  │  ├─es                                 -- 业务相关es
 * │  │  │  ├─mybatis                            -- 业务相关mybatis文件
 * │  │  │  │  ├─mapper                             -- mapper
 * │  │  │  │  ├─entity                             -- mapper对应的实体
 * │  │  │  ├─pojo                               -- 业务相关pojo类
 * │  │  │  ├─service                            -- 业务相关service
 * │  ├─common                              -- 通用工具
 * │  │  ├─aop                                   -- 切面包
 * │  │  ├─config                                -- 通用配置(web、redis、bean配置等)
 * │  │  ├─error                                 -- 业务异常
 * │  │  ├─filter                                -- 过滤器
 * │  │  ├─interceptor                           -- 拦截器
 * │  │  ├─util                                  -- 通用工具
 * │  ├─remote                              -- 调用其他服务的包, 可以包含一些熔断、限流逻辑
 * │  ├─rpc                                 -- 远程调用的实现包
 */

package com.teyuntong.goods.search.service;