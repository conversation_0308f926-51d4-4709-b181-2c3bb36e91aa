package com.teyuntong.goods.search.service.biz.record.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 找货记录返回实体
 *
 * <AUTHOR>
 * @since 2024/10/11 10:08
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties({"payStatus", "robStatus", "id", "pubQq", "userShowName", "linkman"})
@Getter
@Setter
public class SearchRecordVO extends TransportVO {

    /**
     * 货物宽单位米
     */
    private String width;

    /**
     * 货物高单位米
     */
    private String height;
    /*
     * 货物出发地
     */
    private String startPosition;
    /*
     * 货物目的地
     */
    private String destPosition;
    /*
     * 货物id
     */
    private String goodId;
    /*
     * 通话时间
     */
    private Date callTime;
    /*
     * 货物描述
     */
    private String remark;
    /*
     * 拨打电话结果码，1：达成交易 2：需要再沟通 3：价格没谈妥 4：电话打不通 5：虚假交易 6：已经拉走
     */
    private Integer callResultCode;
    /**
     * 电话标注中文
     */
    private String callResultName;
    /*
     * 被拨打电话的货物发布的时间，此字段在APP从列表进入标注页面的时候需要用到
     */
    private Date pubDate;
    /*
     * 当前用户是否对该货物拨打过电话标示 返回结构"是否打过电话状态码_通话标注状态码"，具体参看接口文档
     */
    private String callStatus;
    /**
     * 是否收信息费货源 0是不需要1是需要
     */
    private String isInfoFee;
    /**
     * 是否下过单:0否、1是
     */
    private String hasMakeOrder;

    /*
     * 是否可以打电话，0可以，1不可以
     */
    private Integer isCanCall = 0;
    /*
     * 超过电话拨打限制提示文字，不同的限制类型文本不同，该字段只有在超过拨打电话限制时才有值
     */
    private String promptContent;
    /*
     * 拨打电话限制类型，1：未进行车辆认证 2：未进行身份认证 3：试用期用户 4: 缴费到期
     * 5：超过所有限制，即车辆认证，身份认证，缴费的拨打电话次数都已用完
     */
    private Integer limitType;

    /**
     * 1：过期 2：撤销 3：成交
     */
    private Integer goodStatus;

    /**
     * 通话记录备注
     */
    private String reference;

    /**
     * 平台交易
     */
    private Integer tradeNums = 0;
    /**
     * 和我交易
     */
    private Integer coopNums = 0;
    /**
     * 货源持有人
     */
    private Long goodsUserId;

    /**
     * 支付状态：
     * 0待支付1支付失败2支付成功 3取消支付
     */
    private Integer payStatus;
    /**
     * 接单状态：
     * 0待接单 1接单成功  2货主拒绝 3系统拒绝  4同意装货 5车主装货完成  6系统装货完成 7异常上报 8货主撤销货源退款
     * 9系统撤销货源退款 10车主取销装货 11接单失败（用户同意别人装货，对没有支付成功的支付信息的操作状态）
     * 12车方取消订单 13.异常处理完成 15 车方已发起延迟付款
     */
    private Integer robStatus;
    /**
     * 是否显示详情页面的去支付按钮，0显示，1不显示
     */
    private Integer isPaySuccess = 0;
    /**
     * 已支付金额
     */
    private Integer payAgencyMoney;
    /**
     * 呼叫人ID
     */
    private Long callerId;

    /**
     * 是否可大厅抢单 0：否；1：是
     */
    private Integer declareInPublic = 0;

    /**
     * 货源是否有未处理的回价
     */
    private Boolean haveNewTransportQuotedPrice = false;

    /**
     * 对于该货源的出价是否以被同意
     */
    private Boolean haveAgreeTransportQuotedPrice = false;

    /**
     * 该货源相较于首次发货是否补充价格
     */
    private Boolean haveFillInPrice = false;

    /**
     * 该货源相较于首次发货是否加价
     */
    private Boolean haveAddPrice = false;

    /**
     * 是否跳过权益校验：1是，0否（默认）（比如：专车货源没有拨打权益的时候也能拨打和支付，普通货源指派专车时也能拨打和支付）
     */
    private Integer skipPermission = 0;

    /**
     * 最新沟通记录
     */
    private String lastCallFeedBack;

}
