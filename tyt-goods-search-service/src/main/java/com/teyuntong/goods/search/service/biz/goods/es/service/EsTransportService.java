package com.teyuntong.goods.search.service.biz.goods.es.service;

import com.teyuntong.goods.search.service.biz.goods.dto.BaseTransportSearchDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.IntelligenceSortDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.SearchHallCountDTO;
import com.teyuntong.goods.search.service.biz.goods.es.entity.TransportEsDO;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportEsVO;
import com.teyuntong.goods.search.service.biz.route.dto.OftenRouteSearchDTO;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/23 13:30
 */
public interface EsTransportService {

    /**
     * 智能排序
     *
     * @param intelligenceSortDTO
     * @return
     * @throws IOException
     */
    List<TransportEsVO> intelligenceSortList(IntelligenceSortDTO intelligenceSortDTO);

    /**
     * 智能排序数量
     *
     * @param intelligenceSortDTO
     * @return
     */
    Long intelligenceBubbleCount(IntelligenceSortDTO intelligenceSortDTO);

    /**
     * 找货大厅列表
     *
     * @param searchDTO
     * @return
     */
    List<TransportEsDO> searchHallList(BaseTransportSearchDTO searchDTO);

    /**
     * 秒抢货源banner列表，与searchHallList的区别是随机取10条数据
     *
     * @param searchDTO
     * @return
     */
    List<TransportEsDO> searchSeckillBannerList(BaseTransportSearchDTO searchDTO);

    /**
     * 根据srcMsgId查询ES货源
     */
    List<TransportEsDO> selectBySrcMsgIds(List<Long> srcMsgIdList);

    /**
     * 找货大厅时间排序气泡数量
     *
     * @param searchDTO
     * @return
     */
    Long searchHallCount(SearchHallCountDTO searchDTO);

    /**
     * 查询符合插入固定资源位的货源
     *
     * @param searchDTO
     * @return
     */
    List<TransportEsDO> searchFixedTransport(BaseTransportSearchDTO searchDTO);

    /**
     * 找货大厅气泡接口需要给客户端带回去一个货源详情，给他们提示展示，他们不好做
     *
     * @param searchDTO
     * @return
     */
    List<TransportEsDO> searchHallCountDetail(SearchHallCountDTO searchDTO);

    /**
     * 常跑路线气泡梳理
     */
    Long oftenRouteBubbleCount(OftenRouteSearchDTO searchDTO);

    /**
     * 常跑路线找货大厅列表
     */
    List<TransportEsDO> oftenRouteHallList(OftenRouteSearchDTO searchDTO);
}
