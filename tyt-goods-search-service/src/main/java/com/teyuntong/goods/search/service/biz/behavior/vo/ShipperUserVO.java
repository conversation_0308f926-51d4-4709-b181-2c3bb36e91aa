package com.teyuntong.goods.search.service.biz.behavior.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 屏蔽的货主信息
 *
 * <AUTHOR>
 * @since 2025/07/10 10:19
 */
@Getter
@Setter
public class ShipperUserVO {

    /**
     * 屏蔽的货主id
     */
    private Long userId;
    /**
     * 平台交易次数
     */
    private String tradeNums = "0";
    /**
     * 合作次数
     */
    private String coopNums = "0";
    /**
     * 信任分
     */
    private String transportScore;
    /**
     * 评级
     */
    private String tsRank;
    /**
     * 用户显示名称
     */
    private String userShowName;
    /**
     * 用户账号
     */
    private String callPhone;
    /**
     * 注册时间
     */
    private Date regTime;
    /**
     * 是否需要解密昵称，1 需要 2 不需要
     */
    private Integer isNeedDecrypt;
    /**
     * 用户昵称（加密）
     */
    private String nickName;
    /**
     * 车方信用等级
     */
    private String carCreditRankLevel;
    /**
     * 信用分
     */
    // private BigDecimal totalScore;
    /**
     * 信用分等级 "1 2 3 4 5"
     */
    // private Integer rankLevel;
}
