package com.teyuntong.goods.search.service.biz.similarity.converter;

import com.teyuntong.goods.search.service.biz.similarity.vo.SimilarityVO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/07/15 17:30
 */
@Mapper
public interface SimilarityRpcConverter {

    SimilarityRpcConverter INSTANCE = Mappers.getMapper(SimilarityRpcConverter.class);


    List<SimilarityVO> convertDOs2VOs(List<TransportDO> transportDOS);


}
