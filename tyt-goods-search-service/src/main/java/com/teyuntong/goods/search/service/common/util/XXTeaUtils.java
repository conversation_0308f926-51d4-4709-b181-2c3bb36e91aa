package com.teyuntong.goods.search.service.common.util;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;

/*
 * tea 加密算法
 */
public class XXTeaUtils {
    public static String Encrypt(String data, String key) {
        int[] s1 = toIntArray(PadRight(data, MIN_LENGTH).getBytes(StandardCharsets.UTF_8));
        int[] s2 = toIntArray(PadRight(key, MIN_LENGTH).getBytes(StandardCharsets.UTF_8));
        int[] r = TEAEncrypt(s1, s2);
        return ToHexString(r);
    }

    public static String Decrypt(String data, String key) {
        if (data == null || data.length() < MIN_LENGTH) {
            return data;
        }
        byte[] code = ToByteArray(TEADecrypt(
                toIntArray(data),
                toIntArray(PadRight(key, MIN_LENGTH).getBytes(StandardCharsets.UTF_8))
        ));
        return new String(code, StandardCharsets.UTF_8);
    }

    private static int[] toIntArray(String data) {
        int len = data.length() / 8;
        int[] result = new int[len];
        for (int i = 0; i < len; i++) {
            String sub = data.substring(i * 8, i * 8 + 8);
            long tt = new BigInteger(sub, 16).longValue();

            int iValue = (int) tt;
            result[i] = iValue;

        }
        return result;
    }

    public static int[] TEAEncrypt(int[] v, int[] k) {
        int n = v.length - 1;

        if (n < 1) {
            return v;
        }
        if (k.length < 4) {
            int[] key = new int[4];

            System.arraycopy(k, 0, key, 0, 4);
            k = key;
        }
        int z = v[n], y = v[0], delta = 0x9E3779B9, sum = 0, e;
        int p, q = 6 + 52 / (n + 1);

        while (q-- > 0) {
            sum = sum + delta;
            e = sum >>> 2 & 3;
            for (p = 0; p < n; p++) {
                y = v[p + 1];

                z = v[p] += (z >> 5 ^ y << 2) + (y >> 3 ^ z << 4)
                        ^ (sum ^ y) + (k[p & 3 ^ e] ^ z);
            }
            y = v[0];
            z = v[n] += (z >> 5 ^ y << 2) + (y >> 3 ^ z << 4)
                    ^ (sum ^ y) + (k[p & 3 ^ e] ^ z);
        }
        return v;
    }

    public static int[] TEADecrypt(int[] v, int[] k) {
        int n = v.length - 1;

        if (n < 1) {
            return v;
        }
        if (k.length < 4) {
            int[] key = new int[4];

            System.arraycopy(k, 0, key, 0, k.length);
            k = key;
        }
        int z = v[n], y = v[0], delta = 0x9E3779B9, sum, e;
        int p, q = 6 + 52 / (n + 1);

        sum = q * delta;
        while (sum != 0) {
            e = sum >>> 2 & 3;
            for (p = n; p > 0; p--) {
                z = v[p - 1];
                y = v[p] -= (z >> 5 ^ y << 2) + (y >> 3 ^ z << 4)
                        ^ (sum ^ y) + (k[p & 3 ^ e] ^ z);
            }
            z = v[n];
            y = v[0] -= (z >> 5 ^ y << 2) + (y >> 3 ^ z << 4)
                    ^ (sum ^ y) + (k[p & 3 ^ e] ^ z);
            sum = sum - delta;
        }
        return v;
    }

    private static int[] toIntArray(byte[] data) {
        int n = (((data.length & 3) == 0)
                ? (data.length >>> 2)
                : ((data.length >>> 2) + 1));
        int[] result;
        result = new int[n];
        n = data.length;
        for (int i = 0; i < n; i++) {
            result[i >>> 2] |= (0x000000ff & data[i]) << ((i & 3) << 3);
        }
        return result;
    }


    private static byte[] ToByteArray(int[] data) {
        int n = data.length << 2;

        byte[] result = new byte[n];

        for (int i = 0; i < n; i++) {
            result[i] = (byte) ((data[i >>> 2] >>> ((i & 3) << 3)) & 0xff);
        }
        return result;
    }


    private static String ToHexString(int[] data) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < data.length; i++) {
            sb.append(PadLeft(Integer.toHexString(data[i]), 8));
        }
        return sb.toString();
    }


    private static String PadRight(String source, int length) {
        while (source.length() < length) {
            source += SPECIAL_CHAR;
        }
        return source;
    }

    private static String PadLeft(String source, int length) {
        while (source.length() < length) {
            source = '0' + source;
        }
        return source;
    }


    private static final int MIN_LENGTH = 8;
    private static final char SPECIAL_CHAR = '\0';

    public static void main(String[] args) {
        String k = "a3+@$@$!~!#222444";
        String content = null;
        System.out.println("加密前：" + content);
        String enContent = XXTeaUtils.Encrypt(content, k);
        System.out.println("加密后：" + enContent);
        String d = "2cb35ee5fc748527ed9130da56446f61a8c2cd63f3425934eafcb28f475cd710860601c674b249b2e75e25a4eb9bdfd0b03231c3ca33251a4982c53f49f45a07ad32e882b9a0b93ddd4277e8d9bea46ff72a5695df17117b9423fac98f25f5c9148168fe8f882d175d7c4079e2a6492c00b891bf726b14337c2f23c5b1144b8915da508344fd655248d8b43533cf1219b7f8fb3e55a70a03da14574a84fd8814ee6e8156980a7beb892fa5efc89ec6f7f4149bd9c85c540189491808e56f35382e2db02284295ef0782c71df2362320000872532a3bdd6c9ad4db527a4836d536738ee3caf326a2db4fa95157e5c11d9c1ed6cde5b46be5c99a802f89af350c98c5b2f190cce6c6cc90628156deb39c589bb9a043ba4a577504750392828c21da7ea690d3069fe4079f125928dad76934e0b3cb921bf11106bc5763b51d58852ac935738754b87cda0ef2543d357ad4c9fb21a50976f99a5ff1db8abafa7d055bd43e75790eede5727c21ad5941427608f4bd549c21f47707841e7a5b9c4689a8d686382990985bb0bce2f4e637a8c38f279000077a543d055e1769e8817452facdd25ba8b178b2bf802b484582a5487b0862f0ed952c381b7277d4c69ada3ecf6bf90f778119e339596089770e3f07239bd6580aae4c52049b305eb9f9afb5d84eca2cf7c17b61ac4b9bce9ab316f386b22b31b2ffb8a82c44e3c9949a2fddc948a535aa8162c8f0fb325c8429c675a851bf4ada1096e695fa4c9cf3cbb6f5827e9d57783ccdc1c7995c481647694860207b983";
        String e = XXTeaUtils.Decrypt(enContent, k);
        System.out.println("解密后：" + e);
    }
}

