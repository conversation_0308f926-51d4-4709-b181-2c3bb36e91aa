package com.teyuntong.goods.search.service.biz.goods.dto.rule;

import lombok.Getter;
import lombok.Setter;

/**
 * 智能排序因子：距离规则
 * 格式：{"start":[{"distance":5,"score":100},{"distance":15,"score":20}], "dest":[{"distance":5,"score":100},{"distance":15,"score":20}]}
 * 分 start 出发地 和 dest 目的地，distance是距离，从小到大排序，如距离<=5km，赋值100分，<=15km，赋值20分
 *
 * <AUTHOR>
 * @since 2024/8/29 15:59
 */
@Getter
@Setter
public class IntelligentSortRuleDistance {
    /**
     * 距离
     */
    private Float distance;
    /**
     * 评分
     */
    private Integer score;

}
