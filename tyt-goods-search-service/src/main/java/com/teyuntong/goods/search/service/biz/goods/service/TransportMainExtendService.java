package com.teyuntong.goods.search.service.biz.goods.service;

import com.teyuntong.goods.search.service.biz.goods.entity.TransportMainExtendDO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 开票货源企业信息记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
public interface TransportMainExtendService {

    /**
     * 返回srcMsgId对应的map
     */
    Map<Long, TransportMainExtendDO> getMapBySrcMsgIds(List<Long> srcMsgIds);

    /**
     * 根据srcMsgId查询
     */
    List<TransportMainExtendDO> getBySrcMsgIds(List<Long> srcMsgIds);

    /**
     * 根据srcMsgId查询
     */
    TransportMainExtendDO getBySrcMsgId(Long srcMsgId);
}
