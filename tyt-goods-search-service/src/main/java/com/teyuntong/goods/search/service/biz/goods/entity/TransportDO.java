package com.teyuntong.goods.search.service.biz.goods.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.teyuntong.goods.search.service.common.util.MapPointUtils;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 运输信息表主表
 *
 * <AUTHOR>
 * @since 2024/07/03 15:25
 */
@Getter
@Setter
@TableName("tyt_transport")
public class TransportDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 出发地(省市区以减号-分割开)
     */
    private String startPoint;

    /**
     * 目的地(省市区以减号-分割开)
     */
    private String destPoint;

    /**
     * 货物内容
     */
    private String taskContent;

    /**
     * 联系人
     */
    private String tel;

    /**
     * 发布时间
     */
    private String pubTime;

    /**
     * qq
     */
    private Long pubQq;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 用户认证昵称
     */
    private String userShowName;

    /**
     * 状态 1有效（发布中），0无效（已过期），2待定（QQ专用），3阻止（QQ专用），4成交，5取消状态
     */
    private Integer status;

    /**
     * 人工/自动
     */
    private Integer source;

    /**
     * 采集时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;

    /**
     * 上传电话
     */
    private String uploadCellphone;

    /**
     * 重发时间
     */
    private Integer resend;

    /**
     * 出发地坐标
     */
    private String startCoord;

    /**
     * 目的地坐标
     */
    private String destCoord;

    /**
     * 终端标识
     */
    private Integer platId;

    /**
     * 验证标识
     */
    private Integer verifyFlag;

    /**
     * 运费
     */
    private String price;

    /**
     * 发布人usreID 关联tyt_user表id
     */
    private Long userId;

    /**
     * 运费代码
     */
    private String priceCode;

    /**
     * 出发地坐标x
     */
    private Integer startCoordX;

    /**
     * 出发地坐标y
     */
    private Integer startCoordY;

    /**
     * 目的地坐标x
     */
    private Integer destCoordX;

    /**
     * 目的地坐标y
     */
    private Integer destCoordY;

    /**
     * 出发地详细地址
     */
    private String startDetailAdd;

    /**
     * 出发地经度
     */
    private Integer startLongitude;

    /**
     * 出发地纬度
     */
    private Integer startLatitude;

    /**
     * 目的地详细地址
     */
    private String destDetailAdd;

    /**
     * 目的地经度
     */
    private Integer destLongitude;

    /**
     * 目的地纬度
     */
    private Integer destLatitude;

    /**
     * 发布日期
     */
    private Date pubDate;

    /**
     * 货物代码 与 货物对应 task_content 字段是一对
     */
    private String goodsCode;

    /**
     * 重量代码
     */
    private String weightCode;

    /**
     * 重量单位吨
     */
    private String weight;

    /**
     * 货物长单位米
     */
    private String length;

    /**
     * 货物宽单位米
     */
    private String wide;

    /**
     * 货物高单位米
     */
    private String high;

    /**
     * 是否三超 0未超1超
     */
    private String isSuperelevation;

    /**
     * 联系人
     */
    private String linkman;

    /**
     * 备注
     */
    private String remark;

    /**
     * 出发地目的地之间距离
     */
    private Integer distance;

    /**
     * 发货日期
     */
    private Date pubGoodsTime;

    /**
     * 联系人电话3
     */
    private String tel3;

    /**
     * 联系人电话4
     */
    private String tel4;

    /**
     * 显示类型 0不显示  1显示
     */
    private String displayType;

    /**
     * hash_code
     */
    private String hashCode;

    /**
     * 是否完善了车的信息 车头行驶本认证状态0:未认证；1:认证成功；2：认证失败
     */
    private String isCar;

    /**
     * 用户类型0试用 1付费 2未激活
     */
    private Integer userType;

    /**
     * PC旧版本未拆分的数据
     */
    private String pcOldContent;

    /**
     * 重发次数
     */
    private Integer resendCounts;

    /**
     * 照片认证标志0未认证1通过2认证中3认证失败
     */
    private Integer verifyPhotoSign;

    /**
     * 用户分数
     */
    private Integer userPart;

    /**
     * 出发地城市
     */
    private String startCity;

    /**
     * 重发后原信息ID
     */
    private Long srcMsgId;

    /**
     * 出发地省
     */
    private String startProvinc;

    /**
     * 出发地区
     */
    private String startArea;

    /**
     * 目的地省
     */
    private String destProvinc;

    /**
     * 目的地市
     */
    private String destCity;

    /**
     * 目的地区
     */
    private String destArea;

    /**
     * 客户端版本号
     */
    private String clientVersion;

    /**
     * 是否收信息费货源   0是不需要1是需要
     */
    private String isInfoFee;

    /**
     * 信息费运单状态：0待接单  1有人支付成功 （货主的待同意）2装货中（车主是待装货）3车主装货完成  4系统装货完成 5异常上报
     */
    private String infoStatus;

    /**
     * 运单号
     */
    private String tsOrderNo;

    /**
     * 第一次发布时间
     */
    private Date releaseTime;

    /**
     * 发货人注册时间
     */
    private Date regTime;

    /**
     * 货物型号
     */
    private String type;

    /**
     * 货物品牌
     */
    private String brand;

    /**
     * 货物类型名称,如“装载机”，“挖掘机”
     */
    private String goodTypeName;

    /**
     * 货物的台数，针对标准化的数据
     */
    private Integer goodNumber;

    /**
     * 是否是标准化数据：0是，1不是
     */
    private Integer isStandard;

    /**
     * 匹配项的ID，针对标准化的数据
     */
    private Integer matchItemId;

    /**
     * android两点距离
     */
    private Integer androidDistance;

    /**
     * IOS两点距离
     */
    private Integer iosDistance;

    /**
     * 是否展示在找货列表 0不显示，1是显示
     */
    private Integer isDisplay;

    /**
     * 参考长度 乘100
     */
    private Integer referLength;

    /**
     * 参考宽度 乘100
     */
    private Integer referWidth;

    /**
     * 参考高度 乘100
     */
    private Integer referHeight;

    /**
     * 参考重量 乘100
     */
    private Integer referWeight;

    private Date changeTime;

    /**
     * 车辆长度
     */
    private String carLength;

    /**
     * 装车时间
     */
    private Date loadingTime;

    /**
     * 卸货起始时间
     */
    private Date beginUnloadTime;

    /**
     * 卸车时间
     */
    private Date unloadTime;

    /**
     * 车辆最低长度，单位米
     */
    private BigDecimal carMinLength;

    /**
     * 车辆最大长度，单位米
     */
    private BigDecimal carMaxLength;

    /**
     * 车辆类型
     */
    private String carType;

    /**
     * 装货起始时间
     */
    private Date beginLoadingTime;

    /**
     * 挂车样式
     */
    private String carStyle;

    /**
     * 工作面高最小值，单位米
     */
    private BigDecimal workPlaneMinHigh;

    /**
     * 工作面高最大值，单位米
     */
    private BigDecimal workPlaneMaxHigh;

    /**
     * 工作面长最小值，单位米
     */
    private BigDecimal workPlaneMinLength;

    /**
     * 工作面长最大值，单位米
     */
    private BigDecimal workPlaneMaxLength;

    /**
     * 是否需要爬梯
     */
    private String climb;

    /**
     * 订单量
     */
    private Integer orderNumber;

    /**
     * 好评度
     */
    private Integer evaluate;

    /**
     * 特殊要求
     */
    private String specialRequired;

    /**
     * 相似编码
     */
    private String similarityCode;

    /**
     * 相似货源首发ID
     */
    private Long similarityFirstId;

    /**
     * 相似货源首发信息
     */
    private String similarityFirstInfo;

    /**
     * 轮胎外露标识(1是;0否)
     */
    private String tyreExposedFlag;

    /**
     * 车辆长度标签
     */
    private String carLengthLabels;

    /**
     * 调车数量
     */
    private Integer shuntingQuantity;

    /**
     * 货源类型（电议1，一口价2）
     */
    private Integer firstPublishType;

    /**
     * 货源类型（电议1，一口价2）
     */
    private Integer publishType;

    /**
     * 信息费（订金，元）
     */
    private BigDecimal infoFee;

    /**
     * 是否删除 0：未删除 1已删除
     */
    private Integer isDelete;

    /**
     * 是否是 17.5 米专享 0：否 1：是
     */
    private Integer exclusiveType;

    /**
     * 信用分
     */
    private BigDecimal totalScore;

    /**
     * 信用分等级_new
     */
    private Integer rankLevel;

    /**
     * 是否显示在找货大厅（企业货源）0.不显示 1.显示
     */
    private Integer isShow;

    /**
     * 订金是否退还（0不退还；1退还）
     */
    private Integer refundFlag;

    /**
     * 货源来源（1货主；2调度客服；3个人货主;4:运满满货源；5：宏信货源）
     */
    private Integer sourceType;

    /**
     * 用户发布货源成交个数(来自user_sub表的deal_num)
     */
    private Integer tradeNum;

    /**
     * 官方授权昵称
     */
    private String authName;

    /**
     * json格式的标签字符串（参考plat内TransportLabelJson类）
     */
    private String labelJson;

    /**
     * 保障货源（1是；0否；）
     */
    private Integer guaranteeGoods;

    /**
     * 是否有信用曝光权限（0没有；1有（未点击）；2有（已点击）；）
     */
    private Integer creditRetop;

    /**
     * 排序类型（0默认，1沉底）
     */
    private Integer sortType;

    /**
     * 优推好车主过期时间
     */
    private Date priorityRecommendExpireTime;

    /**
     * 是否是优车货源（0:否 1：优车 2：专车）
     */
    private Integer excellentGoods;

    /**
     * 是否优车2.0货源：1-否，2-是
     */
    private Integer excellentGoodsTwo;

    /**
     * 发货方式：0-普通找车，10-用户出价，20-特惠优车，21-快速优车，22-极速优车，30-专车
     */
    private Integer publishGoodsType;

    /**
     * 司机驾驶此类货物：1-需要，2-不需要
     */
    private Integer driverDriving;

    /**
     * 装货联系电话
     */
    private String loadCellPhone;

    /**
     * 卸货联系电话
     */
    private String unloadCellPhone;

    /**
     * 签约合作商ID（tyt_dispatch_cargo_owner表主键）
     */
    private Long cargoOwnerId;

    /**
     * 技术服务费
     */
    private BigDecimal tecServiceFee;

    /**
     * 标准货名备注
     */
    private String machineRemark;

    /**
     * 优车发货卡id
     */
    private Long excellentCardId;

    /**
     * 是否专票货源 0：否；1:是
     */
    private Integer invoiceTransport;

    /**
     * 附加运费
     */
    private String additionalPrice;

    /**
     * 发货时的企业发票税率
     */
    private BigDecimal enterpriseTaxRate;

    // ============================转换方法，勿删===============================
    public String getStartCoordX() {
        return MapPointUtils.toCoordStr(startCoordX);
    }

    public String getStartCoordY() {
        return MapPointUtils.toCoordStr(startCoordY);
    }

    public String getDestCoordX() {
        return MapPointUtils.toCoordStr(destCoordX);
    }

    public String getDestCoordY() {
        return MapPointUtils.toCoordStr(destCoordY);
    }

    public String getStartLatitude() {
        return MapPointUtils.toMapPointStr(startLatitude);
    }

    public String getStartLongitude() {
        return MapPointUtils.toMapPointStr(startLongitude);
    }

    public String getDestLongitude() {
        return MapPointUtils.toMapPointStr(destLongitude);
    }

    public String getDestLatitude() {
        return MapPointUtils.toMapPointStr(destLatitude);
    }

    public String getDistance() {
        return MapPointUtils.toCoordStr(distance);
    }
    // ============================转换方法，勿删===============================
}
