package com.teyuntong.goods.search.service.biz.record.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 拨打反馈页面填写记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@TableName("tyt_call_feedback_log")
public class CallFeedbackLogDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货源id
     */
    private Long srcMsgId;

    /**
     * 车主id
     */
    private Long carUserId;

    /**
     * 反馈一级选项
     */
    @TableField("feedback_1")
    private String feedback1;

    /**
     * 反馈二级选项
     */
    @TableField("feedback_2")
    private String feedback2;

    /**
     * 货源价格
     */
    private Integer price;

    /**
     * 车主出价金额
     */
    private Integer quotedPrice;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}
