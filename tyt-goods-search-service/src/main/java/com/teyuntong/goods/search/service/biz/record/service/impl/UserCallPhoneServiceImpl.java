package com.teyuntong.goods.search.service.biz.record.service.impl;

import com.teyuntong.goods.search.service.biz.goods.dto.GoodsPhoneDTO;
import com.teyuntong.goods.search.service.biz.record.entity.UserCallPhoneDO;
import com.teyuntong.goods.search.service.biz.record.mapper.UserCallPhoneMapper;
import com.teyuntong.goods.search.service.biz.record.service.UserCallPhoneService;
import com.teyuntong.goods.search.service.common.enums.ActionTypeEnum;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * 用户获取电话表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserCallPhoneServiceImpl implements UserCallPhoneService {

    private final UserCallPhoneMapper userCallPhoneMapper;

    @Override
    public void saveRecord(GoodsPhoneDTO goodsPhoneDTO, Long userId, ActionTypeEnum actionTypeEnum) {
        BaseParamDTO baseParam = LoginHelper.getBaseParam();
        UserCallPhoneDO userCallPhoneDO = new UserCallPhoneDO();
        userCallPhoneDO.setTsId(goodsPhoneDTO.getSrcMsgId());
        userCallPhoneDO.setUserId(userId);
        userCallPhoneDO.setClientVersion(baseParam.getClientVersion());
        userCallPhoneDO.setMuduleType(goodsPhoneDTO.getModuleType());
        userCallPhoneDO.setActionType(4);
        userCallPhoneDO.setClientSign(baseParam.getClientSign());
        userCallPhoneDO.setActionType(actionTypeEnum.getCode());
        userCallPhoneDO.setPath(goodsPhoneDTO.getPath());
        userCallPhoneDO.setCreateTime(new Date());
        userCallPhoneMapper.insert(userCallPhoneDO);
    }
}
