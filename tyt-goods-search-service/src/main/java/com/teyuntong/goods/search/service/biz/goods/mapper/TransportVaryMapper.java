package com.teyuntong.goods.search.service.biz.goods.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.goods.dto.TransportVaryDTO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportVaryDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 运输信息变动表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-21
 */
@Mapper
@DS("good_search")
public interface TransportVaryMapper extends BaseMapper<TransportVaryDO> {
    /**
     * 查询货源变动信息
     *
     * @param transportVaryDTO
     * @return
     */
    List<TransportVaryDO> searchHallVary(TransportVaryDTO transportVaryDTO);
}
