package com.teyuntong.goods.search.service.biz.route.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 常跑路线主表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Getter
@Setter
@TableName("often_drive_route")
public class OftenDriveRouteDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 最小重量
     */
    private Integer minWeight;

    /**
     * 最大重量
     */
    private Integer maxWeight;

    /**
     * 货物类型（逗号拼接）
     */
    private String goodsTypes;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 签名码
     */
    private String signCode;

    /**
     * 签名原内容
     */
    private String signContent;
}
