package com.teyuntong.goods.search.service.biz.group.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 群二维码管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-28
 */
@Getter
@Setter
@TableName("tyt_group_qr_code")
public class TytGroupQrCodeDO {

    /**
     * ID 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 省市
     */
    private String provinceCity;

    /**
     * 群名
     */
    private String groupName;

    /**
     * 二维码链接
     */
    private String qrCodeUrl;

    /**
     * 二维码上传时间
     */
    private Date qrCodeUploadTime;

    /**
     * 状态 0：禁用；1：启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}
