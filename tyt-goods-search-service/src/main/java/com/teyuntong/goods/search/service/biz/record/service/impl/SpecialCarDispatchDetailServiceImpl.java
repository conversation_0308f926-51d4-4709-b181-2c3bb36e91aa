package com.teyuntong.goods.search.service.biz.record.service.impl;

import com.teyuntong.goods.search.service.biz.record.mapper.SpecialCarDispatchDetailMapper;
import com.teyuntong.goods.search.service.biz.record.service.SpecialCarDispatchDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 专车派单详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SpecialCarDispatchDetailServiceImpl implements SpecialCarDispatchDetailService {

    private final SpecialCarDispatchDetailMapper specialCarDispatchDetailMapper;

    @Override
    public Integer selectCountByUserAndGoodsId(Long userId, Long srcMsgId) {
       return specialCarDispatchDetailMapper.selectCountByUserAndGoodsId(userId, srcMsgId);
    }
}
