package com.teyuntong.goods.search.service.biz.record.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.record.entity.UserCallPhoneRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <p>
 * 获取电话记录表（货源与用户关系）每个用户一条数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@Mapper
@DS("tyt")
public interface UserCallPhoneRecordMapper extends BaseMapper<UserCallPhoneRecordDO> {

    UserCallPhoneRecordDO getByUserAndSrcMsgId(@Param("userId") Long userId, @Param("srcMsgId") Long srcMsgId);

    /**
     * 查询用户去重拨打记录
     * @param userId
     * @return
     */
    int getDistinctTsCountByUserId(@Param("userId") Long userId,@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    int getDistinctTsCountBySrcMsgId(@Param("srcMsgId") Long srcMsgId);
}
