package com.teyuntong.goods.search.service.biz.similarity.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;
import com.teyuntong.goods.search.service.biz.goods.mapper.TransportMapper;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.goods.search.service.biz.similarity.dto.SimilarityQueryDTO;
import com.teyuntong.goods.search.service.biz.similarity.service.SimilarityTransportService;
import com.teyuntong.goods.search.service.common.constant.RedisKeyConstant;
import com.teyuntong.goods.search.service.common.util.TransportUtil;
import com.teyuntong.goods.search.service.remote.user.UserLocalService;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024/07/03 16:24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SimilarityTransportServiceImpl implements SimilarityTransportService {

    private final TransportMapper transportMapper;
    private final RedisUtil redisUtil;
    private final UserLocalService userLocalService;
    private final StringRedisTemplate redisTemplate;

    public static final Integer INTERVAL_15_MINUTE = 15 * 60 * 1000;

    /**
     * 相似货源列表查询
     */
    @Override
    public List<TransportDO> getSimilarityList(SimilarityQueryDTO queryDTO) {
        return transportMapper.getSimilarityList(queryDTO);
    }

    /**
     * 相似货源列表排序
     */
    @Override
    public void sortSimilarityList(List<? extends TransportVO> similarityVOList) {
        if (similarityVOList == null || similarityVOList.size() <= 1) {
            return;
        }

        // 排序分数map，key是货源id，value是排序分数
        Map<Long, Long> scoreMap = new HashMap<>();

        // 1. 按序展示：价格等级(无价有货参>低价无货参>无价无货参) > 价格 > 成交率 > 发布时间
        for (TransportVO transportVO : similarityVOList) {
            if (!scoreMap.containsKey(transportVO.getSrcMsgId())) {
                scoreMap.put(transportVO.getSrcMsgId(), sortScore(transportVO));
            }
        }

        // 按分数倒序
        similarityVOList.sort(Comparator.comparing((TransportVO t) -> scoreMap.get(t.getSrcMsgId())).reversed());
    }

    /**
     * 生成排序分数：价格等级(低价有货参>无价有货参>低价无货参>无价无货参) > 价格 > 成交率 > 发布时间
     */
    private Long sortScore(TransportVO vo) {
        long score = 0L;

        boolean hasPrice = TransportUtil.hasPrice(vo.getPrice()); // 是否有价
        boolean validSize = TransportUtil.isValidSize(vo); // 是否货参完整

        // 有价的都是低价，因为高于优车最低价的不是相似货源
        // 价格等级(低价有货参>无价有货参>低价无货参>无价无货参)
        if (hasPrice && validSize) {
            score += 8000_0000_0000_0000L;
        } else if (!hasPrice && validSize) {
            score += 4000_0000_0000_0000L;
        } else if (hasPrice && !validSize) {
            score += 2000_0000_0000_0000L;
        } else {
            score += 1000_0000_0000_0000L;
        }
        // 价格
        if (hasPrice) {
            score += Integer.parseInt(vo.getPrice()) * 1_0000_0000L;
        }
        // 身份
        // Integer identityType = userLocalService.getUserBiIdentityType(vo.getUserId());
        // if (UserBiIdentityEnum.isCargoOwner(identityType)) {
        //     score += (long) 8000 * 1_0000_0000L;
        // } else if (UserBiIdentityEnum.LOGISTICS_COMPANY.getCode().equals(identityType)) {
        //     score += (long) 4000 * 1_0000_0000L;
        // } else if (UserBiIdentityEnum.CARGO_TERMINAL.getCode().equals(identityType)) {
        //     score += (long) 1000 * 1_0000_0000L;
        // }
        // 成交率，格式：0.1234
        score += (long) (getUserDealRate(vo.getUserId()) * 10000 * 1_0000L);
        // 发布时间（发货时间更早，优先级更高）
        if (vo.getReleaseTime() != null) {
            score -= (vo.getReleaseTime().getTime() / 1000) % 86400;
        }
        return score;
    }

    /**
     * 返回用户成交率
     */
    private Double getUserDealRate(Long userId) {
        Object o = userDealRateLocalCache.getIfPresent(userId);
        if (o != null) {
            return (Double) o;
        }
        Object b = redisUtil.get(RedisKeyConstant.GOODS_USER_DEAL_RATE + userId);
        Double rate = (b == null ? 0.0 : Double.parseDouble(b.toString()));
        userDealRateLocalCache.put(userId, rate);
        return rate;
    }

    /**
     * 本地缓存配置
     */
    public static Cache<Object, Object> userDealRateLocalCache = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

}
