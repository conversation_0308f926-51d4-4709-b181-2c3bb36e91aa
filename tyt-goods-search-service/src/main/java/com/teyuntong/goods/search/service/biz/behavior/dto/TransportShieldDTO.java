package com.teyuntong.goods.search.service.biz.behavior.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 屏蔽货源
 *
 * <AUTHOR>
 * @since 2024/12/14 18:02
 */
@Getter
@Setter
public class TransportShieldDTO {

    /**
     * 货源id
     */
    @NotNull(message = "货源id不能为空")
    private Long srcMsgId;

    /**
     * 当前用户id
     */
    private Long userId;

    /**
     * 屏蔽理由
     */
    @NotEmpty(message = "屏蔽理由不能为空")
    private String reason;

    /**
     * 页面来源，中文标注
     */
    private String beFrom;

}
