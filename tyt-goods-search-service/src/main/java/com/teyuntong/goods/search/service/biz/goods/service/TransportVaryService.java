package com.teyuntong.goods.search.service.biz.goods.service;


import com.teyuntong.goods.search.service.biz.goods.dto.TransportVaryDTO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportVaryDO;

import java.util.List;

/**
 * <p>
 * 运输信息变动表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-21
 */
public interface TransportVaryService {
    /**
     * 查询货源变动
     *
     * @param transportVaryDTO
     * @return
     */
    List<TransportVaryDO> searchHallVary(TransportVaryDTO transportVaryDTO);
}
