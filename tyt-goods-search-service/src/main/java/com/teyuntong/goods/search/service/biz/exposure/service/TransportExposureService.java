package com.teyuntong.goods.search.service.biz.exposure.service;

import com.teyuntong.goods.search.service.biz.exposure.vo.ExposureVO;
import com.teyuntong.goods.search.service.biz.goods.dto.BaseTransportSearchDTO;
import com.teyuntong.goods.search.service.biz.goods.entity.TransportDO;

import java.util.List;

/**
 * 急走专区曝光类
 *
 * <AUTHOR>
 * @since 2024/07/16 10:25
 */
public interface TransportExposureService {

    /**
     * 获取急走专区列表
     */
    List<TransportDO> getExposureList(BaseTransportSearchDTO searchDTO);

    /**
     * 保存货源曝光记录
     *
     * @param transportList 货源集合
     */
    void saveExposureResult(List<ExposureVO> transportList);

    /**
     * 查询信用分最高的一条急走货源
     *
     * @param baseTransportSearchDTO
     * @return
     */
    TransportDO getExposureOfScore(BaseTransportSearchDTO baseTransportSearchDTO);
}
