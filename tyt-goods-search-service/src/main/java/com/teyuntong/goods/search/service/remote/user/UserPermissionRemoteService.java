package com.teyuntong.goods.search.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.permission.service.UserPermissionRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 查询用户权益
 *
 * <AUTHOR>
 * @since 2024/07/17 16:43
 */
@FeignClient(name = "tyt-user-service", path = "user", contextId = "userPermissionRemoteService",
        fallbackFactory = UserPermissionRemoteService.UserPermissionRemoteServiceFallback.class)
public interface UserPermissionRemoteService extends UserPermissionRpcService {

    @Component
    class UserPermissionRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<UserPermissionRemoteService> {
        public UserPermissionRemoteServiceFallback() {
            super(true, UserPermissionRemoteService.class);
        }
    }
}
