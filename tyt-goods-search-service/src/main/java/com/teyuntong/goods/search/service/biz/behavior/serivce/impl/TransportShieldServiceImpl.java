package com.teyuntong.goods.search.service.biz.behavior.serivce.impl;

import cn.hutool.core.date.DateUtil;
import com.teyuntong.goods.search.service.biz.behavior.dto.TransportShieldDTO;
import com.teyuntong.goods.search.service.biz.behavior.serivce.TransportShieldService;
import com.teyuntong.goods.search.service.common.constant.RedisKeyConstant;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 货源屏蔽接口 服务类
 *
 * <AUTHOR>
 * @since 2025/02/14 14:17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransportShieldServiceImpl implements TransportShieldService {

    private final RedisUtil redisUtil;

    /**
     * 添加屏蔽
     */
    @Override
    public void save(TransportShieldDTO shieldDTO) {
        // 屏蔽的货源记录到redis中，只当天有效
        String cacheKey = RedisKeyConstant.SHIELD_GOODS_KEY + shieldDTO.getUserId();
        long durationMs = DateUtil.betweenMs(new Date(), DateUtil.endOfDay(new Date()));
        redisUtil.addSet(cacheKey, Duration.ofMillis(durationMs), shieldDTO.getSrcMsgId());
    }

    /**
     * 获取屏蔽列表
     *
     * @param userId
     */
    @Override
    public List<Long> getShieldSrcMsgIds(Long userId) {
        try {
            String cacheKey = RedisKeyConstant.SHIELD_GOODS_KEY + userId;
            Set<Object> values = redisUtil.membersSet(cacheKey);
            return values.stream().map(Object::toString).map(Long::valueOf).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取屏蔽货源列表异常,userId:{}", userId, e);
        }
        return new ArrayList<>();
    }
}
