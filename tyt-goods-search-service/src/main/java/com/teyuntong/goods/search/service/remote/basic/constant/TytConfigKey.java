package com.teyuntong.goods.search.service.remote.basic.constant;

/**
 * 开关管理的key
 *
 * <AUTHOR>
 * @since 2024/07/15 17:20
 */
public class TytConfigKey {

    // 是否需要对电话加密 1 需要 2 不需要
    public static final String IS_NEED_ENCYPT = "is.need.encypt";

    // 货物信息默认userId
    public static final String TRANSPORT_DEFAULT_USER_ID = "tyt_transport_default_user_id";

    // 官方授权名称（0关闭；1开启）
    public static final String CHECK_AUTH_NAME = "tyt:plat:config:checkAuthName";

    // 相似货源星级展示(5，4表示显示S级、A级用户)(5、4、3、2、1表示S、A、B、C、D等级)
    public static final String SIMILAR_DISPLAY_ON_OFF = "similarGoodsSourceStarDisplayOnOff";

    // 是否显示急走货源 1显示，0不显示
    public static final String IS_SHOW_EXPOSURE = "tyt:route:config:hurryPush";

    // 货源曝光秒
    public static final String EXPOSURE_INTERVAL_SECOND = "tyt:route:config:exposureIntervalSecond";

    public static final String BENEFIT_LABEL_SHOW_SWITCH = "benefit_label_show_switch";
}
