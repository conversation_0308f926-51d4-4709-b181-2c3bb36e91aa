package com.teyuntong.goods.search.service.biz.goods.dto.rule;

import lombok.Getter;
import lombok.Setter;

/**
 * 智能排序因子：信息完整度
 * 格式：{"fiveScore":100,"fourScore":70,"threeScore":40,"twoScore":10,"oneScore":10}
 * fiveScore 5项完整得分100
 *
 * <AUTHOR>
 * @since 2024/8/29 15:59
 */
@Getter
@Setter
public class IntelligentSortRuleInfo {
    /**
     * 5项填写得分
     */
    private Integer fiveScore;
    /**
     * 4项填写得分
     */
    private Integer fourScore;
    /**
     * 3项填写得分
     */
    private Integer threeScore;
    /**
     * 2项填写得分
     */
    private Integer twoScore;
    /**
     * 1项填写得分
     */
    private Integer oneScore;

}
