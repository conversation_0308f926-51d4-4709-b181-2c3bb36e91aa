package com.teyuntong.goods.search.service.biz.behavior.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 屏蔽发货人
 *
 * <AUTHOR>
 * @since 2024/07/03 16:51
 */
@Getter
@Setter
@TableName("tyt_shielding_shipper")
public class ShieldingShipperDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户 id
     */
    private Long userId;

    /**
     * 被屏蔽人 id
     */
    private Long shieldingUserId;

    /**
     * 是否删除 0 否，1 是
     */
    private Integer isDelete;

    /**
     * 创建人 id
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人 id
     */
    private Long updateId;

    /**
     * 修改时间
     */
    private Date updateTime;

}
