package com.teyuntong.goods.search.service.common.util;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/05/07 11:30
 */
public class FormatUtil {

    public static String formatDimension(String valueStr) {
        if (valueStr == null || valueStr.isEmpty()) {
            return "";
        }

        try {
            BigDecimal value = new BigDecimal(valueStr);
            if (value.stripTrailingZeros().scale() <= 0) {
                // 是整数，去掉 .0
                return value.toBigIntegerExact().toString();
            } else {
                // 非整数，保持原样输出
                return value.stripTrailingZeros().toPlainString();
            }
        } catch (NumberFormatException e) {
            return valueStr;
        }
    }

}
