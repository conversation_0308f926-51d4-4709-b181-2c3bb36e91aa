package com.teyuntong.goods.search.service.biz.goods.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/10/17 10:41
 */
@Data
public class GoodsPhoneDTO {
    /**
     * 货源id
     */
    @NotNull(message = "货源id不能为空")
    private Long srcMsgId;

    /**
     * 模块类型
     * 1精准货源模块，2 货源列表模块 3 货源详情模块 4 板车购买模块
     * 5 板车司机招聘模块 6 板车维修模块 7 板车配件模块 8 机械设备维修模块
     * 9 机械设备配件模块 10 维修师模块 11 机械司机招聘模块 12 机械司机求职模块
     */
    private Integer moduleType;
    /**
     * 路径（在哪个地方获取电话的）
     */
    private String path;
    /**
     * 是否需要进行车辆认证阻断  1:需要 0:不需要
     */
    private Integer noCarBlock;

    /**
     * 利益点透传
     */
    private String benefitLabelCode;


}
