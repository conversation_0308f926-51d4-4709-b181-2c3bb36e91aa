package com.teyuntong.goods.search.service.biz.dispatch.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.teyuntong.goods.search.service.biz.dispatch.entity.SpecialCarDispatchFailureDO;
import com.teyuntong.goods.search.service.biz.dispatch.mapper.SpecialCarDispatchFailureMapper;
import com.teyuntong.goods.search.service.biz.dispatch.service.SpecialCarDispatchFailureService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 专车自动派单无人接单货源表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SpecialCarDispatchFailureServiceImpl extends ServiceImpl<SpecialCarDispatchFailureMapper, SpecialCarDispatchFailureDO> implements SpecialCarDispatchFailureService {

    /**
     * 根据srcMsgIds查询
     */
    @Override
    public List<SpecialCarDispatchFailureDO> getBySrcMsgIds(List<Long> srcMsgIds) {
        if (CollectionUtils.isEmpty(srcMsgIds)) {
            return List.of();
        }
        return baseMapper.getBySrcMsgIds(srcMsgIds);
    }
}
