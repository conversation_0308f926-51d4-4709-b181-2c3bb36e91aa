package com.teyuntong.goods.search.service.biz.record.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.teyuntong.goods.search.service.biz.goods.dto.SearchRecordQueryDTO;
import com.teyuntong.goods.search.service.biz.record.entity.AppCallLogDO;

import java.util.Date;

/**
 * <p>
 * 记录APP拨打电话的信息（电话标注）  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
public interface AppCallLogService extends IService<AppCallLogDO> {

    /**
     * 获取最近的车主拨打记录
     */
    PageInfo<AppCallLogDO> getRecentCallPage(SearchRecordQueryDTO queryDTO);

    /**
     * 获取车主对货源的拨打记录
     *
     * @param userId
     * @param srcMsgId
     * @return
     */
    AppCallLogDO getBySrcMsgIdAndUserId(Long userId, Long srcMsgId);

    /**
     * 获取最近车主拨打次数
     *
     * @param userId
     * @param today
     * @return
     */
    Integer getRecentCountByUserId(Long userId, Date today);
}
