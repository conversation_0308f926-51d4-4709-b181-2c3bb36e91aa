package com.teyuntong.goods.search.service.biz.exposure.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.exposure.entity.TransportExposureResultDO;
import com.teyuntong.goods.search.service.biz.exposure.vo.ExposureVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 货源曝光次数结果表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Mapper
@DS("tyt")
public interface TransportExposureResultMapper extends BaseMapper<TransportExposureResultDO> {

    /**
     * 更新曝光数量
     */
    int updateExposureCount(@Param("srcMsgIdList") List<Long> srcMsgIdList);

    /**
     * 保存曝光记录
     */
    int saveExposureResult(@Param("transportList") List<ExposureVO> transportList);



}
