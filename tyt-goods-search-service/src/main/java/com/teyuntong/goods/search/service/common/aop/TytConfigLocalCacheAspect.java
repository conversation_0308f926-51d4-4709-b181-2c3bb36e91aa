package com.teyuntong.goods.search.service.common.aop;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import java.lang.reflect.Parameter;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 调用开关接口、公共资源接口的本地缓存切面
 *
 * <AUTHOR>
 * @since 2024/07/15 15:22
 */
@Slf4j
@Aspect
@Component
public class TytConfigLocalCacheAspect {

    /**
     * 本地缓存，适合缓存开关、公共资源等调用频繁，数据量不大的场景
     * 默认淘汰策略：LRU最近最少使用
     */
    public static Cache<Object, Object> localCache = Caffeine.newBuilder()
            // 最大条数
            .maximumSize(1000)
            // 写入后10s过期
            .expireAfterWrite(10, TimeUnit.SECONDS)
            .build();

    /**
     * 开关配置只拦截get开头的方法
     */
    @Pointcut("execution(* com.teyuntong.goods.search.service.remote.basic.ConfigRemoteService.get*(..))")
    public void configServiceGet() {
    }

    /**
     * 切点，多个可以用 || 隔开，例如：configServiceGet() || resourceServiceGet()
     */
    @Pointcut("configServiceGet()")
    public void point() {
    }

    @Around("point()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {

        // 先查询本地缓存
        String cacheKey = Arrays.stream(joinPoint.getArgs())
                .map(t -> t == null ? "null" : t.toString())
                .collect(Collectors.joining("-"));
        Object ifPresent = localCache.getIfPresent(cacheKey);
        if (ifPresent != null) {
            return ifPresent;
        }
        Object result = null;
        try {
            // 执行原方法
            result = joinPoint.proceed();
        } catch (Exception e) {
            log.error("远程调用失败，key:{}", cacheKey, e);
            try {
                MethodSignature signature = (MethodSignature) joinPoint.getSignature();
                Parameter[] parameters = signature.getMethod().getParameters();

                Object[] args = joinPoint.getArgs();
                for (int i = 0; i < parameters.length; i++) {
                    Parameter parameter = parameters[i];
                    RequestParam annotation = parameter.getAnnotation(RequestParam.class);
                    if (annotation != null && Objects.equals(annotation.value(), "defaultValue")) {
                        result = args[i];
                    }
                }
            } catch (Exception e1) {
                log.error("aop解析失败，key:{}", cacheKey, e1);
            }
        }

        // 本地缓存，远程调用失败会返回null
        if (result != null) {
            localCache.put(cacheKey, result);
        }

        return result;
    }
}
