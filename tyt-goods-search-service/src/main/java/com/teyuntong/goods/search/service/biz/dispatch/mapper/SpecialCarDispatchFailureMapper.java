package com.teyuntong.goods.search.service.biz.dispatch.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.dispatch.entity.SpecialCarDispatchFailureDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 专车自动派单无人接单货源表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
@DS("tyt")
public interface SpecialCarDispatchFailureMapper extends BaseMapper<SpecialCarDispatchFailureDO> {

    /**
     * 根据srcMsgIds查询
     */
    List<SpecialCarDispatchFailureDO> getBySrcMsgIds(@Param("srcMsgIds") List<Long> srcMsgIds);
}
