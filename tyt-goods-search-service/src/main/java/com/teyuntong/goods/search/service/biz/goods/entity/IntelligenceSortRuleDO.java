package com.teyuntong.goods.search.service.biz.goods.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 智能排序计分规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Getter
@Setter
@TableName("tyt_intelligence_sort_rule")
public class IntelligenceSortRuleDO {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 权重
     */
    private BigDecimal weight;

    /**
     * 1:地址匹配度，2：时效性，3：信息完整度，4：价格
     */
    private Integer ruleType;

    /**
     * 计分规则
     */
    private String rule;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人id
     */
    private Long modifyId;

    /**
     * 修改人名称
     */
    private String modifyName;

    /**
     * 修改时间
     */
    private Date modifyTime;
}
