package com.teyuntong.goods.search.service.biz.record.service.impl;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.teyuntong.goods.search.service.biz.goods.dto.SearchRecordQueryDTO;
import com.teyuntong.goods.search.service.biz.record.entity.TransportQuotedPriceDO;
import com.teyuntong.goods.search.service.biz.record.mapper.TransportQuotedPriceMapper;
import com.teyuntong.goods.search.service.biz.record.service.TransportQuotedPriceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 无价货源报价表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TransportQuotedPriceServiceImpl implements TransportQuotedPriceService {

    private final TransportQuotedPriceMapper transportQuotedPriceMapper;

    /**
     * 获取最近的车主拨打记录
     */
    @Override
    public PageInfo<TransportQuotedPriceDO> getRecentQuotedPage(SearchRecordQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getCurrentPage(), queryDTO.getPageSize());
        List<TransportQuotedPriceDO> list = transportQuotedPriceMapper.getRecentQuotedList(queryDTO.getUserId(), queryDTO.getStartDate());
        return new PageInfo<>(list);
    }

    /**
     * 获取今日报价记录
     */
    @Override
    public List<TransportQuotedPriceDO> getTodayQuoted(Long carUserId) {
        Date startDate = DateUtil.beginOfDay(new Date());
        return transportQuotedPriceMapper.getRecentQuotedList(carUserId, startDate);
    }

    @Override
    public Integer getRecentCountByUserId(Long userId, Date startTime) {
        return transportQuotedPriceMapper.getRecentCountByUserId(userId, startTime);
    }

    /**
     * 获取报价记录
     */
    @Override
    public TransportQuotedPriceDO getQuoted(Long carUserId, Long srcMsgId) {
        return transportQuotedPriceMapper.getByCarIdAndSrcMsgId(carUserId, srcMsgId);
    }
}
