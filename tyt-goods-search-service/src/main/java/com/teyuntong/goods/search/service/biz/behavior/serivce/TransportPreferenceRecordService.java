package com.teyuntong.goods.search.service.biz.behavior.serivce;

import com.teyuntong.goods.search.service.biz.goods.dto.TransportPreferenceDTO;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 货源用户偏好记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
public interface TransportPreferenceRecordService {

    /**
     * 返回偏好记录
     *
     * @param srcMsgIdList 货源id集合
     * @param userId       用户id
     * @return 返回map，key=srcMsgId, value=status
     */
    Map<Long, Integer> getPreferenceMap(List<Long> srcMsgIdList, Long userId);

    /**
     * 设置货源偏好值
     *
     * @param transportList 货源集合
     * @param userId        用户id
     */
    void setPreference(List<? extends TransportVO> transportList, Long userId);

    /**
     * 保存偏好记录
     */
    void savePreference(TransportPreferenceDTO preferenceDTO);
}
