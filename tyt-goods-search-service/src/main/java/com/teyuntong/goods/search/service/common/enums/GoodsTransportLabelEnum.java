package com.teyuntong.goods.search.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 好中差货标签
 *
 * <AUTHOR>
 * @since 2025/06/04 13:36
 */
@Getter
@AllArgsConstructor
public enum GoodsTransportLabelEnum {

    //好货类型 11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2，0:不符合
    GOOD_ONE(11, "好货1"),
    GOOD_TWO(12, "好货2"),
    GOOD_THREE(13, "好货3"),
    MIDDLE_ONE(21, "中货1"),
    MIDDLE_TWO(22, "中货2"),
    MIDDLE_THREE(23, "中货3"),
    BAD_ONE(31, "差货1"),
    BAD_TWO(32, "差货2");

    private final Integer code;
    private final String name;

    /**
     * 是否是好货
     */
    public static boolean isGood(Integer code) {
        return code != null && code > 10 && code < 20;
    }

    /**
     * 是否是中货
     */
    public static boolean isMid(Integer code) {
        return code != null && code > 20 && code < 30;
    }

    public static String getNameByCode(Integer code) {
        for (GoodsTransportLabelEnum value : GoodsTransportLabelEnum.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return null;
    }
}
