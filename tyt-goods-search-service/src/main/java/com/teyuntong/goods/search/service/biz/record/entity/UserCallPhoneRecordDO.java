package com.teyuntong.goods.search.service.biz.record.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 获取电话记录表（货源与用户关系）每个用户一条数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@Getter
@Setter
@TableName("tyt_user_call_phone_record")
public class UserCallPhoneRecordDO {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 运输信息最原始ID
     */
    private Long tsId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 拨打时间
     */
    private Date ctime;

    /**
     * -1: 老电话规则 1：普通用户 2：试用会员 4：VIP会员
     */
    private Integer level;

    /**
     * 路径
     */
    private String path;

    /**
     * 客户端标识
     */
    private String platId;

    /**
     * 货源利益点标签
     */
    private String benefitLabelCode;
}
