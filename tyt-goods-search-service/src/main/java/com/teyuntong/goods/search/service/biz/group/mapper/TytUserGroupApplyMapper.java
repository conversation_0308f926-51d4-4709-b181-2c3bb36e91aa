package com.teyuntong.goods.search.service.biz.group.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.group.entity.TytUserGroupApplyDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 用户上报申请建群记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-28
 */
@Mapper
@DS("tyt")
public interface TytUserGroupApplyMapper extends BaseMapper<TytUserGroupApplyDO> {

    TytUserGroupApplyDO getJoinByProvinceCityAndUserId(@Param("provinceCity") String provinceCity, @Param("userId") Long userId);

    void insertSelective(@Param("tytUserGroupApplyDO") TytUserGroupApplyDO tytUserGroupApplyDO);

}
