package com.teyuntong.goods.search.service.biz.route.vo;

import com.teyuntong.goods.search.service.biz.goods.vo.TransportVO;
import com.teyuntong.goods.search.service.biz.route.dto.SearchRouteExtraDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 常跑路线找货大厅返回VO
 *
 * <AUTHOR>
 * @since 2025/04/20 17:49
 */
@Getter
@Setter
public class OftenRouteSearchVO {
    /**
     * 当前查询返回了多少条数据
     */
    private Integer responseSize = 0;
    /**
     * 是否还有数据  true:有  false:无
     */
    private boolean hasNext = false;

    /**
     * 货源列表
     */
    private List<TransportVO> list;
    /**
     * 找货大厅额外参数
     */
    private SearchRouteExtraDTO searchRouteExtra;
}
