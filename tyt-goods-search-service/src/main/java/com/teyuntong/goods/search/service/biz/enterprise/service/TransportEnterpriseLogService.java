package com.teyuntong.goods.search.service.biz.enterprise.service;

import com.teyuntong.goods.search.service.biz.enterprise.entity.TransportEnterpriseLogDO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 开票货源企业信息记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
public interface TransportEnterpriseLogService {

    /**
     * 返回专票货源的开票主体ID
     *
     * @return {"srcMsgId":"invoiceSubjectId"}
     */
    Map<Long, Long> getInvoiceSubjectIdMap(List<Long> srcMsgIds);

    /**
     * 根据货源ID获取开票货源信息
     *
     * @param srcMsgId
     * @return
     */
    TransportEnterpriseLogDO selectBySrcMsgId(Long srcMsgId);
}
