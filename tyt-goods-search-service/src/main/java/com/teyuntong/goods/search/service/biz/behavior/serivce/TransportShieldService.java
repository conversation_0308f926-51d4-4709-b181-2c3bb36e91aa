package com.teyuntong.goods.search.service.biz.behavior.serivce;


import com.teyuntong.goods.search.service.biz.behavior.dto.TransportShieldDTO;

import java.util.List;

/**
 * <p>
 * 货源屏蔽接口 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-14
 */
public interface TransportShieldService {

    /**
     * 添加屏蔽
     */
    void save(TransportShieldDTO shieldDTO);

    /**
     * 获取屏蔽列表
     */
    List<Long> getShieldSrcMsgIds(Long userId);
}
