package com.teyuntong.goods.search.service.biz.goods.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 范围倒短
 *
 * <AUTHOR>
 * @since 2024/10/21 16:39
 */
@Data
public class ShortRangeDTO {

    /**
     * 出发地坐标
     */
    @NotBlank(message = "出发地坐标不能为空")
    private String startCoord;
    /**
     * 出发地范围
     */
    @NotBlank(message = "出发地范围不能为空")
    private String startDistance;
    /**
     * 出发地省份
     */
    @NotBlank(message = "出发地省份不能为空")
    private String startProvinc;
    /**
     * 出发地城市
     */
    @NotBlank(message = "出发地城市不能为空")
    private String startCity;
    /**
     * 出发地区县
     */
    private String startArea;
    /**
     * 查询类型
     * 0、自动刷新加载新数据
     * 1、默认查询，或者下拉时重新加载
     * 2、上滑，往前翻请求历史数据
     */
    private Integer queryType;

    /**
     * 查询标记点，找货大厅、急走专区列表需要
     * queryType=0，下拉刷新或自动刷新查询大于querySign，为列表最大id
     * queryType=1，该字段无效
     * queryType=2，上滑查询查询小于querySign，为列表最小id
     */
    private Long querySign;
}
