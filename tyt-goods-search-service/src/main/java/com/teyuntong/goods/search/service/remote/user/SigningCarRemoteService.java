package com.teyuntong.goods.search.service.remote.user;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.car.service.TytSigningCarRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 查询用户权益
 *
 * <AUTHOR>
 * @since 2024/07/17 16:43
 */
@FeignClient(name = "tyt-user-service", path = "user", contextId = "TytSigningCarRpcService", fallbackFactory = SigningCarRemoteService.SigningCarRemoteServiceFallback.class)
public interface SigningCarRemoteService extends TytSigningCarRpcService {

    @Component
    class SigningCarRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<SigningCarRemoteService> {
        public SigningCarRemoteServiceFallback() {
            super(true, SigningCarRemoteService.class);
        }
    }
}
