package com.teyuntong.goods.search.service.common.config;

import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;

/**
 * value 和 hash value 序列化方式都为 json 的 {@link RedisTemplate}
 * 序列化时会携带类信息, 空字段也会被序列化到 json 内
 *
 * <AUTHOR>
 * @since 2023/11/07 17:55
 */
public class OftenRouteRedisTemplate extends RedisTemplate<String, Object> {

    public OftenRouteRedisTemplate(RedisConnectionFactory connectionFactory) {
        setConnectionFactory(connectionFactory);

        setKeySerializer(RedisSerializer.string());
        setValueSerializer(RedisSerializer.json());
        setHashKeySerializer(RedisSerializer.string());
        setHashValueSerializer(RedisSerializer.json());
    }
}
