package com.teyuntong.goods.search.service.remote.goods;

import com.teyuntong.goods.service.client.callphonerecord.service.AxbRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/12/25 16:16
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "AxbRpcService", fallbackFactory = AxbRemoteService.AxbRemoteRemoteFallbackFactory.class)
public interface AxbRemoteService extends AxbRpcService {

    @Slf4j
    @Component
    class AxbRemoteRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<AxbRpcService> {
        protected AxbRemoteRemoteFallbackFactory() {
            super(true, AxbRpcService.class);
        }
    }
}
