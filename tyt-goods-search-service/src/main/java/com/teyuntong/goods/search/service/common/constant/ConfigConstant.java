package com.teyuntong.goods.search.service.common.constant;

/**
 * 配置常量
 *
 * <AUTHOR>
 * @since 2024/10/18 16:11
 */
public class ConfigConstant {
    /**
     * 是否开启搜索记录日志开关
     */
    public final static String SEARCH_LOG_KEY = "searchLog";
    /**
     * 找货大厅走es还是db开关
     */
    public final static String GOODS_SEARCH_ES_DB = "goods_search_es_db";
    /**
     * 支付专车货源时在该配置中的城市进行文案提示
     */
    public final static String SPECIAL_CAR_START_POINT_PUBLIC = "special_car_start_point_public";
    /**
     * 信息费支付跳转地址
     */
    public final static String INFO_FEE_PAY_REDIRECT_URL_FOR_SERVER = "infoFeeToPayRedirectUrlForServer";
    /**
     * 未认证用户N次豁免拨打开关,0关闭，1奇数开启，2全部开启
     */
    public final static String UNAUTH_USER_CALL_EXEMPT_SWITCH = "unauth_user_call_exempt_switch";
    /**
     * 未认证用户N次豁免拨打次数，实验组次数
     */
    public static final String UNAUTH_CALL_EXEMPT_NUMS = "unauth_user_call_exempt_nums";

    public static final String REMAIN_CALL_COUNT = "remain_call_count";
    /**
     * 未认证用户N次豁免拨打次数，对照组次数
     */
    public static final String UNAUTH_CALL_EXEMPT_NUMS_FIX = "unauth_user_call_exempt_nums_fix";
    /**
     * 全局屏蔽的货主测试账号
     */
    public static final String CARGO_OWNER_TEST_ACCOUNT = "cargo_owner_test_user_ids";
    /**
     * 不受全局屏蔽影响的车主测试账号
     */
    public static final String CAR_OWNER_TEST_ACCOUNT = "car_owner_test_user_ids";
    /**
     * 是否开启屏蔽货主功能（主要是做降级使用的）
     */
    public static final String SHIELDING_USER_SWITCH = "shielding_user_switch";


    /**
     * 货方非会员，电议订金不退还时给车方提示
     */
    public final static String NO_GOODSMEMBER_TEXT_REMIND = "no_goodsmember_text_remind";

    /**
     * 找货大厅插入固定资源位的位置
     */
    public final static String TRANSPORT_FIXED_LOCATION = "transport_fixed_location";
    /**
     * 找货大厅插入固定资源位开关 0关闭，1全部开启 2奇数开启
     */
    public final static String TRANSPORT_FIXED_LOCATION_SWITCH = "transport_fixed_location_switch";

    /**
     * 找货大厅打有出价标签，记录最早的出价
     */
    public final static String EARLIEST_PRICE_CACHE_KEY = "EarliestPrice:";

    /**
     * 秒抢货源倒计时缓存前缀，格式：deposit:payment:{tsOrderNo} = 支付截止时间时间戳
     */
    public final static String DEPOSIT_PAYMENT_PREFIX = "deposit:payment:";


    /**
     * 货源拨打返回页面加价档位
     */
    public final static String TRANSPORT_ADD_PRICE_OPTION = "transport_add_price_option";
}
