package com.teyuntong.goods.search.service.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @since 2024/11/7 17:39
 */
@Configuration
public class ThreadPoolConfig {

    @Bean(name = "searchThreadPool")
    public Executor searchThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("search-Thread-");
        executor.initialize();
        return executor;
    }
}
