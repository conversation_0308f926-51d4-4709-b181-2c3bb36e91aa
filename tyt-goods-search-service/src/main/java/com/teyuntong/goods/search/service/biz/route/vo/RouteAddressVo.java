package com.teyuntong.goods.search.service.biz.route.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.teyuntong.goods.search.service.common.error.GoodsSearchErrorCode;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.beans.Transient;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class RouteAddressVo {

    private Long id;

    /**
     * 省
     */
    private String provinc;

    /**
     * 市
     */
    private String city;

    /**
     * 区/县
     */
    private String area;

    @Transient
    @JsonIgnore
    @JSONField(serialize = false)
    public String getAddressSign() {
        if (StringUtils.isBlank(provinc)) {
            throw new BusinessException(GoodsSearchErrorCode.ROUTE_ADDRESS_ERROR);
        }

        List<String> addressList = new ArrayList<>();
        addressList.add(provinc);
        if (StringUtils.isNotBlank(city)) {
            // 地址要按层级来
            addressList.add(city);
            if (StringUtils.isNotBlank(area)) {
                addressList.add(area);
            }
        }
        return StringUtils.join(addressList, ".");
    }

}