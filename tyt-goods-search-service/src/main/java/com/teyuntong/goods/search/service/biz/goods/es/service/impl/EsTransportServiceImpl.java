package com.teyuntong.goods.search.service.biz.goods.es.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.teyuntong.goods.search.service.biz.bi.entity.DwsCarTypeDO;
import com.teyuntong.goods.search.service.biz.bi.mapper.DwsCarTypeMapper;
import com.teyuntong.goods.search.service.biz.goods.dto.BaseTransportSearchDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.EsSearchExtra;
import com.teyuntong.goods.search.service.biz.goods.dto.IntelligenceSortDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.SearchHallCountDTO;
import com.teyuntong.goods.search.service.biz.goods.dto.rule.IntelligentSortRuleAgeing;
import com.teyuntong.goods.search.service.biz.goods.dto.rule.IntelligentSortRuleWeight;
import com.teyuntong.goods.search.service.biz.goods.entity.IntelligenceSortRuleDO;
import com.teyuntong.goods.search.service.biz.goods.es.entity.TransportEsDO;
import com.teyuntong.goods.search.service.biz.goods.es.service.EsTransportService;
import com.teyuntong.goods.search.service.biz.goods.mapper.IntelligenceSortRuleMapper;
import com.teyuntong.goods.search.service.biz.goods.vo.TransportEsVO;
import com.teyuntong.goods.search.service.biz.route.dto.OftenRouteSearchDTO;
import com.teyuntong.goods.search.service.biz.route.dto.SearchRouteExtraDTO;
import com.teyuntong.goods.search.service.biz.route.dto.UserOftenRouteDTO;
import com.teyuntong.goods.search.service.common.enums.ExcellentGoodsTwoEnum;
import com.teyuntong.goods.search.service.common.enums.YesNoEnum;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.common.lucene.search.function.CombineFunction;
import org.elasticsearch.common.lucene.search.function.FunctionScoreQuery;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.index.query.functionscore.ScriptScoreFunctionBuilder;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.ScriptSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.teyuntong.goods.search.service.biz.goods.enums.IntelligentSortRuleTypeEnum.*;
import static jodd.util.StringPool.COMMA;

/**
 * <AUTHOR>
 * @since 2024/7/23 13:31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EsTransportServiceImpl implements EsTransportService {

    public static final BigDecimal HUNDRED = new BigDecimal(100);

    private final RestHighLevelClient restHighLevelClient;
    private final IntelligenceSortRuleMapper intelligenceSortRuleMapper;
    private final DwsCarTypeMapper dwsCarTypeMapper;
    private final RedisUtil redisUtil;

    // 1. 定义评分函数构建器映射
    private static final Map<Integer, BiFunction<IntelligenceSortDTO, IntelligenceSortRuleDO, Script>> SCORE_SCRIPT_BUILDERS = new HashMap<>();

    @PostConstruct
    public void init() {
        // 2. 初始化映射关系
        SCORE_SCRIPT_BUILDERS.put(ADDRESS.getCode(), this::buildAddressScript);
        SCORE_SCRIPT_BUILDERS.put(AGEING.getCode(), this::buildTimeScript);
        SCORE_SCRIPT_BUILDERS.put(INFO.getCode(), this::buildIntegrityScript);
        SCORE_SCRIPT_BUILDERS.put(MODEL.getCode(), this::buildModelScript);
        SCORE_SCRIPT_BUILDERS.put(WEIGHT.getCode(), this::buildWeightScript);
        SCORE_SCRIPT_BUILDERS.put(REFRESH.getCode(), this::buildRefreshScript);
        SCORE_SCRIPT_BUILDERS.put(DISTANCE.getCode(), this::buildDistanceScript);
        SCORE_SCRIPT_BUILDERS.put(LEVEL.getCode(), this::buildLevelScript);
        SCORE_SCRIPT_BUILDERS.put(PRICE.getCode(), this::buildPriceScript);
    }

    @Override
    public Long intelligenceBubbleCount(IntelligenceSortDTO intelligenceSortDTO) {
        CountRequest countRequest = new CountRequest(getTransportIndex());
        BoolQueryBuilder boolQuery = buildBaseQuery(intelligenceSortDTO);
        // 智能排序气泡自己的查询逻辑
        boolQuery.must(QueryBuilders.rangeQuery("releaseTime").gt(intelligenceSortDTO.getEsSearchExtra().getFirstSearchTime()));

        countRequest.query(boolQuery);
        try {
            return restHighLevelClient.count(countRequest, RequestOptions.DEFAULT).getCount();
        } catch (IOException e) {
            log.error("智能排序查询气泡错误,参数{}", JSONUtil.toJsonStr(intelligenceSortDTO), e);
        }
        return 0L;
    }

    @Override
    public List<TransportEsVO> intelligenceSortList(IntelligenceSortDTO intelligenceSortDTO) {
        List<TransportEsVO> transportEsVOList = new ArrayList<>();
        try {
            // 组装查询请求
            SearchRequest searchRequest = buildSearchRequest(intelligenceSortDTO);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = searchResponse.getHits().getHits();

            for (SearchHit hit : hits) {
                TransportEsVO transportEsVO = new TransportEsVO();

                TransportEsDO transportEsDO = BeanUtil.mapToBean(hit.getSourceAsMap(), TransportEsDO.class, true, CopyOptions.create());

                transportEsVO.setScore(hit.getScore());
                transportEsVO.setId(Long.parseLong(hit.getId()));
                transportEsVO.setTransportEsDO(transportEsDO);
                transportEsVOList.add(transportEsVO);
            }
        } catch (IOException e) {
            log.error("智能排序查询错误,参数{}", JSONUtil.toJsonStr(intelligenceSortDTO), e);
        }
        return transportEsVOList;
    }

    /**
     * 组装查询请求
     *
     * @param intelligenceSortDTO
     * @return
     */
    public SearchRequest buildSearchRequest(IntelligenceSortDTO intelligenceSortDTO) {
        // 组装基础查询条件
        BoolQueryBuilder boolQuery = buildBaseQuery(intelligenceSortDTO);
        // 只能查询小于第一次查询时间的货源
        boolQuery.must(QueryBuilders.rangeQuery("ctime").lt(intelligenceSortDTO.getEsSearchExtra().getFirstSearchTime()));
        // 组装智能排序的查询逻辑
        FunctionScoreQueryBuilder.FilterFunctionBuilder[] filterFunctionBuilders = buildScoreFunction(intelligenceSortDTO);
        // 组装智能排序的查询逻辑
        FunctionScoreQueryBuilder functionScoreQueryBuilder = QueryBuilders.functionScoreQuery(boolQuery, filterFunctionBuilders)
                .scoreMode(FunctionScoreQuery.ScoreMode.SUM) // 多个评分函数结果相加
                .boostMode(CombineFunction.REPLACE); // 取自定义评分函数，忽略掉ES文档评分

        // 组装查询条件
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(functionScoreQueryBuilder).size(intelligenceSortDTO.getPageSize())
                .sort(SortBuilders.scoreSort().order(SortOrder.DESC))
                .sort(SortBuilders.fieldSort("id").order(SortOrder.DESC));

        // 添加分页条件，按分数倒序，按id升序
        EsSearchExtra esSearchExtra = intelligenceSortDTO.getEsSearchExtra();
        if (esSearchExtra.getLastSortScore() != null && esSearchExtra.getLastSortTsId() != null) {
            sourceBuilder.searchAfter(new Object[]{esSearchExtra.getLastSortScore(), esSearchExtra.getLastSortTsId()});
        }

        SearchRequest searchRequest = new SearchRequest(getTransportIndex());
        searchRequest.source(sourceBuilder);
        log.debug("智能排序查询ES searchRequest.source：{}", sourceBuilder);
        return searchRequest;
    }

    /**
     * 组装查询条件
     *
     * @param searchBean
     * @return
     */
    private BoolQueryBuilder buildBaseQuery(BaseTransportSearchDTO searchBean) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("status", 1)).must(QueryBuilders.termQuery("displayType", 1)).must(QueryBuilders.termQuery("isDisplay", 1)).must(QueryBuilders.termQuery("isShow", 1));

        BoolQueryBuilder startBoolQuery = QueryBuilders.boolQuery();
        BoolQueryBuilder startAddressQuery = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(searchBean.getStartProvinc())) {
            startAddressQuery.filter(QueryBuilders.termQuery("startProvinc", searchBean.getStartProvinc()));
        }
        if (StringUtils.isNotBlank(searchBean.getStartCity())) {
            startAddressQuery.filter(QueryBuilders.termQuery("startCity", searchBean.getStartCity()));
        }
        if (StringUtils.isNotBlank(searchBean.getStartArea())) {
            startAddressQuery.filter(QueryBuilders.termQuery("startArea", searchBean.getStartArea()));
        }
        BoolQueryBuilder startCoordQuery = QueryBuilders.boolQuery();
        if (searchBean.getStartCoordX() != null && searchBean.getStartCoordY() != null) {
            startCoordQuery.must(QueryBuilders.rangeQuery("startCoordX").from(searchBean.getStartCoordX() - searchBean.getStartRange()).to(searchBean.getStartCoordX() + searchBean.getStartRange()));
            startCoordQuery.must(QueryBuilders.rangeQuery("startCoordY").from(searchBean.getStartCoordY() - searchBean.getStartRange()).to(searchBean.getStartCoordY() + searchBean.getStartRange()));
        }
        // 检查 addressQuery 是否有子句（即是否所有字段都被填充了）
        if (startAddressQuery.hasClauses()) {
            startBoolQuery.should(startAddressQuery);
        }
        // 检查 coordQuery 是否有子句（即 destCoordX 和 destCoordY 是否都被填充了）
        if (startCoordQuery.hasClauses()) {
            startBoolQuery.should(startCoordQuery);
        }
        if (startBoolQuery.hasClauses()) {
            query.must(startBoolQuery);
        }

        BoolQueryBuilder destBoolQuery = QueryBuilders.boolQuery();
        BoolQueryBuilder destAddressQuery = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(searchBean.getDestProvinc())) {
            destAddressQuery.filter(QueryBuilders.termQuery("destProvinc", searchBean.getDestProvinc()));
        }
        if (StringUtils.isNotBlank(searchBean.getDestCity())) {
            destAddressQuery.filter(QueryBuilders.termQuery("destCity", searchBean.getDestCity()));
        }
        if (StringUtils.isNotBlank(searchBean.getDestArea())) {
            destAddressQuery.filter(QueryBuilders.termQuery("destArea", searchBean.getDestArea()));
        }
        BoolQueryBuilder destCoordQuery = QueryBuilders.boolQuery();
        if (searchBean.getDestCoordX() != null && searchBean.getDestCoordY() != null) {
            destCoordQuery.must(QueryBuilders.rangeQuery("destCoordX").from(searchBean.getDestCoordX() - searchBean.getDestRange()).to(searchBean.getDestCoordX() + searchBean.getDestRange()));
            destCoordQuery.must(QueryBuilders.rangeQuery("destCoordY").from(searchBean.getDestCoordY() - searchBean.getDestRange()).to(searchBean.getDestCoordY() + searchBean.getDestRange()));
        }

        // 检查 addressQuery 是否有子句（即是否所有字段都被填充了）
        if (destAddressQuery.hasClauses()) {
            destBoolQuery.should(destAddressQuery);
        }
        // 检查 coordQuery 是否有子句（即 destCoordX 和 destCoordY 是否都被填充了）
        if (destCoordQuery.hasClauses()) {
            destBoolQuery.should(destCoordQuery);
        }
        if (destBoolQuery.hasClauses()) {
            query.must(destBoolQuery);
        }

        if (searchBean.getQueryType() != null) {
            if (Objects.equals(searchBean.getQueryType(), 2)) {
                if (searchBean.getQuerySign() != null) {
                    query.must(QueryBuilders.rangeQuery("id").lt(searchBean.getQuerySign()));
                }
                if (searchBean.getMinTsId() != null) {
                    query.must(QueryBuilders.rangeQuery("id").lt(searchBean.getMinTsId()));
                }
            } else {
                if (searchBean.getQuerySign() != null && searchBean.getQuerySign() > 0) {
                    query.must(QueryBuilders.rangeQuery("id").gt(searchBean.getQuerySign()));
                }
            }
        }
        if (searchBean.getStartWeight() != null) {
            query.must(QueryBuilders.rangeQuery("weight").gt(0));
            query.must(QueryBuilders.rangeQuery("referWeight").gte(searchBean.getStartWeight()));
        }

        if (searchBean.getEndWeight() != null) {
            query.must(QueryBuilders.rangeQuery("weight").gt(0));
            query.must(QueryBuilders.rangeQuery("referWeight").lte(searchBean.getEndWeight()));
        }

        if (searchBean.getMinLength() != null) {
            query.must(QueryBuilders.rangeQuery("length").gt(0));
            query.must(QueryBuilders.rangeQuery("referLength").gte(searchBean.getMinLength()));
        }

        if (searchBean.getMaxLength() != null) {
            query.must(QueryBuilders.rangeQuery("length").gt(0));
            query.must(QueryBuilders.rangeQuery("referLength").lte(searchBean.getMaxLength()));
        }

        if (searchBean.getMinWidth() != null) {
            query.must(QueryBuilders.rangeQuery("wide").gt(0));
            query.must(QueryBuilders.rangeQuery("referWidth").gte(searchBean.getMinWidth()));
        }

        if (searchBean.getMaxWidth() != null) {
            query.must(QueryBuilders.rangeQuery("wide").gt(0));
            query.must(QueryBuilders.rangeQuery("referWidth").lte(searchBean.getMaxWidth()));
        }

        if (searchBean.getMinHeight() != null) {
            query.must(QueryBuilders.rangeQuery("high").gt(0));
            query.must(QueryBuilders.rangeQuery("referHeight").gte(searchBean.getMinHeight()));
        }

        if (searchBean.getMaxHeight() != null) {
            query.must(QueryBuilders.rangeQuery("high").gt(0));
            query.must(QueryBuilders.rangeQuery("referHeight").lte(searchBean.getMaxHeight()));
        }

        if (searchBean.getMinPrice() != null) {
            query.must(QueryBuilders.rangeQuery("price").gte(searchBean.getMinPrice()));
        }

        if (searchBean.getMaxPrice() != null) {
            query.must(QueryBuilders.rangeQuery("price").lte(searchBean.getMaxPrice()));
        }

        if (searchBean.getPublishType() != null) {
            query.must(QueryBuilders.termQuery("publishType", searchBean.getPublishType()));
        }

        if (searchBean.getRefundFlag() != null) {
            query.must(QueryBuilders.termQuery("refundFlag", searchBean.getRefundFlag()));
        }
        if (searchBean.getExcellentGoods() != null) {
            query.must(QueryBuilders.termQuery("excellentGoods", searchBean.getExcellentGoods()));
        }

        Date startLoadingTime = searchBean.getStartLoadingTime() == null ? null : new Date(searchBean.getStartLoadingTime());
        Date endLoadingTime = searchBean.getEndLoadingTime() == null ? null : new Date(searchBean.getEndLoadingTime());
        Boolean queryNullLoadingTime = searchBean.getQueryNullLoadingTime();
        if (startLoadingTime == null && endLoadingTime == null) {
            if (Boolean.TRUE.equals(queryNullLoadingTime)) {
                query.must(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("loadingTime")));
            }
        } else {
            if (startLoadingTime != null) {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                boolQuery.should(QueryBuilders.rangeQuery("loadingTime").gte(startLoadingTime));

                if (Boolean.TRUE.equals(queryNullLoadingTime)) {
                    boolQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("loadingTime")));
                }
                query.must(boolQuery);
            }

            if (endLoadingTime != null) {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                boolQuery.should(QueryBuilders.rangeQuery("loadingTime").lte(endLoadingTime));

                if (Boolean.TRUE.equals(queryNullLoadingTime)) {
                    boolQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("loadingTime")));
                }
                query.must(boolQuery);
            }
        }

        if (searchBean.getPriceFlag() != null) {
            if (searchBean.getPriceFlag() == 0) {
                //无价
                BoolQueryBuilder priceBoolQuery = QueryBuilders.boolQuery();
                priceBoolQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("price")));
                priceBoolQuery.should(QueryBuilders.rangeQuery("price").lte(0));
                query.must(priceBoolQuery);
            } else {
                //有价
                query.must(QueryBuilders.rangeQuery("price").gt(0));
            }
        }
        String goodsNames = searchBean.getGoodsName();
        if (StringUtils.isNotBlank(goodsNames)) {
            List<String> goodsNameList = Arrays.stream(goodsNames.split(COMMA)).filter(StringUtils::isNotBlank).toList();
            //商品名称 or 查询
            BoolQueryBuilder goodsNameBool = QueryBuilders.boolQuery();

            for (String oneName : goodsNameList) {
                if (StringUtils.isNotBlank(oneName)) {
                    goodsNameBool.should(QueryBuilders.matchPhraseQuery("taskContent", oneName));
                    //添加同义词
                    goodsNameBool.should(QueryBuilders.matchPhraseQuery("taskContent.synonym", oneName));
                }
            }
            query.must(goodsNameBool);
        }

        if (CollectionUtils.isNotEmpty(searchBean.getExcludeSrcMsgIds())) {
            query.mustNot(QueryBuilders.termsQuery("srcMsgId", searchBean.getExcludeSrcMsgIds()));
        }

        if (CollectionUtils.isNotEmpty(searchBean.getUseSrcMsgIds())) {
            query.must(QueryBuilders.termsQuery("srcMsgId", searchBean.getUseSrcMsgIds()));
        }

        if (CollectionUtils.isNotEmpty(searchBean.getExcludeUserIds())) {
            query.mustNot(QueryBuilders.termsQuery("userId", searchBean.getExcludeUserIds()));
        }

        if (Objects.nonNull(searchBean.getUseCarType())) {
            query.must(QueryBuilders.termQuery("useCarType", searchBean.getUseCarType()));
        }

        // 秒抢货源
        if (searchBean.getSeckillGoods() != null) {
            query.must(QueryBuilders.termQuery("seckillGoods", searchBean.getSeckillGoods()));
        }

        // 好货推荐 （好1、好2、好3、中1、中2、中3），
        if (Objects.equals(searchBean.getGoodTransportRecommend(), 1)) {
            query.must(QueryBuilders.rangeQuery("goodTransportLabel").gt(0).lt(30));
        }

        return query;
    }

    /**
     * 组装评分函数
     */
    private FunctionScoreQueryBuilder.FilterFunctionBuilder[] buildScoreFunction(IntelligenceSortDTO aiSortDTO) {
        // 1. 获取所有规则并构建Map
        List<IntelligenceSortRuleDO> ruleList = intelligenceSortRuleMapper.selectAll();
        Map<Integer, IntelligenceSortRuleDO> sortRuleMap = ruleList.stream()
                .collect(Collectors.toMap(IntelligenceSortRuleDO::getRuleType, Function.identity(), (oldVal, newVal) -> newVal));

        // 2. 构建评分函数
        List<FunctionScoreQueryBuilder.FilterFunctionBuilder> functionBuilders = new ArrayList<>();

        for (Map.Entry<Integer, IntelligenceSortRuleDO> entry : sortRuleMap.entrySet()) {
            Integer ruleType = entry.getKey();
            IntelligenceSortRuleDO rule = entry.getValue();
            BigDecimal weight = rule.getWeight();
            // 权重<=0的，直接不参与排序
            if (weight.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            // 3. 获取对应的构建方法
            BiFunction<IntelligenceSortDTO, IntelligenceSortRuleDO, Script> scriptBuilder = SCORE_SCRIPT_BUILDERS.get(ruleType);
            if (scriptBuilder != null) {
                Script script = scriptBuilder.apply(aiSortDTO, rule);
                if (script != null) {
                    // 4. 脚本+权重值构建排序function
                    ScriptScoreFunctionBuilder function = new ScriptScoreFunctionBuilder(script).setWeight(weight.floatValue());
                    functionBuilders.add(new FunctionScoreQueryBuilder.FilterFunctionBuilder(function));
                }
            }
        }

        return functionBuilders.toArray(new FunctionScoreQueryBuilder.FilterFunctionBuilder[0]);
    }

    /**
     * 质量分评分
     * 格式：{"price":[{"modelScore":5,"score":10},{"modelScore":15,"score":20}], "noprice":[{"modelScore":5,"score":10},{"modelScore":15,"score":20}]}
     * 分有价price和无价noprice，modelScore是质量分，有价质量分<=5得分10
     */
    private Script buildModelScript(IntelligenceSortDTO aiSortDTO, IntelligenceSortRuleDO ruleDO) {
        Map<String, Object> params = JSONUtil.toBean(ruleDO.getRule(), Map.class);

        return new Script(ScriptType.INLINE, // 脚本类型
                Script.DEFAULT_SCRIPT_LANG, // 脚本语言
                """
                        def price = doc['price'].size() > 0 ? (double)(doc['price'].value): 0;
                        def goodModelScore = doc['goodModelScore'].size() > 0 ? doc['goodModelScore'].value: 0.0;
                        def list = price == 0 ? params.noprice : params.price;
                        for (def item : list) {
                            if (goodModelScore <= item.modelScore) {
                                return item.score;
                            }
                        }
                        return 100;
                        """, params);
    }

    /**
     * 车货吨位匹配评分
     * 格式：
     * [{"floatDown":0,"score":100},{"floatDown":20,"score":80},{"floatDown":30,"score":50},{"floatDown":40,"score":20},{"floatDown":40,"score":0},
     * {"floatUp":0,"score":100},{"floatUp":20,"score":80},{"floatUp":30,"score":50},{"floatUp":40,"score":20},{"floatUp":40,"score":0}]
     * floatDown向下浮动，floatDown=0完全匹配得分100，floatDown下浮动20%得发80，floatDown最后一个是浮动上限，超过40%得分0
     * floatUp向下浮动，floatUp=0完全匹配得分100，floatUp下浮动20%得发80，floatUp最后一个是浮动上限，超过40%得分0
     */
    private Script buildWeightScript(IntelligenceSortDTO aiSortDTO, IntelligenceSortRuleDO ruleDO) {
        List<Map<String,Number>> weightList = getBuildWeightScriptParams(aiSortDTO.getUserId(), aiSortDTO.getCarId(), ruleDO);
        if (weightList == null) {
            return null;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("list", weightList);

        return new Script(ScriptType.INLINE, // 脚本类型
                Script.DEFAULT_SCRIPT_LANG, // 脚本语言
                """
                        def weight = Double.parseDouble(doc['weight'].value);
                        for (def item : params.list) {
                            if (weight >= item.weightL && weight <= item.weightR) {
                                return item.score;
                            }
                        }
                        return 0;
                        """, params);
    }

    /**
     * 获取构建车货吨位匹配评分参数
     */
    private List<Map<String, Number>> getBuildWeightScriptParams(Long userId, Long carId, IntelligenceSortRuleDO ruleDO) {
        try {
            if (userId == null || carId == null) {
                return null;
            }
            // 优先取缓存
            String cacheKey = "tyt:inSort:weight:params:" + userId + ":" + carId;
            String cacheValue = redisUtil.getString(cacheKey);
            if (StringUtils.isNotBlank(cacheValue)) {
                return JSON.parseObject(cacheValue, new TypeReference<List<Map<String, Number>>>() {});
            }

            // 查询BI车辆重量表，获取车型吨重—上下限
            List<DwsCarTypeDO> carTypeList = dwsCarTypeMapper.getWeightMinByUserId(userId.toString());
            if (carTypeList.isEmpty()) {
                return null;
            }
            // 优先取匹配的车型吨重—下限，如果没有，取吨重标签—下限
            DwsCarTypeDO dwsCarTypeDO = carTypeList.stream()
                    .filter(t -> Objects.equals(t.getCarId(), carId.toString()))
                    .findAny()
                    .orElse(carTypeList.get(0));
            // 下浮取吨重下限，上浮取吨重上限
            BigDecimal weightMin = StringUtils.isNotBlank(dwsCarTypeDO.getCarWeightMin()) ?
                    new BigDecimal(dwsCarTypeDO.getCarWeightMin()) :
                    StringUtils.isNotBlank(dwsCarTypeDO.getWeightMin()) ?
                            new BigDecimal(dwsCarTypeDO.getWeightMin()) : null;
            BigDecimal weightMax = StringUtils.isNotBlank(dwsCarTypeDO.getCarWeightMax()) ?
                    new BigDecimal(dwsCarTypeDO.getCarWeightMax()) :
                    StringUtils.isNotBlank(dwsCarTypeDO.getWeightMax()) ?
                            new BigDecimal(dwsCarTypeDO.getWeightMax()) : null;
            if (weightMin == null || weightMax == null) {
                return null;
            }

            List<IntelligentSortRuleWeight> ruleWeightList = JSONUtil.toList(ruleDO.getRule(), IntelligentSortRuleWeight.class);
            List<Map<String, Number>> weightList = new ArrayList<>();

            // 第一条是完全匹配，区间 [weightMin, weightMax]
            weightList.add(Map.of("weightL", weightMin.floatValue(), "weightR", weightMax.floatValue(), "score", ruleWeightList.get(0).getScore()));

            // 再处理下浮区间，下浮按weightMin取值
            float weightL = weightMin.floatValue(); // 吨重左区间
            float weightR = weightMin.floatValue(); // 吨重右区间
            for (IntelligentSortRuleWeight t : ruleWeightList) {
                if (t.getFloatDown() != null && t.getFloatDown() > 0) {
                    BigDecimal weight = weightMin.multiply(HUNDRED.subtract(new BigDecimal(t.getFloatDown().toString())))
                            .divide(HUNDRED, 2, RoundingMode.HALF_DOWN);
                    weightL = weight.floatValue();
                    Map<String, Number> map = new HashMap<>(Map.of("weightL", weightL, "weightR", weightR, "score", t.getScore()));
                    weightList.add(map);
                    weightR = weightL;
                }
            }
            weightList.get(weightList.size() - 1).put("weightL", 0f);// 最后一条左区间设为0

            // 再处理上浮区间，上浮按weightMax取值
            weightL = weightMax.floatValue(); // 吨重左区间
            weightR = weightMax.floatValue(); // 吨重右区间
            for (IntelligentSortRuleWeight t : ruleWeightList) {
                if (t.getFloatUp() != null && t.getFloatUp() > 0) {
                    BigDecimal weight = weightMax.multiply(HUNDRED.add(new BigDecimal(t.getFloatUp().toString())))
                            .divide(HUNDRED, 2, RoundingMode.HALF_DOWN);
                    weightR = weight.floatValue();
                    Map<String, Number> map = new HashMap<>(Map.of("weightL", weightL, "weightR", weightR, "score", t.getScore()));
                    weightList.add(map);
                    weightL = weightR;
                }
            }
            weightList.get(weightList.size() - 1).put("weightR", 10000f);// 最后一条右区间设为10000

            redisUtil.set(cacheKey, JSON.toJSONString(weightList), Duration.ofHours(1));
            return weightList;
        } catch (Exception e) {
            log.error("构建车货吨位匹配评分参数异常：", e);
            return null;
        }
    }

    /**
     * 信息完整度分数计算
     * 格式：{"fiveScore":100,"fourScore":70,"threeScore":40,"twoScore":10,"oneScore":10}
     * fiveScore 5项完整得分100
     */
    private Script buildIntegrityScript(IntelligenceSortDTO aiSortDTO, IntelligenceSortRuleDO ruleDO) {

        String rule = ruleDO.getRule();
        Map<String, Object> params = JSONUtil.toBean(rule, Map.class);

        return new Script(ScriptType.INLINE, // 脚本类型
                Script.DEFAULT_SCRIPT_LANG, // 脚本语言
                """
                          def startArea =  doc['startArea'].size() > 0 ? doc['startArea'].value: null;
                          def startDetailAdd = doc['startDetailAdd.keyword'].size() > 0 ? doc['startDetailAdd.keyword'].value: null;
                          def destArea =  doc['destArea'].size() > 0 ? doc['destArea'].value: null;
                          def destDetailAdd =  doc['destDetailAdd.keyword'].size() > 0 ? doc['destDetailAdd.keyword'].value: null;
                          def length = doc['length'].size() > 0 ? doc['length'].value: null;
                          def wide =  doc['wide'].size() > 0 ? doc['wide'].value: null;
                          def high =  doc['high'].size() > 0 ? doc['high'].value: null;
                          def count = 0;
                          if (startDetailAdd != null && startDetailAdd != startArea ) {
                              count++;
                          }
                          if (destDetailAdd != null && destDetailAdd != destArea) {
                              count++;
                          }
                          if (length != null) {
                              count++;
                          }
                          if (wide != null) {
                              count++;
                          }
                          if (high != null) {
                              count++;
                          }
                          return count == 5? params.fiveScore : count == 4 ? params.fourScore : count == 3 ? params.threeScore : count == 2 ? params.twoScore : count == 1 ? params.oneScore : 0;
                        """, params);
    }

    /**
     * 发布时间分数计算
     * 格式：[{"score":100,"ageing":0.25},{"score":80,"ageing":0.5},{"score":60,"ageing":0.75},{"score":40,"ageing":1},{"score":20,"ageing":3},{"score":5,"ageing":12}]
     * ageing发货失效<=0.25得分100
     */
    private Script buildTimeScript(IntelligenceSortDTO aiSortDTO, IntelligenceSortRuleDO ruleDO) {

        String rule = ruleDO.getRule();
        List<IntelligentSortRuleAgeing> ageingList = JSONUtil.toList(rule, IntelligentSortRuleAgeing.class);

        Map<String, Integer> ageingScores = new TreeMap<>(Comparator.comparingLong(Long::parseLong));
        for (IntelligentSortRuleAgeing ageing : ageingList) {
            long ageingLong = ageing.getAgeing() * 60 * 60 * 1000;
            ageingScores.put(Long.toString(ageingLong), ageing.getScore());
        }

        Map<String, Object> params = new HashMap<>();
        params.put("firstSearchTime", aiSortDTO.getEsSearchExtra().getFirstSearchTime());
        params.put("ageingScore", ageingScores);

        return new Script(ScriptType.INLINE, // 脚本类型
                Script.DEFAULT_SCRIPT_LANG, // 脚本语言
                """
                            def releaseTime = doc['releaseTime'].value != null ? doc['releaseTime'].value.toInstant().toEpochMilli() : 0L;
                            def currentTime = params.firstSearchTime;
                            def timeDifference = currentTime - releaseTime;
                            def ageingScores = params.ageingScore;
                            for (entry in ageingScores.entrySet()) {
                                def ageingThreshold = Long.parseLong(entry.getKey());
                                if (timeDifference < ageingThreshold) {
                                    return entry.getValue();
                                }
                            }
                          return 0;
                        """, params);
    }

    /**
     * 地址匹配
     * 格式：{"cityCityScore":100,"cityProvinceScore":90,"provinceCityScore":80,"provinceProvinceScore":70,"startCityScore":60,"startProvinceScore":50,"destCityScore":40,"destProvinceScore":30}
     * cityCityScore出发地市目的地市匹配得分；cityProvinceScore出发地市目的地省匹配得分；startCityScore只出发地市匹配得分；后面类似
     */
    private Script buildAddressScript(IntelligenceSortDTO aiSortDTO, IntelligenceSortRuleDO ruleDO) {

        String rule = ruleDO.getRule();
        Map<String, Object> addressRule = JSONUtil.toBean(rule, Map.class);

        Map<String, Object> params = new HashMap<>();
        params.putAll(addressRule);
        params.put("startCity", aiSortDTO.getStartCity());
        params.put("destCity", aiSortDTO.getDestCity());
        params.put("startProvinc", aiSortDTO.getStartProvinc());
        params.put("destProvinc", aiSortDTO.getDestProvinc());
        params.put("startArea", aiSortDTO.getStartArea());

        return new Script(ScriptType.INLINE, // 脚本类型
                Script.DEFAULT_SCRIPT_LANG, // 脚本语言
                """
                            boolean sc = doc['startCity'].value == params.startCity;
                            boolean dc = doc['destCity'].value == params.destCity;
                            boolean sp = doc['startProvinc'].value == params.startProvinc;
                            boolean dp = doc['destProvinc'].value == params.destProvinc;
                            if(sc && dc) return params.cityCityScore;
                            else if(sc && dp) return params.cityProvinceScore;
                            else if(sp && dc) return params.provinceCityScore;
                            else if(sp && dp) return params.provinceProvinceScore;
                            else if(sc) return params.startCityScore;
                            else if(dc) return params.destCityScore;
                            else if(sp) return params.startProvinceScore;
                            else if(dp) return params.destProvinceScore;
                            else return 0;
                        """, params);
    }

    /**
     * 刷新规则评分
     * 格式：{"useCard":[{"interval":5,"score":100},{"interval":30,"score":80},{"interval":60,"score":20}],"other":[{"interval":5,"score":50},{"interval":30,"score":40},{"interval":60,"score":0}]}
     * useCard是使用曝光卡刷新，other是非曝光卡刷新，interval是距上次刷新间隔，score是分数
     */
    private Script buildRefreshScript(IntelligenceSortDTO aiSortDTO, IntelligenceSortRuleDO ruleDO) {
        Map<String, Object> params = JSONUtil.toBean(ruleDO.getRule(), Map.class);
        params.put("now", aiSortDTO.getEsSearchExtra().getFirstSearchTime()); // 把请求的时间戳放进去

        return new Script(ScriptType.INLINE, // 脚本类型
                Script.DEFAULT_SCRIPT_LANG, // 脚本语言
                """
                        def topFlag = doc['topFlag'].size() > 0? doc['topFlag'].value : 0;
                        def list = topFlag == 3? params.useCard : params.other;
                        def ctimeStamp = doc['ctime'].value.toInstant().toEpochMilli();
                        def timeDifference = (params.now - ctimeStamp) / 1000 / 60;
                        for (def item : list) {
                            if (timeDifference <= item.interval) {
                                return item.score;
                            }
                        }
                        return 0;
                        """, params);
    }

    /**
     * 距离规则评分
     * 1. 搜索条件的出发地，距离货源发货地的距离，从小到大排序（距离按照市到市来计算）
     * 2. 若出发地是省，则距离都相同（因为省没有外扩，省内一视同仁）
     * 格式：{"start":[{"distance":5,"score":100},{"distance":15,"score":20}], "dest":[{"distance":5,"score":100},{"distance":15,"score":20}]}
     * 分 start 出发地 和 dest 目的地，distance是距离，从小到大排序，如距离<=5km，赋值100分，<=15km，赋值20分
     */
    private Script buildDistanceScript(IntelligenceSortDTO aiSortDTO, IntelligenceSortRuleDO ruleDO) {
        // 如果出发地和目的地经纬度都为空，该因子不生效
        if (StringUtils.isBlank(aiSortDTO.getStartLonLat()) && StringUtils.isBlank(aiSortDTO.getDestLonLat())) {
            return null;
        }

        Map<String, Object> params = JSONUtil.toBean(ruleDO.getRule(), Map.class);
        // 把出发地、目的地经纬度也传进去
        if (StringUtils.isNotBlank(aiSortDTO.getStartLonLat())) {
            String[] split = aiSortDTO.getStartLonLat().split(",");
            params.put("startLon", Float.parseFloat(split[0]));
            params.put("startLat", Float.parseFloat(split[1]));
        }
        if (StringUtils.isNotBlank(aiSortDTO.getDestLonLat())) {
            String[] split = aiSortDTO.getDestLonLat().split(",");
            params.put("destLon", Float.parseFloat(split[0]));
            params.put("destLat", Float.parseFloat(split[1]));
        }

        // ES7.0已经移除GeoPoint.arcDistance(...) 方法，需要自己实现Haversine公式手动计算两点间的距离
        // 返回距离（米），R是地球半径（米）
        String haversineFunction = """
                    double haversine(double lat1, double lon1, double lat2, double lon2) {
                        double R = 6371000;
                        double phi1 = Math.toRadians(lat1);
                        double phi2 = Math.toRadians(lat2);
                        double deltaPhi = Math.toRadians(lat2 - lat1);
                        double deltaLambda = Math.toRadians(lon2 - lon1);
                        double a = Math.sin(deltaPhi/2) * Math.sin(deltaPhi/2) +
                                   Math.cos(phi1) * Math.cos(phi2) *
                                   Math.sin(deltaLambda/2) * Math.sin(deltaLambda/2);
                        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
                        return R * c;
                    }
                """;
        return new Script(ScriptType.INLINE, // 脚本类型
                Script.DEFAULT_SCRIPT_LANG, // 脚本语言
                haversineFunction + """
                             def score = 0;
                             if(params.startLon != null && params.startLat != null) {
                                double lat1 = (double)doc['startLatitude'].value / 1000000;
                                double lon1 = (double)doc['startLongitude'].value / 1000000;
                                double km = haversine(lat1, lon1, params.startLat, params.startLon) / 1000;
                                for (def item : params.start) {
                                    if (km <= item.distance) {
                                        score += item.score;
                                        break;
                                    }
                                }
                             }
                             if(params.destLon != null && params.destLat != null) {
                                double lat1 =(double)doc['destLatitude'].value / 1000000;
                                 double lon1 = (double)doc['destLongitude'].value / 1000000;
                                 double km = haversine(lat1, lon1, params.destLat, params.destLon)/ 1000;
                                 for (def item : params.dest) {
                                     if (km <= item.distance) {
                                         score += item.score;
                                         break;
                                     }
                                 }
                             }
                             return score;
                        """, params);
    }

    /**
     * 好货规则评分
     * 格式：[{"level":0,"score":100},{"level":20,"score":80},{"level":21,"score":50},{"level":22,"score":20},{"level":0,"score":0}]
     * level是好货标签的值，value是分数。好货标签：11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2，0:其他
     */
    private Script buildLevelScript(IntelligenceSortDTO aiSortDTO, IntelligenceSortRuleDO ruleDO) {
        Map<String, Object> params = new HashMap<>();
        params.put("list", JSON.parseArray(ruleDO.getRule(), Map.class));

        return new Script(ScriptType.INLINE, // 脚本类型
                Script.DEFAULT_SCRIPT_LANG, // 脚本语言
                """
                            if(doc['goodTransportLabel'].size() > 0) {
                                def t = doc['goodTransportLabel'].value;
                                for (def item : params.list) {
                                    if (t == item.level) {
                                        return item.score;
                                    }
                                }
                            }
                            return 0;
                        """, params);
    }

    /**
     * 公里单价规则评分
     * 格式：[{"score":100,"unitPrice":0.76},{"score":80,"unitPrice":0.61},{"score":60,"unitPrice":0.60},{"score":40,"unitPrice":0.58},{"score":20,"unitPrice":0.54},{"score":5,"unitPrice":0.50}]
     * score是分数，unitPrice的公里单价，按价格倒序
     */
    private Script buildPriceScript(IntelligenceSortDTO aiSortDTO, IntelligenceSortRuleDO ruleDO) {
        Map<String, Object> params = new HashMap<>();
        params.put("list", JSON.parseArray(ruleDO.getRule(), Map.class));

        return new Script(ScriptType.INLINE, // 脚本类型
                Script.DEFAULT_SCRIPT_LANG, // 脚本语言
                """
                            if(doc['price'].size() > 0 && doc['distance'].size() > 0) {
                                def price = doc['price'].value;
                                def distance = doc['distance'].value / 100;
                                def unitPrice = (float)price / distance;
                                for (def item : params.list) {
                                    if (unitPrice >= item.unitPrice) {
                                        return item.score;
                                    }
                                }
                            }
                            return 0;
                        """, params);
    }


    public static String getTransportIndex() {
        String formatDate = DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMATTER);
        return "transport_" + formatDate;
    }


    @Override
    public List<TransportEsDO> searchHallList(BaseTransportSearchDTO searchDTO) {

        List<TransportEsDO> transportEsDOList = new ArrayList<>();

        try {
            BoolQueryBuilder query = buildBaseQuery(searchDTO);
            // 组装查询条件
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(query).size(searchDTO.getPageSize()).sort(SortBuilders.fieldSort("id").order(SortOrder.DESC));

            SearchRequest searchRequest = new SearchRequest(getTransportIndex());
            searchRequest.source(sourceBuilder);

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = searchResponse.getHits().getHits();
            for (SearchHit hit : hits) {
                TransportEsDO transportEsDO = BeanUtil.mapToBean(hit.getSourceAsMap(), TransportEsDO.class, true, CopyOptions.create());
                transportEsDOList.add(transportEsDO);
            }
        } catch (Exception e) {
            log.error("找货大厅列表查询错误,参数{}", JSONUtil.toJsonStr(searchDTO), e);
        }
        return transportEsDOList;
    }

    @Override
    public List<TransportEsDO> searchSeckillBannerList(BaseTransportSearchDTO searchDTO) {

        List<TransportEsDO> transportEsDOList = new ArrayList<>();

        try {
            BoolQueryBuilder query = buildBaseQuery(searchDTO);
            // 组装查询条件
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(query).size(searchDTO.getPageSize())
                    // 与找货大厅搜索唯一的区别就是排序不同：秒抢货源banner列表随机排序
                    .sort(new ScriptSortBuilder(new Script("Math.random()"), ScriptSortBuilder.ScriptSortType.NUMBER));

            SearchRequest searchRequest = new SearchRequest(getTransportIndex());
            searchRequest.source(sourceBuilder);

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = searchResponse.getHits().getHits();
            for (SearchHit hit : hits) {
                TransportEsDO transportEsDO = BeanUtil.mapToBean(hit.getSourceAsMap(), TransportEsDO.class, true, CopyOptions.create());
                transportEsDOList.add(transportEsDO);
            }
        } catch (Exception e) {
            log.error("好差货查询错误,参数{}", JSONUtil.toJsonStr(searchDTO), e);
        }
        return transportEsDOList;
    }

    /**
     * 根据srcMsgId查询ES货源
     *
     * @param srcMsgIdList
     */
    @Override
    public List<TransportEsDO> selectBySrcMsgIds(List<Long> srcMsgIdList) {
        List<TransportEsDO> transportEsDOList = new ArrayList<>();

        try {
            // 组装查询条件
            BoolQueryBuilder query = QueryBuilders.boolQuery();
            query.must(QueryBuilders.termQuery("status", 1))
                    .must(QueryBuilders.termsQuery("srcMsgId", srcMsgIdList));

            SearchRequest searchRequest = new SearchRequest(getTransportIndex());
            searchRequest.source(new SearchSourceBuilder().query(query));

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = searchResponse.getHits().getHits();
            for (SearchHit hit : hits) {
                TransportEsDO transportEsDO = BeanUtil.mapToBean(hit.getSourceAsMap(), TransportEsDO.class, true, CopyOptions.create());
                transportEsDOList.add(transportEsDO);
            }
        } catch (Exception e) {
            log.error("根据srcMsgId查询ES货源报错,参数{}", srcMsgIdList, e);
        }
        return transportEsDOList;
    }


    @Override
    public Long searchHallCount(SearchHallCountDTO searchDTO) {

        try {
            BoolQueryBuilder query = buildBaseQuery(searchDTO);
            // 时间排序气泡接口，要大于传过来的最大id
            if (searchDTO.getMaxTsId() != null && searchDTO.getMaxTsId() >= 0) {
                query.must(QueryBuilders.rangeQuery("id").gt(searchDTO.getMaxTsId()));
            }
            CountRequest countRequest = new CountRequest(getTransportIndex());
            countRequest.query(query);
            return restHighLevelClient.count(countRequest, RequestOptions.DEFAULT).getCount();
        } catch (Exception e) {
            log.error("时间排序查询气泡错误,参数{}", JSONUtil.toJsonStr(searchDTO), e);
        }
        return 0L;
    }

    @Override
    public List<TransportEsDO> searchFixedTransport(BaseTransportSearchDTO searchDTO) {
        List<TransportEsDO> transportEsDOList = new ArrayList<>();

        try {
            // 不要好差货的好货，也就是秒抢货源
            searchDTO.setSeckillGoods(YesNoEnum.N.getCode());
            //
            BoolQueryBuilder query = buildBaseQuery(searchDTO);
            // 基本条件完成后，还只要首发时间在30分钟之前的货源
            Date date = DateUtil.offsetMinute(new Date(), -30).toJdkDate();
            query.must(QueryBuilders.rangeQuery("releaseTime").lt(date));

            // 只要优车2.0或者是有价的货源
            query.must(QueryBuilders.boolQuery().should(QueryBuilders.termQuery("excellentGoodsTwo", ExcellentGoodsTwoEnum.EXCELLENT_GOODS_TWO.getCode()))
                    .should(QueryBuilders.rangeQuery("price").gt(0)));

            // 组装查询条件
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            // 一次查出200条货源
            sourceBuilder.query(query).size(200);

            SearchRequest searchRequest = new SearchRequest(getTransportIndex());
            searchRequest.source(sourceBuilder);

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = searchResponse.getHits().getHits();
            for (SearchHit hit : hits) {
                TransportEsDO transportEsDO = BeanUtil.mapToBean(hit.getSourceAsMap(), TransportEsDO.class, true, CopyOptions.create());
                transportEsDOList.add(transportEsDO);
            }
        } catch (Exception e) {
            log.error("固定位货源列表查询错误,参数{}", JSONUtil.toJsonStr(searchDTO), e);
        }
        return transportEsDOList;
    }

    @Override
    public List<TransportEsDO> searchHallCountDetail(SearchHallCountDTO searchDTO) {

        List<TransportEsDO> transportEsDOList = new ArrayList<>();

        try {
            BoolQueryBuilder query = buildBaseQuery(searchDTO);
            // 时间排序气泡接口，要大于传过来的最大id
            if (searchDTO.getMaxTsId() != null && searchDTO.getMaxTsId() > 0) {
                query.must(QueryBuilders.rangeQuery("id").gt(searchDTO.getMaxTsId()));
            }
            // 组装查询条件
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(query).size(searchDTO.getPageSize()).sort(SortBuilders.fieldSort("id").order(SortOrder.DESC));

            SearchRequest searchRequest = new SearchRequest(getTransportIndex());
            searchRequest.source(sourceBuilder);

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = searchResponse.getHits().getHits();
            for (SearchHit hit : hits) {
                TransportEsDO transportEsDO = BeanUtil.mapToBean(hit.getSourceAsMap(), TransportEsDO.class, true, CopyOptions.create());
                transportEsDOList.add(transportEsDO);
            }
        } catch (Exception e) {
            log.error("找货大厅列气泡表查询错误,参数{}", JSONUtil.toJsonStr(searchDTO), e);
        }
        return transportEsDOList;
    }

    /**
     * 常跑路线气泡数量
     *
     * @param searchDTO
     */
    @Override
    public Long oftenRouteBubbleCount(OftenRouteSearchDTO searchDTO) {
        CountRequest countRequest = new CountRequest(getTransportIndex());
        BoolQueryBuilder boolQuery = this.buildOftenRouteQuery(searchDTO);
        // 新增气泡数从第一次查询的时间开始算起
        boolQuery.must(QueryBuilders.rangeQuery("releaseTime").gt(searchDTO.getSearchRouteExtra().getFirstSearchTime()));

        countRequest.query(boolQuery);
        log.info("常跑路线气泡数量查询参数{}", JSONUtil.toJsonStr(countRequest));
        try {
            return restHighLevelClient.count(countRequest, RequestOptions.DEFAULT).getCount();
        } catch (IOException e) {
            log.error("常跑路线气泡数量查询错误,参数{}", JSONUtil.toJsonStr(searchDTO), e);
        }
        return 0L;
    }

    /**
     * 常跑路线找货大厅列表
     *
     * @param searchDTO
     */
    @Override
    public List<TransportEsDO> oftenRouteHallList(OftenRouteSearchDTO searchDTO) {
        List<TransportEsDO> transportEsDOList = new ArrayList<>();
        try {
            BoolQueryBuilder query = this.buildOftenRouteQuery(searchDTO);

            // 组装查询条件
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(query).size(searchDTO.getPageSize())
                    .sort(SortBuilders.fieldSort("releaseTime").order(SortOrder.DESC))
                    .sort(SortBuilders.fieldSort("srcMsgId").order(SortOrder.DESC));
            // 添加分页条件，按分数倒序，按id降序
            SearchRouteExtraDTO esSearchExtra = searchDTO.getSearchRouteExtra();
            if (esSearchExtra.getLastSortValues() != null) {
                sourceBuilder.searchAfter(esSearchExtra.getLastSortValues());
            }

            SearchRequest searchRequest = new SearchRequest(getTransportIndex());
            searchRequest.source(sourceBuilder);

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = searchResponse.getHits().getHits();
            for (SearchHit hit : hits) {
                transportEsDOList.add(BeanUtil.mapToBean(hit.getSourceAsMap(), TransportEsDO.class, true, CopyOptions.create()));
                esSearchExtra.setLastSortValues(hit.getSortValues());
            }
        } catch (Exception e) {
            log.error("常跑路线找货大厅列表查询错误,参数{}", JSONUtil.toJsonStr(searchDTO), e);
        }
        return transportEsDOList;
    }

    /**
     * 组装常跑路线查询条件
     */
    private BoolQueryBuilder buildOftenRouteQuery(OftenRouteSearchDTO searchDTO) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.termQuery("status", 1))
                .must(QueryBuilders.termQuery("displayType", 1))
                .must(QueryBuilders.termQuery("isDisplay", 1))
                .must(QueryBuilders.termQuery("isShow", 1));

        if (searchDTO.getPriceFlag() != null) {
            if (searchDTO.getPriceFlag() == 0) {
                query.must(QueryBuilders.boolQuery()
                        .should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("price")))
                        .should(QueryBuilders.rangeQuery("price").lte(0)));
            } else {
                query.must(QueryBuilders.rangeQuery("price").gt(0));
            }
        }
        if (searchDTO.getPublishType() != null) {
            query.must(QueryBuilders.termQuery("publishType", searchDTO.getPublishType()));
        }
        if (searchDTO.getRefundFlag() != null) {
            query.must(QueryBuilders.termQuery("refundFlag", searchDTO.getRefundFlag()));
        }

        if (CollectionUtils.isNotEmpty(searchDTO.getExcludeUserIds())) {
            query.mustNot(QueryBuilders.termsQuery("userId", searchDTO.getExcludeUserIds()));
        }
        if (CollectionUtils.isNotEmpty(searchDTO.getExcludeSrcMsgIds())) {
            query.mustNot(QueryBuilders.termsQuery("srcMsgId", searchDTO.getExcludeSrcMsgIds()));
        }

        // 每条路线匹配吨重+出发地+目的地，地址可多选
        BoolQueryBuilder allRouteBoolQuery = QueryBuilders.boolQuery();
        for (UserOftenRouteDTO userOftenRoute : searchDTO.getUserOftenRoutes()) {
            BoolQueryBuilder routeBoolQuery = QueryBuilders.boolQuery();
            routeBoolQuery.must(QueryBuilders.rangeQuery("referWeight")
                    .from(userOftenRoute.getMinW() * 100)
                    .to(userOftenRoute.getMaxW() * 100));

            routeBoolQuery.must(buildOftenRouteQuery(userOftenRoute.getStartAddr(), "start"));
            routeBoolQuery.must(buildOftenRouteQuery(userOftenRoute.getDestAddr(), "dest"));
            allRouteBoolQuery.should(routeBoolQuery);
        }
        query.must(allRouteBoolQuery);

        return query;
    }

    private BoolQueryBuilder buildOftenRouteQuery(List<UserOftenRouteDTO.RouteAddressDTO> routeAddressDTOList, String prefix) {
        BoolQueryBuilder addressBoolQuery = QueryBuilders.boolQuery();

        for (UserOftenRouteDTO.RouteAddressDTO routeAddr : routeAddressDTOList) {
            BoolQueryBuilder singleAddressBoolQuery = QueryBuilders.boolQuery();
            if (StringUtils.isNotBlank(routeAddr.getP())) {
                singleAddressBoolQuery.must(QueryBuilders.termsQuery(prefix + "Provinc", routeAddr.getP()));
            }
            if (StringUtils.isNotBlank(routeAddr.getC())) {
                singleAddressBoolQuery.must(QueryBuilders.termsQuery(prefix + "City", routeAddr.getC()));
            }
            if (StringUtils.isNotBlank(routeAddr.getA())) {
                singleAddressBoolQuery.must(QueryBuilders.termsQuery(prefix + "Area", routeAddr.getA()));
            }
            addressBoolQuery.should(singleAddressBoolQuery);
        }

        return addressBoolQuery;
    }
}
