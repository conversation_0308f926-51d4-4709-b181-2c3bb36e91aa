package com.teyuntong.goods.search.service.biz.group.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.search.service.biz.group.entity.TytGroupQrCodeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@Mapper
@DS("tyt")
public interface TytGroupQrCodeMapper extends BaseMapper<TytGroupQrCodeDO> {

    TytGroupQrCodeDO getGroupQRCodeByProvinceCity(@Param("city") String city, @Param("startDate") Date startDate);

}
