<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.teyuntong</groupId>
        <artifactId>tyt-goods-search</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>tyt-goods-search-schedule</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.teyuntong</groupId>
            <artifactId>tyt-infra-common-xxl-job-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.teyuntong</groupId>
            <artifactId>tyt-goods-search-service</artifactId>
        </dependency>
    </dependencies>
</project>
