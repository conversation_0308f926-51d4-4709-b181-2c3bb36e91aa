package com.teyuntong.goods.search.mybatis.generator;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.TemplateType;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.query.SQLQuery;
import org.apache.commons.lang3.RegExUtils;
import org.jetbrains.annotations.NotNull;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;

public class MyBatisPlusGenerator {

    /**
     * dev 数据源配置
     */
    private static final String DEV_URL = "******************************************************" +
            ".com:3306/tyt?useUnicode=true&characterEncoding=UTF-8";
    private static final String DEV_USERNAME = "tyt_dev";
    private static final String DEV_PASSWORD = "tyt_dev#20200724";
    private static final DataSourceConfig.Builder DEV_DATASOURCE = new DataSourceConfig
            .Builder(DEV_URL, DEV_USERNAME, DEV_PASSWORD)
            .databaseQueryClass(SQLQuery.class); // 设置SQL查询方式，默认的是元数据查询方式

    /**
     * test 数据源配置
     */
    private static final String TEST_URL = "******************************************************" +
            ".com:3306/tyt?useUnicode=true&characterEncoding=UTF-8";
    private static final String TEST_USERNAME = "tyt_test";
    private static final String TEST_PASSWORD = "tyt_testDB_20200608";
    private static final DataSourceConfig.Builder TEST_DATASOURCE = new DataSourceConfig
            .Builder(TEST_URL, TEST_USERNAME, TEST_PASSWORD)
            .databaseQueryClass(SQLQuery.class); // 设置SQL查询方式，默认的是元数据查询方式

    public static void main(String[] args) {

        String property = System.getProperty("user.dir");
        System.out.println(property);

        FastAutoGenerator.create(DEV_DATASOURCE)
                // 全局配置
                .globalConfig(
                        (scanner, builder) -> builder
                                .outputDir(".generate")
                                .dateType(DateType.ONLY_DATE)
                )
                // 包配置
                .packageConfig(
                        (scanner, builder) -> builder
                                .parent(scanner.apply("请输入包名"))
                )

                // 关闭 controller 生成
                .templateConfig((scanner, builder) -> builder.disable(TemplateType.CONTROLLER)
                        .service("/templates/service.java")
                        .serviceImpl("/templates/serviceImpl.java"))
                // 策略配置
                .strategyConfig(getStrategyConfig())
                // 使用 Freemarker引擎，因为Velocity自定义模板会报找不到模板的异常
                .templateEngine(new FreemarkerTemplateEngine())
                /*
                    模板引擎配置，默认 Velocity 可选模板引擎 Beetl 或 Freemarker
                   .templateEngine(new BeetlTemplateEngine())
                 */
                .execute();
    }

    @NotNull
    private static BiConsumer<Function<String, String>, StrategyConfig.Builder> getStrategyConfig() {
        String tytPrefixRegex = "(.*)(?i)tyt(.*)";
        return (scanner, builder) ->
                builder.addInclude(getTables(scanner.apply("请输入表名，多个英文逗号分隔？所有输入all")))
                        .entityBuilder()
                        .enableLombok()
                        .disableSerialVersionUID()
                        .convertFileName(it -> RegExUtils.replaceFirst(it,tytPrefixRegex , "$1$2DO"))

                        .serviceBuilder()
                        .convertServiceFileName(it -> RegExUtils.replaceFirst(it, tytPrefixRegex, "$1$2Service"))
                        .convertServiceImplFileName(it -> RegExUtils.replaceFirst(it, tytPrefixRegex, "$1$2ServiceImpl"))

                        .mapperBuilder()
                        .mapperAnnotation(org.apache.ibatis.annotations.Mapper.class)
                        .enableBaseResultMap()
                        .enableBaseColumnList()
                        .formatMapperFileName("%sDao")
                        .formatXmlFileName("%sXml")
                        .convertMapperFileName(it -> RegExUtils.replaceFirst(it, tytPrefixRegex, "$1$2Mapper"))
                        .convertXmlFileName(it -> RegExUtils.replaceFirst(it, tytPrefixRegex, "$1$2Mapper"))


                        .build();
    }

    // 处理 all 情况
    protected static List<String> getTables(String tables) {
        return "all".equals(tables) ? Collections.emptyList() : Arrays.asList(tables.split(","));
    }
}