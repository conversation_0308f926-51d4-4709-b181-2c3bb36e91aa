package com.teyuntong.goods.search.client.transport.service;

import com.teyuntong.goods.search.client.transport.vo.BenefitLabelVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2025/05/16 17:06
 */

public interface TransportLabelRpcService {

    @GetMapping(value = "/rpc/transport/label/getTransportBenefitLabel")
    BenefitLabelVO getTransportBenefitLabel(@RequestParam("srcMsgId") Long srcMsgId);



}
