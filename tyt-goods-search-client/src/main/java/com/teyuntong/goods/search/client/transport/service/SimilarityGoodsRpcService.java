package com.teyuntong.goods.search.client.transport.service;

import com.teyuntong.goods.search.client.transport.dto.SimilarityGoodsDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface SimilarityGoodsRpcService {

    /**
     * 判断是否有相似货源且处于相似货源首位（实时）
     * @param srcMsgId srcMsgId
     * @param similarityCode similarityCode
     * @return 是否
     */
    @GetMapping(value = "/rpc/similarityGoods/similarityGoodsIsTop")
    Boolean similarityGoodsIsTop(@RequestParam("srcMsgId") Long srcMsgId, @RequestParam("similarityCode") String similarityCode);

    /**
     * 查询货源是否位于首位
     *
     * @param srcMsgId
     * @return
     */
    @GetMapping(value = "/rpc/similarityGoods/goodsIsTop")
    Boolean goodsIsTop(@RequestParam("srcMsgId") Long srcMsgId, @RequestParam("similarityCode") String similarityCode);

    /**
     * 批量查询货源是否位于首位
     * 查询不到或查询异常时，返回null，注意判空
     *
     * @param dtoList
     * @return
     */
    @PostMapping(value = "/rpc/similarityGoods/batchSelectGoodsIsTop")
    Map<Long, Optional<Boolean>> batchSelectGoodsIsTop(@RequestBody List<SimilarityGoodsDTO> dtoList);
}
