package com.teyuntong.goods.search.client.transport.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/05/15 17:46
 */
@Getter
@AllArgsConstructor
public enum BenefitLabelEnum {

    HIGH_PRICE(1, "30天同路线高价"),

    NO_TEC_FEE(2, "技术服务费闪降至0元"),

    HIGH_HEAT(3, "关注度高的热门货"),

    NEW_GOODS(4, "新货上架，先到先得"),

    ;

    private final Integer code;
    private final String label;

    public static BenefitLabelEnum getBenefitLabelEnum(Integer code) {
        for (BenefitLabelEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

}
